# 基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/inksdev/openjdk:8-jre
# author
MAINTAINER inks

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>
# 指定路径
WORKDIR /home/<USER>

ENV PARAMS="--server.port=8080 --spring.profiles.active=prod"
RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

# 复制jar文件到路径
COPY ./target/*.jar /home/<USER>/app.jar
EXPOSE 8080
# 启动文件服务
ENTRYPOINT ["/bin/sh","-c","java -Dfile.encoding=utf8 -Djava.security.egd=file:/dev/./urandom -jar app.jar ${PARAMS}"]