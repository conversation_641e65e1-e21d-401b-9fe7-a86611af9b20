<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MinIO 视频云平台</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary: #4361ee;
      --primary-light: #5773f5;
      --secondary: #3a0ca3;
      --accent: #4cc9f0;
      --success: #2ec4b6;
      --warning: #ff9f1c;
      --danger: #e63946;
      --dark: #1a1c2c;
      --light: #f8f9fa;
      --border-radius: 12px;
      --shadow: 0 10px 30px rgba(0,0,0,0.1);
      --shadow-hover: 0 15px 40px rgba(0,0,0,0.15);
      --gradient: linear-gradient(135deg, var(--primary), var(--secondary));
    }
    
    body {
      font-family: 'Nunito', sans-serif;
      background-color: #f0f5ff;
      color: #333;
      min-height: 100vh;
      padding: 40px 0;
    }
    
    .app-container {
      max-width: 1100px;
      margin: 0 auto;
      padding: 0 15px;
    }
    
    .page-header {
      text-align: center;
      margin-bottom: 2.5rem;
    }
    
    .page-title {
      font-weight: 700;
      font-size: 2.5rem;
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 0.5rem;
    }
    
    .page-description {
      color: #6c757d;
      font-weight: 300;
      font-size: 1.1rem;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .card {
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      transition: all 0.3s ease;
      overflow: hidden;
      margin-bottom: 2rem;
      background-color: white;
    }
    
    .card:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-hover);
    }
    
    .card-header {
      background-color: white;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      padding: 1.5rem;
    }
    
    .card-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      margin: 0;
      font-size: 1.25rem;
    }
    
    .card-title i {
      font-size: 1.5rem;
      margin-right: 0.75rem;
      color: var(--primary);
    }
    
    .card-body {
      padding: 1.5rem;
    }
    
    .drag-area {
      border: 2px dashed rgba(67,97,238,0.2);
      border-radius: var(--border-radius);
      padding: 3rem 2rem;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
      background-color: rgba(67,97,238,0.03);
    }
    
    .drag-area:hover, .drag-area.active {
      border-color: var(--primary);
      background-color: rgba(67,97,238,0.05);
    }
    
    .drag-area .icon {
      font-size: 4rem;
      color: var(--primary);
      margin-bottom: 1rem;
      opacity: 0.8;
      transition: all 0.3s ease;
    }
    
    .drag-area:hover .icon, .drag-area.active .icon {
      opacity: 1;
      transform: scale(1.1);
    }
    
    .drag-area .header {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--dark);
    }
    
    .drag-area .support {
      font-size: 0.875rem;
      color: #6c757d;
      margin-bottom: 1.5rem;
    }
    
    .drag-area input {
      display: none;
    }
    
    .btn {
      border-radius: 50px;
      padding: 0.6rem 1.8rem;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background: var(--gradient);
      border: none;
    }
    
    .btn-primary:hover, .btn-primary:focus {
      background: linear-gradient(135deg, var(--primary-light), var(--secondary));
      box-shadow: 0 5px 15px rgba(67,97,238,0.3);
    }
    
    .btn-outline-primary {
      color: var(--primary);
      border: 1px solid var(--primary);
      background: transparent;
    }
    
    .btn-outline-primary:hover {
      background-color: var(--primary);
      color: white;
    }
    
    .upload-info {
      margin-top: 1.5rem;
      border-radius: var(--border-radius);
      overflow: hidden;
      background-color: white;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    
    .file-details {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      background-color: rgba(0,0,0,0.02);
    }
    
    .file-name {
      font-weight: 600;
      max-width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .file-size {
      font-size: 0.875rem;
      color: #6c757d;
    }
    
    .progress {
      height: 0.5rem;
      border-radius: 0;
      margin: 0;
    }
    
    .progress-bar {
      background-color: var(--primary);
    }
    
    .upload-status {
      padding: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .status-text {
      font-size: 0.875rem;
      color: #6c757d;
    }
    
    .form-control {
      border-radius: 10px;
      padding: 0.75rem 1rem;
      border: 1px solid rgba(0,0,0,0.1);
      background-color: #f8f9fa;
      transition: all 0.3s ease;
    }
    
    .form-control:focus {
      box-shadow: 0 0 0 3px rgba(67,97,238,0.1);
      border-color: var(--primary);
    }
    
    .form-label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--dark);
    }
    
    .player-container {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--shadow);
    }
    
    video {
      width: 100%;
      border-radius: var(--border-radius);
      background-color: #000;
    }
    
    .video-info {
      background-color: rgba(67,97,238,0.05);
      border-radius: var(--border-radius);
      padding: 1.5rem;
      margin-top: 1.5rem;
    }
    
    .video-info-item {
      margin-bottom: 1rem;
    }
    
    .video-info-label {
      font-weight: 600;
      margin-bottom: 0.25rem;
      color: var(--dark);
    }
    
    .video-info-value {
      background-color: white;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9rem;
      overflow-x: auto;
      border: 1px solid rgba(0,0,0,0.05);
    }
    
    .copy-btn {
      background-color: var(--light);
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .copy-btn:hover {
      background-color: var(--primary);
      color: white;
    }
    
    .alert {
      border-radius: var(--border-radius);
      padding: 1rem 1.5rem;
      margin-bottom: 1.5rem;
      border: none;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    
    .alert-success {
      background-color: rgba(46,196,182,0.1);
      color: var(--success);
    }
    
    .alert-danger {
      background-color: rgba(230,57,70,0.1);
      color: var(--danger);
    }
    
    .tab-container {
      overflow: hidden;
      border-radius: var(--border-radius);
      margin-bottom: 2rem;
    }
    
    .tabs {
      display: flex;
      background-color: white;
      border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .tab {
      padding: 1.25rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      opacity: 0.7;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    }
    
    .tab.active {
      opacity: 1;
      border-bottom-color: var(--primary);
      color: var(--primary);
    }
    
    .tab-content {
      display: none;
      padding: 2rem;
      background-color: white;
    }
    
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
<div class="app-container">
  <div class="page-header">
    <h1 class="page-title">MinIO 视频云平台</h1>
    <p class="page-description">上传、管理和流式播放您的视频文件</p>
  </div>
  
  <div class="tab-container card">
    <div class="tabs">
      <div class="tab active" onclick="switchTab('upload')">上传视频</div>
      <div class="tab" onclick="switchTab('player')">视频播放器</div>
    </div>
    
    <div id="upload-tab" class="tab-content active">
      <div class="drag-area" id="drag-area">
        <div class="icon"><i class="bi bi-cloud-arrow-up"></i></div>
        <h3 class="header">拖放您的视频</h3>
        <p class="support">支持 MP4, WebM, MOV 等格式</p>
        <button class="btn btn-primary">
          <i class="bi bi-file-earmark-play"></i> 选择视频
        </button>
        <input type="file" id="file-input" accept="video/*">
      </div>
      
      <div id="upload-info" class="upload-info" style="display: none;">
        <div class="file-details">
          <div class="file-name" id="file-name"></div>
          <div class="file-size" id="file-size"></div>
        </div>
        <div class="progress">
          <div class="progress-bar" id="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
        <div class="upload-status">
          <div class="status-text" id="status-text">准备上传...</div>
          <button class="btn btn-outline-primary btn-sm" id="cancel-btn">
            取消
          </button>
        </div>
      </div>
      
      <div id="upload-result" class="video-info" style="display: none;">
        <div class="alert alert-success mb-4">
          <i class="bi bi-check-circle-fill me-2"></i> 视频上传成功！
        </div>
        
        <div class="video-info-item">
          <div class="video-info-label">Bucket:</div>
          <div class="d-flex align-items-center">
            <div class="video-info-value flex-grow-1" id="result-bucket"></div>
            <button class="copy-btn ms-2" onclick="copyText('result-bucket')">
              <i class="bi bi-clipboard"></i>
            </button>
          </div>
        </div>
        
        <div class="video-info-item">
          <div class="video-info-label">对象名（含目录）:</div>
          <div class="d-flex align-items-center">
            <div class="video-info-value flex-grow-1" id="result-object"></div>
            <button class="copy-btn ms-2" onclick="copyText('result-object')">
              <i class="bi bi-clipboard"></i>
            </button>
          </div>
        </div>
        
        <div class="d-flex justify-content-between mt-4">
          <button class="btn btn-outline-primary" onclick="resetUpload()">
            <i class="bi bi-arrow-repeat"></i> 上传新视频
          </button>
          <button class="btn btn-primary" onclick="goToPlayer()">
            <i class="bi bi-play-circle"></i> 前往播放器
          </button>
        </div>
      </div>
    </div>
    
    <div id="player-tab" class="tab-content">
      <div class="row g-4">
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="bucket" class="form-label">Bucket:</label>
            <input type="text" class="form-control" id="bucket" placeholder="例如: inkscommon" value="inkscommon">
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="object" class="form-label">对象名（含目录）:</label>
            <input type="text" class="form-control" id="object" placeholder="例如: videos/2025/05/28/video.mp4">
          </div>
        </div>
      </div>
      
      <div class="d-grid">
        <button class="btn btn-primary mt-2 mb-4" onclick="play()">
          <i class="bi bi-play-fill"></i> 加载并播放
        </button>
      </div>
      
      <div class="player-container">
        <video id="player" controls></video>
      </div>
    </div>
  </div>
</div>

<script>
  // 全局变量
  let currentFile = null;
  let uploadCancelled = false;
  const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB 分片大小
  const API_SERVER_PREFIX = 'http://*************:8081';
  
  // 初始化
  function init() {
    initDragArea();
    
    // 取消按钮事件
    document.getElementById('cancel-btn').addEventListener('click', cancelUpload);
    
    // 初始化标签页
    document.getElementById('upload-tab').classList.add('active');
    document.getElementById('player-tab').classList.remove('active');
  }
  
  // 初始化拖放区域
  function initDragArea() {
    const dragArea = document.getElementById('drag-area');
    const fileInput = document.getElementById('file-input');
    
    dragArea.addEventListener('click', () => {
      fileInput.click();
    });
    
    fileInput.addEventListener('change', () => {
      if (fileInput.files.length > 0) {
        handleSelectedFile(fileInput.files[0]);
      }
    });
    
    // 拖放事件
    dragArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      dragArea.classList.add('active');
    });
    
    dragArea.addEventListener('dragleave', () => {
      dragArea.classList.remove('active');
    });
    
    dragArea.addEventListener('drop', (e) => {
      e.preventDefault();
      dragArea.classList.remove('active');
      
      if (e.dataTransfer.files.length > 0) {
        handleSelectedFile(e.dataTransfer.files[0]);
      }
    });
  }
  
  // 处理选择的文件
  function handleSelectedFile(file) {
    if (!file || !file.type.startsWith('video/')) {
      showAlert('请选择有效的视频文件', 'danger');
      return;
    }
    
    currentFile = file;
    uploadCancelled = false;
    
    // 显示文件信息
    document.getElementById('file-name').textContent = file.name;
    document.getElementById('file-size').textContent = formatFileSize(file.size);
    document.getElementById('status-text').textContent = '准备上传...';
    document.getElementById('progress-bar').style.width = '0%';
    
    // 显示上传信息
    document.getElementById('upload-info').style.display = 'block';
    document.getElementById('upload-result').style.display = 'none';
    
    // 判断是否使用分片上传
    if (file.size > CHUNK_SIZE) {
      uploadLargeFile(file);
    } else {
      uploadFile(file);
    }
  }
  
  // 上传小文件
  function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const percent = Math.round((e.loaded / e.total) * 100);
        document.getElementById('progress-bar').style.width = percent + '%';
        document.getElementById('status-text').textContent = `上传中... ${percent}%`;
      }
    });
    
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          if (response.code === 200) {
            const data = response.data;
            showUploadSuccess(data);
          } else {
            showAlert('上传失败: ' + response.msg, 'danger');
          }
        } catch (e) {
          showAlert('解析响应失败', 'danger');
        }
      } else {
        showAlert('上传失败: 服务器返回 ' + xhr.status, 'danger');
      }
    });
    
    xhr.addEventListener('error', () => {
      showAlert('上传失败: 网络错误', 'danger');
    });
    
    xhr.open('POST', `${API_SERVER_PREFIX}/File/uploadByStream`);
    xhr.send(formData);
  }
  
  // 上传大文件（分片上传）
  function uploadLargeFile(file) {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    let uploadId = null;
    
    // 第一步：初始化分片上传
    const initUpload = () => {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${API_SERVER_PREFIX}/File/split/init`);
      xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.code === 200) {
              uploadId = response.data.uploadId;
              document.getElementById('status-text').textContent = `初始化完成，开始分片上传...`;
              uploadChunks(0);
            } else {
              showAlert('初始化上传失败: ' + response.msg, 'danger');
            }
          } catch (e) {
            showAlert('解析响应失败', 'danger');
          }
        } else {
          showAlert('初始化上传失败: 服务器返回 ' + xhr.status, 'danger');
        }
      };
      
      xhr.onerror = () => {
        showAlert('初始化上传失败: 网络错误', 'danger');
      };
      
      xhr.send(`fileName=${encodeURIComponent(file.name)}&fileSize=${file.size}&chunkSize=${CHUNK_SIZE}`);
    };
    
    // 第二步：上传分片
    const uploadChunks = (chunkIndex) => {
      if (uploadCancelled) {
        document.getElementById('status-text').textContent = '上传已取消';
        return;
      }
      
      if (chunkIndex >= totalChunks) {
        // 所有分片上传完成，进行合并
        completeUpload();
        return;
      }
      
      const start = chunkIndex * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);
      
      const formData = new FormData();
      formData.append('uploadId', uploadId);
      formData.append('chunkNumber', chunkIndex);
      formData.append('file', chunk);
      formData.append('chunkHash', `${uploadId}_chunk_${chunkIndex}`);
      
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const chunkPercent = Math.round((e.loaded / e.total) * 100);
          const totalPercent = Math.round(((chunkIndex + (e.loaded / e.total)) / totalChunks) * 100);
          document.getElementById('progress-bar').style.width = totalPercent + '%';
          document.getElementById('status-text').textContent = `上传分片 ${chunkIndex + 1}/${totalChunks}... ${totalPercent}%`;
        }
      });
      
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.code === 200) {
              // 上传下一个分片
              uploadChunks(chunkIndex + 1);
            } else {
              showAlert('上传分片失败: ' + response.msg, 'danger');
            }
          } catch (e) {
            showAlert('解析响应失败', 'danger');
          }
        } else {
          showAlert('上传分片失败: 服务器返回 ' + xhr.status, 'danger');
        }
      });
      
      xhr.addEventListener('error', () => {
        showAlert('上传分片失败: 网络错误', 'danger');
      });
      
      xhr.open('POST', `${API_SERVER_PREFIX}/File/split/chunk`);
      xhr.send(formData);
    };
    
    // 第三步：完成上传
    const completeUpload = () => {
      document.getElementById('status-text').textContent = '正在处理视频...';
      
      const xhr = new XMLHttpRequest();
      xhr.open('POST', `${API_SERVER_PREFIX}/File/split/complete`);
      xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.code === 200) {
              // 开始轮询合并进度
              pollMergeProgress(uploadId);
            } else {
              showAlert('完成上传失败: ' + response.msg, 'danger');
            }
          } catch (e) {
            showAlert('解析响应失败', 'danger');
          }
        } else {
          showAlert('完成上传失败: 服务器返回 ' + xhr.status, 'danger');
        }
      };
      
      xhr.onerror = () => {
        showAlert('完成上传失败: 网络错误', 'danger');
      };
      
      xhr.send(`uploadId=${uploadId}`);
    };
    
    // 轮询合并进度
    const pollMergeProgress = (uploadId) => {
      if (uploadCancelled) {
        return;
      }
      
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `${API_SERVER_PREFIX}/File/split/progress?uploadId=${uploadId}`);
      
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.code === 200) {
              const data = response.data;
              const status = data.status;
              const percent = data.percent || 0;
              
              // 更新进度条和状态文本
              document.getElementById('progress-bar').style.width = percent + '%';
              document.getElementById('status-text').textContent = `处理视频中... ${percent}%`;
              
              // 如果合并完成，显示上传成功
              if (status === 'COMPLETED') {
                showUploadSuccess(data);
              } else if (status === 'FAILED') {
                showAlert('视频处理失败: ' + (data.errorMessage || '未知错误'), 'danger');
              } else {
                // 继续轮询，间隔1秒
                setTimeout(() => pollMergeProgress(uploadId), 1000);
              }
            } else {
              showAlert('获取进度失败: ' + response.msg, 'danger');
            }
          } catch (e) {
            showAlert('解析进度响应失败', 'danger');
          }
        } else {
          showAlert('获取进度失败: 服务器返回 ' + xhr.status, 'danger');
        }
      };
      
      xhr.onerror = () => {
        showAlert('获取进度失败: 网络错误', 'danger');
        // 网络错误时，稍后重试
        setTimeout(() => pollMergeProgress(uploadId), 2000);
      };
      
      xhr.send();
    };
    
    // 开始上传
    initUpload();
  }
  
  // 取消上传
  function cancelUpload() {
    uploadCancelled = true;
    document.getElementById('upload-info').style.display = 'none';
    showAlert('上传已取消', 'warning');
  }
  
  // 显示上传成功
  function showUploadSuccess(data) {
    document.getElementById('upload-info').style.display = 'none';
    document.getElementById('upload-result').style.display = 'block';
    
    document.getElementById('result-bucket').textContent = data.bucket || 'inkscommon';
    document.getElementById('result-object').textContent = data.objectName || '';
    
    // 自动填充播放器表单
    document.getElementById('object').value = data.objectName || '';
    document.getElementById('bucket').value = data.bucket || 'inkscommon';
  }
  
  // 复制文本
  function copyText(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
      showAlert('已复制到剪贴板', 'success');
    }).catch(() => {
      showAlert('复制失败', 'danger');
    });
  }
  
  // 重置上传
  function resetUpload() {
    document.getElementById('upload-result').style.display = 'none';
    document.getElementById('file-input').value = '';
    currentFile = null;
  }
  
  // 切换到播放器
  function goToPlayer() {
    switchTab('player');
  }
  
  // 切换标签页
  function switchTab(tabId) {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
      tab.classList.remove('active');
      if (tab.textContent.includes(tabId === 'upload' ? '上传' : '播放')) {
        tab.classList.add('active');
      }
    });
    
    tabContents.forEach(content => {
      content.classList.remove('active');
    });
    
    document.getElementById(tabId + '-tab').classList.add('active');
  }
  
  // 显示警告信息
  function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}-fill me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.app-container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动关闭
    setTimeout(() => {
      alertDiv.classList.remove('show');
      setTimeout(() => {
        alertDiv.remove();
      }, 150);
    }, 3000);
  }
  
  // 格式化文件大小
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  function play(){
    const bucket = document.getElementById('bucket').value.trim();
    const object = document.getElementById('object').value.trim();
    if(!bucket || !object){ 
      showAlert('请填写 bucket 和对象名', 'danger');
      return; 
    }

    // 使用新的路径格式访问流
    const url = `${API_SERVER_PREFIX}/File/split/stream/${object}`;


    const video = document.getElementById('player');
    video.onerror = () => showAlert('加载失败，请检查输入或后端服务', 'danger');
    video.src = url;
    video.load(); 
    video.play();
  }
  
  // 初始化应用
  window.addEventListener('DOMContentLoaded', init);
</script>
</body>
</html>
