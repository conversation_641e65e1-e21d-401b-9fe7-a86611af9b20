<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinIO 视频云平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #5773f5;
            --secondary: #3a0ca3;
            --accent: #4cc9f0;
            --success: #2ec4b6;
            --warning: #ff9f1c;
            --danger: #e63946;
            --dark: #1a1c2c;
            --light: #f8f9fa;
            --border-radius: 12px;
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --shadow-hover: 0 15px 40px rgba(0,0,0,0.15);
            --gradient: linear-gradient(135deg, var(--primary), var(--secondary));
        }

        body {
            font-family: 'Nunito', sans-serif;
            background-color: #f0f5ff;
            color: #333;
            min-height: 100vh;
            padding: 40px 0;
        }

        .app-container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .page-title {
            font-weight: 700;
            font-size: 2.5rem;
            background: var(--gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .page-description {
            color: #6c757d;
            font-weight: 300;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: 2rem;
            background-color: white;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }

        .card-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            margin: 0;
            font-size: 1.25rem;
        }

        .card-title i {
            font-size: 1.5rem;
            margin-right: 0.75rem;
            color: var(--primary);
        }

        .card-body {
            padding: 1.5rem;
        }

        .drag-area {
            border: 2px dashed rgba(67,97,238,0.2);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background-color: rgba(67,97,238,0.03);
        }

        .drag-area:hover, .drag-area.active {
            border-color: var(--primary);
            background-color: rgba(67,97,238,0.05);
        }

        .drag-area .icon {
            font-size: 4rem;
            color: var(--primary);
            margin-bottom: 1rem;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .drag-area:hover .icon, .drag-area.active .icon {
            opacity: 1;
            transform: scale(1.1);
        }

        .drag-area .header {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .drag-area .support {
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 1.5rem;
        }

        .drag-area input {
            display: none;
        }

        .btn {
            border-radius: 50px;
            padding: 0.6rem 1.8rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--gradient);
            border: none;
        }

        .btn-primary:hover, .btn-primary:focus {
            background: linear-gradient(135deg, var(--primary-light), var(--secondary));
            box-shadow: 0 5px 15px rgba(67,97,238,0.3);
        }

        .btn-outline-primary {
            color: var(--primary);
            border: 1px solid var(--primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        .btn-outline-danger { /* Added for cancel button */
            color: var(--danger);
            border: 1px solid var(--danger);
            background: transparent;
        }
        .btn-outline-danger:hover {
            background-color: var(--danger);
            color: white;
        }

        .upload-info {
            margin-top: 1.5rem;
            border-radius: var(--border-radius);
            overflow: hidden;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .file-details {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background-color: rgba(0,0,0,0.02);
        }

        .file-name {
            font-weight: 600;
            max-width: 70%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-size {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .progress {
            height: 1rem; /* Slightly thicker progress bar */
            border-radius: 0;
            margin: 0;
            font-size: 0.75rem; /* For text on progress bar */
        }

        .progress-bar {
            background-color: var(--primary);
            color: white; /* Text color on progress bar */
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .progress-bar.bg-success {
            background-color: var(--success) !important;
        }
        .progress-bar.bg-danger {
            background-color: var(--danger) !important;
        }

        .upload-status {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-text {
            font-size: 0.875rem;
            color: #555; /* Slightly darker text for status */
            font-weight: 500;
        }
        .status-text .badge { margin-left: 8px; font-size: 0.8em; padding: 0.3em 0.6em;}

        .form-control {
            border-radius: 10px;
            padding: 0.75rem 1rem;
            border: 1px solid rgba(0,0,0,0.1);
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(67,97,238,0.1);
            border-color: var(--primary);
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark);
        }

        .player-container {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        video {
            width: 100%;
            border-radius: var(--border-radius);
            background-color: #000;
        }

        .video-info {
            background-color: rgba(67,97,238,0.05);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .video-info-item {
            margin-bottom: 1rem;
        }

        .video-info-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--dark);
        }

        .video-info-value {
            background-color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .copy-btn {
            background-color: var(--light);
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .copy-btn:hover {
            background-color: var(--primary);
            color: white;
        }

        .alert {
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .alert-success {
            background-color: rgba(46,196,182,0.1);
            color: var(--success);
        }

        .alert-danger {
            background-color: rgba(230,57,70,0.1);
            color: var(--danger);
        }

        .tab-container {
            overflow: hidden;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
        }

        .tabs {
            display: flex;
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .tab {
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab.active {
            opacity: 1;
            border-bottom-color: var(--primary);
            color: var(--primary);
        }

        .tab-content {
            display: none;
            padding: 2rem;
            background-color: white;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
<div class="app-container">
    <div class="page-header">
        <h1 class="page-title">MinIO 视频云平台</h1>
        <p class="page-description">上传、管理和流式播放您的视频文件</p>
    </div>

    <div class="tab-container card">
        <div class="tabs">
            <div class="tab active" onclick="switchTab('upload')">上传视频</div>
            <div class="tab" onclick="switchTab('player')">视频播放器</div>
        </div>

        <div id="upload-tab" class="tab-content active">
            <div class="drag-area" id="drag-area">
                <div class="icon"><i class="bi bi-cloud-arrow-up"></i></div>
                <h3 class="header">拖放您的视频</h3>
                <p class="support">支持 MP4, WebM, MOV 等格式</p>
                <button class="btn btn-primary">
                    <i class="bi bi-file-earmark-play"></i> 选择视频
                </button>
                <input type="file" id="file-input" accept="video/*">
            </div>

            <div id="upload-info" class="upload-info" style="display: none;">
                <div class="file-details">
                    <div class="file-name" id="file-name"></div>
                    <div class="file-size" id="file-size"></div>
                </div>
                <div class="progress">
                    <div class="progress-bar" id="progress-bar" role="progressbar" style="width: 0%">0%</div>
                </div>
                <div class="upload-status">
                    <div class="status-text" id="status-text">准备上传...</div>
                    <button class="btn btn-outline-danger btn-sm" id="cancel-btn"> <i class="bi bi-x-circle"></i> 取消
                    </button>
                </div>
            </div>

            <div id="upload-result" class="video-info" style="display: none;">
                <div class="alert alert-success mb-4" id="upload-success-alert">
                    <i class="bi bi-check-circle-fill me-2"></i> 视频上传成功！
                </div>
                <div class="alert alert-danger mb-4" id="upload-error-alert" style="display:none;">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i> <span id="upload-error-message">上传失败</span>
                </div>

                <div class="video-info-item">
                    <div class="video-info-label">原始文件名:</div>
                    <div class="d-flex align-items-center">
                        <div class="video-info-value flex-grow-1" id="result-original-filename"></div>
                    </div>
                </div>

                <div class="video-info-item">
                    <div class="video-info-label">Bucket:</div>
                    <div class="d-flex align-items-center">
                        <div class="video-info-value flex-grow-1" id="result-bucket"></div>
                        <button class="copy-btn ms-2" onclick="copyText('result-bucket')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>

                <div class="video-info-item">
                    <div class="video-info-label">对象名（含目录）:</div>
                    <div class="d-flex align-items-center">
                        <div class="video-info-value flex-grow-1" id="result-object"></div>
                        <button class="copy-btn ms-2" onclick="copyText('result-object')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
                <div class="video-info-item" id="result-url-container" style="display:none;">
                    <div class="video-info-label">播放链接 (7天有效):</div>
                    <div class="d-flex align-items-center">
                        <div class="video-info-value flex-grow-1" id="result-url" style="white-space: normal; word-break: break-all;"></div>
                        <button class="copy-btn ms-2" onclick="copyText('result-url')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-primary" onclick="resetUpload()">
                        <i class="bi bi-arrow-repeat"></i> 上传新视频
                    </button>
                    <button class="btn btn-primary" onclick="goToPlayer()" id="go-to-player-btn" style="display:none;">
                        <i class="bi bi-play-circle"></i> 前往播放器
                    </button>
                </div>
            </div>
        </div>

        <div id="player-tab" class="tab-content">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="bucket" class="form-label">Bucket:</label>
                        <input type="text" class="form-control" id="bucket" placeholder="例如: inkscommon" value="inkscommon">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="object" class="form-label">对象名（含目录）:</label>
                        <input type="text" class="form-control" id="object" placeholder="例如: videos/2025/05/28/video.mp4">
                    </div>
                </div>
            </div>

            <div class="d-grid">
                <button class="btn btn-primary mt-2 mb-4" onclick="play()">
                    <i class="bi bi-play-fill"></i> 加载并播放
                </button>
            </div>

            <div class="player-container">
                <video id="player" controls></video>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/spark-md5/3.0.2/spark-md5.min.js"></script>
<script>
    // 全局变量
    let currentFile = null;
    let uploadCancelled = false; // 前端取消标志
    let currentUploadId = null;
    let mergeProgressInterval = null; // 用于轮询合并进度的定时器

    const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB 分片大小
    const API_SERVER_PREFIX = 'http://*************:8081'; // 请替换为您的API服务器地址

    // 初始化
    function init() {
        initDragArea();
        document.getElementById('cancel-btn').addEventListener('click', cancelCurrentUpload);

        // 初始化标签页 (确保首次加载时正确显示)
        document.getElementById('upload-tab').classList.add('active');
        document.getElementById('player-tab').classList.remove('active');
        document.querySelector('.tabs .tab[onclick*="upload"]').classList.add('active');
        document.querySelector('.tabs .tab[onclick*="player"]').classList.remove('active');
    }

    // 初始化拖放区域
    function initDragArea() {
        const dragArea = document.getElementById('drag-area');
        const fileInput = document.getElementById('file-input');

        dragArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => e.target.files.length && handleSelectedFile(e.target.files[0]));

        ['dragover', 'dragleave', 'drop'].forEach(eventName => {
            dragArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (eventName === 'dragover') dragArea.classList.add('active');
                else if (eventName === 'dragleave') dragArea.classList.remove('active');
                else if (eventName === 'drop') {
                    dragArea.classList.remove('active');
                    e.dataTransfer.files.length && handleSelectedFile(e.dataTransfer.files[0]);
                }
            });
        });
    }

    function handleSelectedFile(file) {
        if (!file || !file.type.startsWith('video/')) {
            showAlert('请选择有效的视频文件 (例如 MP4, WebM)', 'danger');
            return;
        }

        resetUploadState();
        currentFile = file;

        document.getElementById('file-name').textContent = file.name;
        document.getElementById('file-size').textContent = formatFileSize(file.size);
        setUploadStatus('准备上传...', 0);

        document.getElementById('upload-info').style.display = 'block';
        document.getElementById('upload-result').style.display = 'none';
        document.getElementById('cancel-btn').disabled = false;

        uploadLargeFile(file);
    }

    function resetUploadState() {
        currentFile = null;
        uploadCancelled = false;
        currentUploadId = null;
        if (mergeProgressInterval) {
            clearInterval(mergeProgressInterval);
            mergeProgressInterval = null;
        }
        document.getElementById('file-input').value = '';
        document.getElementById('upload-info').style.display = 'none';
        document.getElementById('upload-result').style.display = 'none';
        document.getElementById('upload-error-alert').style.display = 'none';
        document.getElementById('upload-success-alert').style.display = 'block';
        document.getElementById('go-to-player-btn').style.display = 'none';
        document.getElementById('result-url-container').style.display = 'none';
        setUploadStatus("准备上传...", 0);
        document.getElementById('cancel-btn').disabled = true; // Disable cancel initially
    }


    async function uploadLargeFile(file) {
        let totalChunks;
        let uploadedChunksStatus = [];
        const fileIdentifier = `uploadState_${file.name}_${file.size}_${file.lastModified}`;
        const storedUploadId = localStorage.getItem(`uploadId_${fileIdentifier}`);

        const initUpload = () => {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${API_SERVER_PREFIX}/File/initMultipartUpload`);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.code === 200 && response.data) {
                                currentUploadId = response.data.uploadId;
                                totalChunks = response.data.totalChunks;
                                // 后端返回的 uploadedChunks 优先，否则尝试从localStorage恢复
                                const backendUploadedChunks = response.data.uploadedChunks;
                                const localUploadedChunks = JSON.parse(localStorage.getItem(fileIdentifier));

                                if (response.data.resumed && backendUploadedChunks) {
                                    uploadedChunksStatus = backendUploadedChunks;
                                    showAlert('已恢复未完成的上传任务。', 'info');
                                } else if (localUploadedChunks && localUploadedChunks.length === totalChunks && storedUploadId === currentUploadId) {
                                    uploadedChunksStatus = localUploadedChunks;
                                    showAlert('已从浏览器缓存恢复上传进度。', 'info');
                                } else {
                                    uploadedChunksStatus = new Array(totalChunks).fill(false);
                                }

                                localStorage.setItem(`uploadId_${fileIdentifier}`, currentUploadId);
                                setUploadStatus(`初始化完成，准备上传分片...`, 0);
                                resolve(response.data);
                            } else {
                                showAlert(`初始化上传失败: ${response.msg || '未知错误'}`, 'danger');
                                reject(new Error(response.msg));
                            }
                        } catch (e) {
                            showAlert('解析初始化响应失败', 'danger');
                            reject(e);
                        }
                    } else {
                        showAlert(`初始化上传失败: 服务器返回 ${xhr.status}`, 'danger');
                        reject(new Error(`服务器错误 ${xhr.status}`));
                    }
                };
                xhr.onerror = () => {
                    showAlert('初始化上传失败: 网络错误', 'danger');
                    reject(new Error('网络错误'));
                };
                let params = `fileName=${encodeURIComponent(file.name)}&fileSize=${file.size}&chunkSize=${CHUNK_SIZE}`;
                if (storedUploadId) {
                    params += `&uploadId=${storedUploadId}`;
                }
                xhr.send(params);
            });
        };

        const uploadChunkAsync = (chunkIndex, chunkFile, attempt = 1) => {
            return new Promise(async (resolve, reject) => {
                if (uploadCancelled) {
                    setUploadStatus('上传已取消', parseFloat(document.getElementById('progress-bar').style.width));
                    return reject(new Error('上传已取消'));
                }
                
                // 如果分片已经标记为上传过，但正在重试上传，则在界面上显示
                if (uploadedChunksStatus[chunkIndex] && attempt > 1) {
                    console.log(`分片 ${chunkIndex} 已标记为上传成功，但正在重试上传 (尝试 ${attempt}/3)`);
                }

                const formData = new FormData();
                formData.append('uploadId', currentUploadId);
                formData.append('chunkNumber', chunkIndex);
                formData.append('file', chunkFile, `chunk_${chunkIndex}`);

                // 计算分片的MD5哈希值用于校验
                const spark = new SparkMD5.ArrayBuffer();
                try {
                    const buffer = await chunkFile.arrayBuffer();
                    spark.append(buffer);
                    console.log(`分片 ${chunkIndex} 读取成功，大小: ${buffer.byteLength} 字节`);
                } catch (readError) {
                    console.error(`读取分片 ${chunkIndex} 内容失败:`, readError);
                    showAlert(`读取分片 ${chunkIndex} 内容失败: ${readError.message}`, 'danger');
                    return reject(readError);
                }
                const chunkHash = spark.end();
                formData.append('chunkHash', chunkHash);
                console.log(`分片 ${chunkIndex} 哈希: ${chunkHash}`);

                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${API_SERVER_PREFIX}/File/uploadChunk`);

                xhr.upload.onprogress = (e) => {
                    if (e.lengthComputable) {
                        const chunkPercent = Math.round((e.loaded / e.total) * 100);
                        
                        // 计算已完成分片的总字节数
                        let completedBytes = 0;
                        for(let i = 0; i < totalChunks; i++) {
                            if(uploadedChunksStatus[i] && i !== chunkIndex) {
                                completedBytes += (i < totalChunks - 1) ? CHUNK_SIZE : (file.size % CHUNK_SIZE || CHUNK_SIZE);
                            }
                        }
                        
                        // 添加当前分片的已上传字节
                        const currentChunkBytes = e.loaded;
                        const totalBytes = file.size;
                        const overallPercent = Math.min(Math.round(((completedBytes + currentChunkBytes) / totalBytes) * 100), 99);
                        
                        setUploadStatus(`上传分片 ${chunkIndex + 1}/${totalChunks}... ${chunkPercent}%`, overallPercent);
                    }
                };

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.code === 200) {
                                // 标记分片为已上传
                                uploadedChunksStatus[chunkIndex] = true;
                                localStorage.setItem(fileIdentifier, JSON.stringify(uploadedChunksStatus));
                                
                                // 更新显示的进度
                                const uploadedCount = uploadedChunksStatus.filter(status => status).length;
                                const percentComplete = Math.min(Math.round((uploadedCount / totalChunks) * 100), 99);
                                
                                console.log(`分片 ${chunkIndex} 上传成功: ${response.data?.message || 'OK'}, 已完成: ${uploadedCount}/${totalChunks}`);
                                setUploadStatus(`分片 ${chunkIndex + 1}/${totalChunks} 成功，已完成: ${uploadedCount}/${totalChunks}`, percentComplete);
                                
                                resolve(response.data);
                            } else {
                                console.error(`分片 ${chunkIndex} 上传失败，服务器响应:`, response);
                                
                                if (attempt < 3) { 
                                    const retryMsg = `分片 ${chunkIndex + 1} 上传失败 (${response.msg||'服务器错误'}), 自动重试 (${attempt}/3)...`;
                                    console.warn(retryMsg);
                                    showAlert(retryMsg, 'warning');
                                    setTimeout(() => uploadChunkAsync(chunkIndex, chunkFile, attempt + 1).then(resolve).catch(reject), 1000 * attempt);
                                } else {
                                    uploadedChunksStatus[chunkIndex] = false; // 确保标记为未上传
                                    localStorage.setItem(fileIdentifier, JSON.stringify(uploadedChunksStatus));
                                    showAlert(`上传分片 ${chunkIndex + 1} 失败: ${response.msg || '未知错误'}`, 'danger');
                                    reject(new Error(response.msg || '未知错误'));
                                }
                            }
                        } catch (e) {
                            console.error(`解析分片 ${chunkIndex} 响应失败:`, e, xhr.responseText);
                            showAlert(`解析分片 ${chunkIndex + 1} 响应失败`, 'danger');
                            reject(e);
                        }
                    } else {
                        console.error(`分片 ${chunkIndex} 上传HTTP错误:`, xhr.status, xhr.statusText);
                        showAlert(`上传分片 ${chunkIndex + 1} 失败: 服务器返回 ${xhr.status}`, 'danger');
                        reject(new Error(`服务器错误 ${xhr.status}`));
                    }
                };
                xhr.onerror = () => {
                    console.error(`分片 ${chunkIndex} 上传网络错误`);
                    if (attempt < 3) {
                        const retryMsg = `分片 ${chunkIndex + 1} 网络错误，自动重试 (${attempt}/3)...`;
                        console.warn(retryMsg);
                        showAlert(retryMsg, 'warning');
                        setTimeout(() => uploadChunkAsync(chunkIndex, chunkFile, attempt + 1).then(resolve).catch(reject), 1000 * attempt);
                    } else {
                        uploadedChunksStatus[chunkIndex] = false; // 确保标记为未上传
                        localStorage.setItem(fileIdentifier, JSON.stringify(uploadedChunksStatus));
                        showAlert(`上传分片 ${chunkIndex + 1} 失败: 网络错误 (已达最大重试次数)`, 'danger');
                        reject(new Error('网络错误'));
                    }
                };
                xhr.send(formData);
                console.log(`开始上传分片 ${chunkIndex}...`);
            });
        };

        const completeUpload = () => {
            return new Promise((resolve, reject) => {
                if (uploadCancelled) {
                    setUploadStatus('合并已取消', parseFloat(document.getElementById('progress-bar').style.width));
                    return reject(new Error('合并已取消'));
                }
                
                // 再次验证所有分片是否已上传
                const missingChunks = [];
                for (let i = 0; i < uploadedChunksStatus.length; i++) {
                    if (!uploadedChunksStatus[i]) {
                        missingChunks.push(i);
                    }
                }
                
                if (missingChunks.length > 0) {
                    const errorMsg = `存在未完成的分片 ${missingChunks.join(', ')}，无法合并文件`;
                    showAlert(errorMsg, 'danger');
                    return reject(new Error(errorMsg));
                }
                
                setUploadStatus('所有分片上传完毕，请求服务器合并...', 99, true);

                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${API_SERVER_PREFIX}/File/completeMultipartUpload`);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.code === 200 && response.data && response.data.uploadId) {
                                startMergeProgressPolling(response.data.uploadId);
                                resolve();
                            } else {
                                showAlert(`请求合并失败: ${response.msg || '未知错误'}`, 'danger');
                                reject(new Error(response.msg));
                            }
                        } catch (e) {
                            showAlert('解析合并请求响应失败', 'danger');
                            reject(e);
                        }
                    } else {
                        showAlert(`请求合并失败: 服务器返回 ${xhr.status}`, 'danger');
                        reject(new Error(`服务器错误 ${xhr.status}`));
                    }
                };
                xhr.onerror = () => {
                    showAlert('请求合并失败: 网络错误', 'danger');
                    reject(new Error('网络错误'));
                };
                xhr.send(`uploadId=${currentUploadId}`);
            });
        };

        try {
            await initUpload();
            if (uploadCancelled) return;

            let initialUploadedCount = uploadedChunksStatus.filter(s => s).length;
            if (initialUploadedCount > 0 && initialUploadedCount < totalChunks) {
                let initialProgress = Math.min(Math.round((initialUploadedCount * CHUNK_SIZE / file.size) * 100), 99);
                setUploadStatus(`已恢复 ${initialUploadedCount}/${totalChunks} 分片，继续上传...`, initialProgress);
            }


            for (let i = 0; i < totalChunks; i++) {
                if (uploadCancelled) throw new Error("上传已取消 (分片循环中)");
                if (uploadedChunksStatus[i]) {
                    console.log(`分片 ${i} 已上传，跳过.`);
                    const percentForThisChunk = Math.min(Math.round(((i + 1) * CHUNK_SIZE / file.size) * 100), 99);
                    setUploadStatus(`分片 ${i + 1}/${totalChunks} 已跳过...`, percentForThisChunk);
                    continue;
                }
                const start = i * CHUNK_SIZE;
                const end = Math.min(start + CHUNK_SIZE, file.size);
                const chunk = file.slice(start, end);
                await uploadChunkAsync(i, chunk);
            }

            if (uploadCancelled) throw new Error("上传已取消 (准备合并前)");
            await completeUpload();

        } catch (error) {
            console.error('上传流程出错:', error);
            if (!uploadCancelled) {
                setUploadStatus(`上传失败: ${error.message}`, parseFloat(document.getElementById('progress-bar').style.width), false, true);
            }
            document.getElementById('cancel-btn').disabled = true;
        }
    }

    function startMergeProgressPolling(uid) {
        if (mergeProgressInterval) {
            clearInterval(mergeProgressInterval);
        }
        // Initial status after completeMultipartUpload is called
        setUploadStatus('服务器处理中 <span class="badge bg-secondary">排队中</span>', 99, true);


        mergeProgressInterval = setInterval(async () => {
            if (uploadCancelled && uid === currentUploadId) {
                clearInterval(mergeProgressInterval);
                mergeProgressInterval = null;
                setUploadStatus('合并已取消', 99, false, true);
                return;
            }
            try {
                const response = await fetch(`${API_SERVER_PREFIX}/File/getMergeProgress?uploadId=${uid}`);
                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.status}`);
                }
                const result = await response.json();
                if (result.code === 200 && result.data) {
                    const data = result.data;
                    let statusText = `服务器处理中 (${data.percent}%)`;
                    let progressBarAnimated = true;
                    let isError = false;
                    let currentPercent = data.percent;


                    let statusBadge = '';
                    switch(data.status) {
                        case "QUEUED": statusBadge = '<span class="badge bg-secondary">排队中</span>'; break;
                        case "CHECKING": statusBadge = '<span class="badge bg-info text-dark">检查分片</span>'; break;
                        case "MERGING": statusBadge = '<span class="badge bg-primary">合并分片</span>'; break;
                        case "UPLOADING_TO_MINIO": statusBadge = '<span class="badge bg-success">上传存储</span>'; break;
                        case "PREPARING_UPLOAD": statusBadge = '<span class="badge bg-warning text-dark">准备上传</span>'; break;
                        case "COMPLETED": statusBadge = '<span class="badge bg-success">已完成</span>'; break;
                        case "ERROR": statusBadge = '<span class="badge bg-danger">错误</span>'; break;
                        case "CANCELLED": statusBadge = '<span class="badge bg-warning text-dark">已取消</span>'; break;
                        default: statusBadge = `<span class="badge bg-light text-dark">${data.status}</span>`;
                    }
                    statusText = `服务器处理中 ${statusBadge} (${currentPercent}%)`;

                    if (data.status === 'COMPLETED') {
                        clearInterval(mergeProgressInterval);
                        mergeProgressInterval = null;
                        progressBarAnimated = false;
                        setUploadStatus('处理完成!', 100, false);
                        showUploadSuccess(data);
                        const fileIdentifier = `uploadState_${currentFile.name}_${currentFile.size}_${currentFile.lastModified}`;
                        localStorage.removeItem(`uploadId_${fileIdentifier}`);
                        localStorage.removeItem(fileIdentifier);
                        document.getElementById('cancel-btn').disabled = true;

                    } else if (data.status === 'ERROR' || data.cancelled || data.status === 'CANCELLED') {
                        clearInterval(mergeProgressInterval);
                        mergeProgressInterval = null;
                        progressBarAnimated = false;
                        isError = true;
                        statusText = `处理失败: ${statusBadge} ${data.errorMessage || (data.cancelled ? '用户取消' : '未知错误')}`;
                        setUploadStatus(statusText, currentPercent, false, true);
                        showUploadError(data.errorMessage || (data.cancelled ? '用户取消操作成功' : '发生未知错误'));
                        document.getElementById('cancel-btn').disabled = true;
                    }
                    setUploadStatus(statusText, currentPercent, progressBarAnimated, isError);
                } else {
                    setUploadStatus(`获取进度失败: ${result.msg || '未知错误'}`, parseFloat(document.getElementById('progress-bar').style.width), false, true);
                    showUploadError(`获取进度失败: ${result.msg || '未知错误'}`);
                    clearInterval(mergeProgressInterval);
                    mergeProgressInterval = null;
                }
            } catch (error) {
                console.error('轮询进度失败:', error);
                setUploadStatus(`轮询进度网络错误`, parseFloat(document.getElementById('progress-bar').style.width), false, true);
                showUploadError(`轮询进度网络错误: ${error.message}`);
                clearInterval(mergeProgressInterval);
                mergeProgressInterval = null;
            }
        }, 2000);
    }


    async function cancelCurrentUpload() {
        if (!currentUploadId && !currentFile) {
            showAlert('没有正在进行的上传任务。', 'info');
            resetUploadState();
            return;
        }
        uploadCancelled = true;
        document.getElementById('cancel-btn').disabled = true;

        if (mergeProgressInterval) {
            clearInterval(mergeProgressInterval);
            mergeProgressInterval = null;
        }
        setUploadStatus('正在取消上传...', parseFloat(document.getElementById('progress-bar').style.width));

        if (currentUploadId) {
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${API_SERVER_PREFIX}/File/cancelMultipartUpload`);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.onload = () => {
                    resetUploadState();
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.code === 200) {
                                showAlert('上传已取消。', 'warning');
                            } else {
                                showAlert(`取消失败: ${response.msg || '未知错误'}`, 'danger');
                            }
                        } catch (e) {
                            showAlert('取消请求响应解析失败。', 'danger');
                        }
                    } else {
                        showAlert(`取消请求失败: 服务器返回 ${xhr.status}`, 'danger');
                    }
                    cleanupLocalStorage();
                    currentUploadId = null;
                };
                xhr.onerror = () => {
                    resetUploadState();
                    showAlert('取消请求失败: 网络错误', 'danger');
                    cleanupLocalStorage();
                    currentUploadId = null;
                };
                xhr.send(`uploadId=${currentUploadId}`);
            } catch (error) {
                resetUploadState();
                showAlert(`取消操作时发生错误: ${error.message}`, 'danger');
                cleanupLocalStorage();
                currentUploadId = null;
            }
        } else {
            resetUploadState();
            showAlert('上传已取消 (初始化前)。', 'warning');
        }
    }

    function cleanupLocalStorage() {
        if(currentFile) {
            const fileIdentifier = `uploadState_${currentFile.name}_${currentFile.size}_${currentFile.lastModified}`;
            localStorage.removeItem(`uploadId_${fileIdentifier}`);
            localStorage.removeItem(fileIdentifier);
        }
    }

    function setUploadStatus(text, percent, animated = false, isError = false) {
        document.getElementById('status-text').innerHTML = text;
        const progressBar = document.getElementById('progress-bar');
        const displayPercent = Math.max(0, Math.min(100, Math.round(percent))); // Ensure percent is between 0 and 100
        progressBar.style.width = displayPercent + '%';
        progressBar.textContent = displayPercent + '%';

        if (animated) {
            progressBar.classList.add('progress-bar-striped', 'progress-bar-animated');
        } else {
            progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
        }

        progressBar.classList.remove('bg-success', 'bg-danger'); // Reset colors
        if(isError) {
            progressBar.classList.add('bg-danger');
        } else if (displayPercent === 100 && !animated) { // Fully completed and not just waiting for merge
            progressBar.classList.add('bg-success');
        } else {
            // Default primary color is set by .progress-bar, no need to add a class for it
        }
    }

    function showUploadSuccess(data) {
        document.getElementById('upload-info').style.display = 'none';
        document.getElementById('upload-result').style.display = 'block';
        document.getElementById('upload-error-alert').style.display = 'none';
        document.getElementById('upload-success-alert').style.display = 'block';

        document.getElementById('result-original-filename').textContent = data.originalFileName || currentFile.name; // Use data if available
        document.getElementById('result-bucket').textContent = data.bucket || 'inkscommon';
        document.getElementById('result-object').textContent = data.objectName || '';

        if (data.url) {
            document.getElementById('result-url').textContent = data.url;
            document.getElementById('result-url-container').style.display = 'block';
        } else {
            document.getElementById('result-url-container').style.display = 'none';
        }

        document.getElementById('object').value = data.objectName || '';
        document.getElementById('bucket').value = data.bucket || 'inkscommon';
        if (data.objectName) {
            document.getElementById('go-to-player-btn').style.display = 'inline-block';
        }
    }

    function showUploadError(message) {
        document.getElementById('upload-info').style.display = 'none';
        document.getElementById('upload-result').style.display = 'block';
        document.getElementById('upload-success-alert').style.display = 'none';
        document.getElementById('upload-error-alert').style.display = 'block';
        document.getElementById('upload-error-message').textContent = message;
        document.getElementById('go-to-player-btn').style.display = 'none';
        document.getElementById('result-url-container').style.display = 'none';
    }

    function copyText(elementId) {
        const textToCopy = document.getElementById(elementId).textContent;
        navigator.clipboard.writeText(textToCopy).then(() => {
            showAlert('已复制到剪贴板!', 'success');
        }).catch(err => {
            showAlert('复制失败: ' + err, 'danger');
        });
    }

    function resetUpload() {
        resetUploadState();
        // Optionally, clear the player fields as well
        // document.getElementById('bucket').value = 'inkscommon';
        // document.getElementById('object').value = '';
    }

    function goToPlayer() { switchTab('player'); }

    function switchTab(tabId) {
        document.querySelectorAll('.tabs .tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));

        document.querySelector(`.tabs .tab[onclick*="'${tabId}'"]`).classList.add('active');
        document.getElementById(`${tabId}-tab`).classList.add('active');
    }

    function showAlert(message, type = 'info', duration = 3000) {
        const existingAlert = document.querySelector('.app-container > .alert');
        if(existingAlert) existingAlert.remove(); // Remove previous alert if any

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.setAttribute('role', 'alert');
        alertDiv.innerHTML = `
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}-fill me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

        const container = document.querySelector('.app-container');
        container.insertBefore(alertDiv, container.firstChild); // Insert at the top of the app container

        if (duration > 0) {
            setTimeout(() => {
                // Trigger fade out
                alertDiv.classList.remove('show');
                // Remove after fade out transition (Bootstrap default is 150ms)
                setTimeout(() => alertDiv.remove(), 150);
            }, duration);
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function play(){
        const bucket = document.getElementById('bucket').value.trim();
        const objectPath = document.getElementById('object').value.trim(); // Renamed for clarity
        if(!bucket || !objectPath){
            showAlert('请填写 Bucket 和对象名才能播放。', 'danger');
            return;
        }

        // Ensure the object path for streaming does not start with a slash if the prefix also has one.
        // The backend /File/stream/** expects the objectName directly.
        const streamUrl = `${API_SERVER_PREFIX}/File/stream/${objectPath}`;

        const videoPlayer = document.getElementById('player');
        videoPlayer.onerror = () => {
            let errorMsg = '视频加载失败。请检查：<ul>';
            errorMsg += '<li>Bucket 和对象名是否正确。</li>';
            errorMsg += '<li>后端服务是否正常运行。</li>';
            errorMsg += '<li>网络连接是否通畅。</li>';
            errorMsg += '<li>浏览器是否支持该视频格式。</li></ul>';
            showAlert(errorMsg, 'danger', 5000); // Longer duration for error with list
        };
        videoPlayer.src = streamUrl;
        videoPlayer.load();
        videoPlayer.play().catch(e => {
            // Autoplay might be blocked by browser, this is usually fine as controls are present.
            console.warn("播放视频时出错 (可能是自动播放限制):", e.message);
            showAlert(`无法自动播放视频: ${e.message}. 请尝试手动点击播放按钮。`, 'warning');
        });
    }

    window.addEventListener('DOMContentLoaded', init);
</script>
</body>
</html>