# 🗂️ 文件服务 API 文档（完整版）

**统一规则**：

- `objectname` = `dir`目录名 + `filename`文件名
- 所有上传类接口需指定目标目录 `dir`
- 文件名 `filename`可选，如未指定则自动生成UUID文件名

---

## 1️⃣ 通用文件接口

| 接口              | HTTP | 入参                                | 说明                  |
| ----------------- | ---- | ----------------------------------- | --------------------- |
| `/upload`         | POST | `file`(文件) `dir` `filename`(可选) | 通用上传文件          |
| `/uploadByPath`   | POST | `filepath` `dir` `filename`(可选)   | 指定本地路径上传      |
| `/uploadByStream` | POST | `file`(文件) `dir` `filename`(可选) | 文件流上传            |
| `/remove`         | POST | `objectname`                        | 删除指定单个文件      |
| `/proxy/**`       | GET  | URL路径中的`objectname`             | 通用图片预览/文件下载 |

### 📌 接口示例

#### 通用文件上传

**请求说明**：
- 通过表单提交文件，必须包含`file`文件对象和`dir`目录参数
- 可选参数`filename`指定文件名，若不提供则自动生成UUID文件名
- Content-Type类型为`multipart/form-data`
- 响应返回对象名、文件名及访问URL等信息

#### 指定路径上传

**请求说明**：
- 指定服务器本地文件路径进行上传，仅限于服务器本地文件
- 必须参数：`filepath`(服务器本地完整路径)和`dir`(目标目录)
- 可选参数`filename`指定最终存储的文件名
- Content-Type类型为`application/x-www-form-urlencoded`

#### 文件流上传

**请求说明**：
- 通过文件流方式上传，适合系统间对接或程序化上传
- 直接传输原始文件流数据，无需缓存为临时文件
- 必须参数：文件数据流和`dir`目录
- 可选参数：`filename`(文件名)、`contentType`(文件类型)和`size`(文件大小)
- 若不指定contentType，系统会根据文件后缀自动推断

#### 删除文件

**请求说明**：
- 删除指定的已上传文件
- 必须参数：`objectname`(完整的对象名，包含目录)
- Content-Type类型为`application/x-www-form-urlencoded`
- 响应包含删除状态及结果信息

#### 文件预览/下载

**请求说明**：
- 通过`/File/proxy/{objectname}`路径访问文件
- 图片类型会直接预览，非图片类型会触发下载
- 支持通过URL直接嵌入图片到HTML中，或创建下载链接

---

## 2️⃣ 大文件分片接口（MinIO）

| 接口               | HTTP | 入参                                               | 说明                |
| ------------------ | ---- | -------------------------------------------------- | ------------------- |
| `/split/init`      | POST | `fileName` `fileSize` `chunkSize` `uploadId`(可选) | 初始化分片上传      |
| `/split/chunk`     | POST | `file`(文件) `uploadId` `chunkNumber` `chunkHash`  | 上传单个分片        |
| `/split/complete`  | POST | `uploadId` `dir` `filename`(可选)                  | 完成分片合并并上传  |
| `/split/cancel`    | POST | `uploadId`                                         | 取消分片上传任务    |
| `/split/progress`  | GET  | `uploadId`                                         | 查询合并进度        |
| `/split/stream/**` | GET  | URL路径中的objectname，支持HTTP Range              | 断点下载/视频流播放 |

### 📌 接口示例

#### 大文件分片上传流程

**使用流程说明**：

1. **初始化分片上传**：
   - 请求`/File/split/init`接口
   - 提供文件名`fileName`、总大小`fileSize`和分片大小`chunkSize`
   - 返回唯一的`uploadId`用于后续操作

2. **上传分片**：
   - 请求`/File/split/chunk`接口
   - 每个分片需提供`file`(分片数据)、`uploadId`、`chunkNumber`(序号)和`chunkHash`(可选校验值)
   - 分片序号从0开始，按顺序上传

3. **完成上传**：
   - 所有分片上传完成后，请求`/File/split/complete`接口
   - 提供`uploadId`、目标目录`dir`和可选的`filename`
   - 系统将异步合并所有分片为完整文件

4. **查询进度**：
   - 调用`/File/split/progress`接口查询合并进度
   - 提供`uploadId`参数
   - 返回合并状态(WAITING/PROCESSING/COMPLETED/ERROR)和进度百分比
   - 当状态为COMPLETED时，返回结果包含完成后的文件访问地址

#### 视频流播放

**请求说明**：
- 通过`/File/split/stream/{objectname}`访问视频流
- 支持HTTP Range请求，可实现视频断点播放和快进功能
- 可在视频播放器中设置视频源为此URL路径

---

## 3️⃣ 文本上传接口

| 接口            | HTTP | 入参                           | 说明                   |
| --------------- | ---- | ------------------------------ | ---------------------- |
| `/uploadByText` | POST | `text` `dir` `filename`        | 文本内容转.txt文件上传 |
| `/uploadByHtml` | POST | `htmlContent` `dir` `filename` | 字符串转.html文件上传  |

### 📌 接口示例

#### 文本上传

**请求说明**：
- 将纯文本内容上传为.txt文件
- 必须参数：`text`(文本内容)、`dir`(目标目录)和`filename`(文件名)
- Content-Type类型为`application/x-www-form-urlencoded`
- 适用于直接将文本内容保存为文件，无需创建临时文件

#### HTML内容上传

**请求说明**：
- 将HTML字符串内容上传为.html文件
- 必须参数：`htmlContent`(HTML内容)、`dir`(目标目录)和`filename`(文件名)
- Content-Type类型为`application/x-www-form-urlencoded`
- 适用于动态生成的HTML内容保存为静态文件

---

## 4️⃣ 图片上传接口

| 接口            | HTTP | 入参                                       | 说明                 |
| --------------- | ---- | ------------------------------------------ | -------------------- |
| `/upload2Image` | POST | `file`(文件) `dir` `filename`(可选)        | 同时上传缩略图和原图 |
| `/uploadBase64` | POST | JSON格式 {`base64Data`, `dir`, `filename`} | Base64图片上传       |

### 📌 接口示例

#### 缩略图上传

**请求说明**：
- 上传图片并同时生成缩略图
- 必须参数：`file`(图片文件)、`dir`(目标目录)
- 可选参数：`filename`(图片文件名)
- Content-Type类型为`multipart/form-data`
- 响应同时返回原图URL和缩略图URL(thumbUrl)

#### Base64图片上传

**请求说明**：
- 上传Base64编码的图片数据
- 必须参数：`base64Data`(去掉前缀的Base64字符串)、`dir`(目标目录)和`filename`(文件名)
- Content-Type类型为`application/json`
- 适用于Canvas截图、裁剪后的图片数据等场景

---

## 5️⃣ 目录/批量操作接口

| 接口                  | HTTP | 入参                   | 说明                       |
| --------------------- | ---- | ---------------------- | -------------------------- |
| `/uploadZipStructure` | POST | `zipFile`(文件) `path` | 上传zip并按目录解压上传    |
| `/getFileTreeSizes`   | GET  | `path` `recursive`     | 获取目录下文件树及大小     |
| `/removeFilesByPath`  | GET  | `path`                 | 批量递归删除目录下所有对象 |

### 📌 接口示例

#### 上传ZIP并解压

**请求说明**：
- 上传ZIP文件并按原目录结构解压上传到指定路径
- 必须参数：`zipFile`(ZIP压缩文件)和`path`(解压目标路径)
- Content-Type类型为`multipart/form-data`
- 响应包含解压文件数量和总大小信息

#### 获取文件树

**请求说明**：
- 获取指定路径下的文件树结构及大小统计
- 必须参数：`path`(目标路径)
- 可选参数：`recursive`(是否递归子目录，true/false)
- 请求方法为GET
- 返回目录树结构和文件总大小

#### 批量删除文件

**请求说明**：
- 递归删除指定路径下的所有文件和子目录
- 必须参数：`path`(要删除的目录路径)
- 请求方法为GET
- 响应包含删除文件数量和操作结果

---

## 📦 响应格式示例

所有接口统一返回：

```json
// 成功响应示例
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "objectName": "images/logo.png",
    "fileName": "logo.png",
    "url": "/File/proxy/images/logo.png",
    "size": 24680,
    "contentType": "image/png"
  }
}

// 失败响应示例
{
  "code": 500,
  "msg": "上传失败: 文件格式不支持",
  "data": null
}
