package inks.sa.common.core.config.oss.service;

import cn.hutool.core.util.IdUtil;
import com.aliyun.oss.OSS;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.R;
import inks.sa.common.core.config.oss.utils.OssConstant;
import inks.sa.common.core.config.oss.utils.UploadImageVO;
import io.minio.*;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
// ... 其他 import ...

@RestController
@RequestMapping("/allFile") // 统一的API前缀
public class AllFileController0604 {

    private static final Logger log = LoggerFactory.getLogger(AllFileController.class);

    @Resource
    private OSSConfigManager configManager;

    // 注入 MinIO 和 Aliyun 的 OssService 实现
    @Resource
    @Qualifier("ossMinioServiceImpl") // 假设实现类的Bean名称
    private OssService minioService;

    @Resource
    @Qualifier("ossAliyunServiceImpl") // 假设实现类的Bean名称
    private OssService aliyunService;

    // MinIO Client (主要用于分片上传的特定API和流式操作)
    @Resource
    private MinioClient minioClient;

    // Aliyun OSS Client (主要用于流式操作的特定API)
    @Resource
    private OSS aliyunOssClient;


    // --- 大文件分片相关成员 (从 File_VideoController 迁移并适配) ---
    private static final ConcurrentHashMap<String, ChunkInfo> CHUNK_INFO_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Long> CHUNK_LAST_ACCESS_TIME = new ConcurrentHashMap<>();
    private static final Map<String, MergeProgress> MERGE_PROGRESS_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Future<?>> ACTIVE_MERGE_TASKS = new ConcurrentHashMap<>();

    @Value("${file.upload.temp-dir:${java.io.tmpdir}/allfile-upload-chunks}")
    private String tempDirBase;
    private Path tempDirPath;

    @Value("${file.upload.expiration-hours:24}")
    private int chunkExpirationHours;

    private ExecutorService mergeExecutor;
    private boolean minioInitialized = false; // 用于标记MinIO分片环境是否初始化

    // --- 初始化与销毁 ---
    @PostConstruct
    public void initController() {
        try {
            // 初始化分片上传的临时目录
            this.tempDirPath = Paths.get(tempDirBase);
            if (!Files.exists(this.tempDirPath)) {
                Files.createDirectories(this.tempDirPath);
                log.info("Created temporary directory for chunks: {}", this.tempDirPath.toAbsolutePath());
            }

            // 初始化用于合并操作的线程池
            mergeExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            log.info("Merge executor service initialized for AllFileController.");

            // MinIO Bucket检查 (如果MinIO是当前或默认OSS类型，或者为了分片上传功能)
            // 注意: 分片上传目前主要针对MinIO实现
            initMinioForSharding();

        } catch (Exception e) {
            log.error("Error during AllFileController initialization", e);
        }
    }

    private synchronized void initMinioForSharding() {
        if (minioInitialized) return;
        try {
            String minioBucket = configManager.getMinioBucket();
            if (minioClient != null && StringUtils.isNotBlank(minioBucket)) {
                boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioBucket).build());
                if (!bucketExists) {
                    minioClient.makeBucket(MakeBucketArgs.builder().bucket(minioBucket).build());
                    log.info("MinIO Bucket '{}' created for sharding.", minioBucket);
                }
                minioInitialized = true;
                log.info("MinIO sharding environment initialized. Bucket: '{}'", minioBucket);
            } else {
                log.warn("MinIO client or bucket not configured, sharding uploads might fail if MinIO is intended.");
            }
        } catch (Exception e) {
            log.error("Failed to initialize MinIO for sharding", e);
        }
    }


    @PreDestroy
    public void destroyController() {
        if (mergeExecutor != null && !mergeExecutor.isShutdown()) {
            // ... (安全关闭 mergeExecutor, 参考 File_VideoController)
            log.info("AllFileController merge executor service shut down.");
        }
    }

    // --- 辅助方法 ---
    private OssService getCurrentOssService() {
        String ossType = configManager.getOssType();
        if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
            return minioService;
        } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
            return aliyunService;
        }
        throw new IllegalStateException("Unsupported OSS type: " + ossType);
    }

    private String getCurrentBucketName() {
        String ossType = configManager.getOssType();
        if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
            return configManager.getMinioBucket();
        } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
            return configManager.getAliyunBucket();
        }
        throw new IllegalStateException("Unsupported OSS type: " + ossType);
    }

    private String buildObjectName(String dir, String clientProvidedFilename, String originalMultipartFilename) {
        String filenameToUse;
        if (StringUtils.isNotBlank(clientProvidedFilename)) {
            filenameToUse = clientProvidedFilename;
        } else if (StringUtils.isNotBlank(originalMultipartFilename)) {
            String extension = "";
            if (originalMultipartFilename.contains(".")) {
                extension = originalMultipartFilename.substring(originalMultipartFilename.lastIndexOf("."));
            }
            filenameToUse = IdUtil.fastSimpleUUID() + extension;
        } else {
            filenameToUse = IdUtil.fastSimpleUUID(); // 无原始文件名，生成纯UUID
        }
        // 规范化目录和文件名
        String cleanedDir = dir.replaceAll("^/+", "").replaceAll("/+$", "");
        return cleanedDir.isEmpty() ? filenameToUse : cleanedDir + "/" + filenameToUse;
    }

    private String getFileExtension(String fileName) {
        if (StringUtils.isNotBlank(fileName) && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf("."));
        }
        return "";
    }


    // =====================================================================================
    // 1️⃣ 通用文件上传类
    // =====================================================================================

    @ApiOperation("通用上传文件")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<FileInfo> upload(
            @RequestParam("file") MultipartFile file,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename) {
        if (file.isEmpty()) {
            return R.fail("上传文件不能为空");
        }
        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String objectName = buildObjectName(dir, filename, file.getOriginalFilename());

            // 假设 OssService 有一个接受 (MultipartFile, bucket, objectName) 的方法
            // 或者 (InputStream, bucket, objectName, contentType, size)
            FileInfo fileInfo = service.upload(file, objectName);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("指定文件本地路径上传")
    @PostMapping("/uploadByPath")
    public R<String> uploadByPath(
            @RequestParam("filepath") String filepath,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename) {
        File localFile = new File(filepath);
        if (!localFile.exists() || !localFile.isFile()) {
            return R.fail("指定的文件路径无效或不是文件");
        }
        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String objectName = buildObjectName(dir, filename, localFile.getName());

            // 假设 OssService 有一个接受 (File, bucket, objectName) 的方法
            objectName= service.putFile(filepath, bucketName, objectName);
            return R.ok(objectName);
        } catch (Exception e) {
            log.error("Upload by path failed for filepath: {}. Error: {}", filepath, e.getMessage(), e);
            return R.fail("路径上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("文件流上传")
    @PostMapping(value = "/uploadInputStream", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> uploadInputStream(
            @RequestPart("file") MultipartFile file, // 使用 @RequestPart 获取文件流及其元数据
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename) {
        if (file.isEmpty()) {
            return R.fail("上传文件流不能为空");
        }
        try (InputStream inputStream = file.getInputStream()) {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String objectName = buildObjectName(dir, filename, file.getOriginalFilename());
            long size = file.getSize();
            String contentType = file.getContentType();

            // 假设 OssService 有一个接受 (InputStream, bucket, objectName, size, contentType) 的方法
            objectName = service.uploadStream(inputStream, bucketName, objectName, size, contentType);
            return R.ok(objectName);
        } catch (Exception e) {
            log.error("Upload input stream failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("文件流上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除指定单个文件")
    @PostMapping("/remove")
    public R<String> remove(@RequestParam("objectname") String objectname) {
        if (!StringUtils.isNotBlank(objectname)) {
            return R.fail("文件名不能为空");
        }
        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            service.remove(bucketName, objectname);
            return R.ok("文件删除成功: " + objectname);
        } catch (Exception e) {
            log.error("Remove failed for objectname: {}. Error: {}", objectname, e.getMessage(), e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @ApiOperation("通用图片预览/文件下载（支持Range，代理访问OSS）")
    @GetMapping("/proxy/**")
    public void proxy(HttpServletRequest request, HttpServletResponse response) {
        String objectName = extractPathFromPattern(request, "/proxy/**");
        if (!StringUtils.isNotBlank(objectName)) {
            sendError(response, HttpServletResponse.SC_BAD_REQUEST, "请求的 objectName 为空");
            return;
        }

        try {
            String ossType = configManager.getOssType();
            String bucketName = getCurrentBucketName(); // 获取当前配置的bucket

            if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
                // 使用 MinIO 客户端处理流式传输和 Range 请求
                // (参考 File_VideoController.streamFromMinio 方法的核心逻辑)
                // 需要 MinioClient, bucketName, objectName
                // StatObjectArgs, GetObjectArgs (带 offset, length for range)
                // IOUtils.copy, response.setHeader, response.setStatus...
                streamMinioObject(minioClient, bucketName, objectName, request, response);

            } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
                // 使用 Aliyun OSS 客户端处理流式传输和 Range 请求
                // 需要 OSS Client, bucketName, objectName
                // GetObjectRequest (带 setRange for range)
                // IOUtils.copy, response.setHeader, response.setStatus...
                streamAliyunObject(aliyunOssClient, bucketName, objectName, request, response);
            } else {
                sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "未知的OSS类型配置");
            }
        } catch (Exception e) {
            log.error("Proxy error for object {}: {}", objectName, e.getMessage(), e);
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "代理访问文件失败: " + e.getMessage());
        }
    }

    private String extractPathFromPattern(HttpServletRequest request, String patternPrefix) {
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        //  /allFile/proxy/path/to/object.txt -> path/to/object.txt
        return antPathMatcher.extractPathWithinPattern(bestMatchPattern, path);
    }

    private void sendError(HttpServletResponse response, int sc, String message) {
        try {
            if (!response.isCommitted()) {
                response.sendError(sc, message);
            }
        } catch (IOException e) {
            log.error("Error sending error response: {}", e.getMessage());
        }
    }

    // streamMinioObject 和 streamAliyunObject 具体实现需要参考原代码并适配Range
    // 此处仅为示意，详细实现略
    private void streamMinioObject(MinioClient client, String bucket, String objectName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ObjectStat stat = client.statObject(StatObjectArgs.builder().bucket(bucket).object(objectName).build());
        // ... Range 处理逻辑, 设置 response headers, 获取流并 copy ... (参考File_VideoController.handleRangeRequest & streamObjectContent)
        log.info("Streaming MinIO object: {}/{}", bucket, objectName); // 示例日志
        // 简化版：直接下载整个文件
        try (InputStream is = client.getObject(GetObjectArgs.builder().bucket(bucket).object(objectName).build())) {
            response.setContentType(stat.contentType());
            response.setContentLengthLong(stat.length());
            IOUtils.copy(is, response.getOutputStream());
        }
    }

    private void streamAliyunObject(OSS client, String bucket, String objectName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        com.aliyun.oss.model.ObjectMetadata meta = client.getObjectMetadata(bucket, objectName);
        // ... Range 处理逻辑, 设置 response headers, 获取流并 copy ...
        log.info("Streaming Aliyun object: {}/{}", bucket, objectName); // 示例日志
        // 简化版：直接下载整个文件
        try (InputStream is = client.getObject(bucket, objectName).getObjectContent()) {
            response.setContentType(meta.getContentType());
            response.setContentLengthLong(meta.getContentLength());
            IOUtils.copy(is, response.getOutputStream());
        }
    }


    // =====================================================================================
    // 2️⃣ 大文件分片类 (主要适配MinIO, 与File_VideoController逻辑类似)
    // =====================================================================================

    @ApiOperation("初始化分片上传")
    @PostMapping("/initMultipartUpload")
    public R<Map<String, Object>> initMultipartUpload(
            @RequestParam("fileName") String clientOriginalFileName,
            @RequestParam("fileSize") Long fileSize,
            @RequestParam("chunkSize") Integer chunkSize,
            @RequestParam(value = "uploadId", required = false) String existingUploadId) {
        if (!StringUtils.isNotBlank(clientOriginalFileName) || fileSize == null || chunkSize == null || fileSize <= 0 || chunkSize <= 0) {
            return R.fail("参数错误: 文件名、文件大小和分片大小必须有效");
        }
        if (!minioInitialized) initMinioForSharding(); // 确保MinIO环境就绪

        try {
            String uploadId = StringUtils.isNotBlank(existingUploadId) ? existingUploadId : UUID.randomUUID().toString();
            ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
            Map<String, Object> result = new HashMap<>();

            if (chunkInfo != null) { // 恢复任务
                if (chunkInfo.getOriginalFileName().equals(clientOriginalFileName) && chunkInfo.getTotalSize() == fileSize) {
                    log.info("恢复已存在的分片上传任务. UploadID: {}, 文件名: {}", uploadId, clientOriginalFileName);
                    result.put("resumed", true);
                } else {
                    log.warn("UploadID {} 已存在但文件信息不匹配. 将生成新任务.", uploadId);
                    uploadId = UUID.randomUUID().toString();
                    chunkInfo = null;
                    result.put("resumed", false);
                }
            } else {
                result.put("resumed", false);
            }

            if (chunkInfo == null) { // 创建新任务
                Path tempUploadDir = this.tempDirPath.resolve(uploadId);
                if (!Files.exists(tempUploadDir)) Files.createDirectories(tempUploadDir);

                chunkInfo = new ChunkInfo();
                chunkInfo.setUploadId(uploadId);
                chunkInfo.setOriginalFileName(clientOriginalFileName); // 存储原始文件名
                chunkInfo.setTotalSize(fileSize);
                chunkInfo.setChunkSize(chunkSize);
                chunkInfo.setTempDir(tempUploadDir.toString());
                int totalChunks = (int) Math.ceil((double) fileSize / chunkSize);
                chunkInfo.setTotalChunks(totalChunks);
                chunkInfo.setUploadedChunks(new boolean[totalChunks]);
                chunkInfo.setChunkHashes(new String[totalChunks]);
                // objectName 和 fileName 将在 completeMultipartUpload 时根据传入的 dir/filename 确定
                CHUNK_INFO_MAP.put(uploadId, chunkInfo);
                log.info("初始化新的分片上传. 原始文件名: '{}', UploadID: {}", clientOriginalFileName, uploadId);
            }

            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());

            result.put("uploadId", uploadId);
            result.put("chunkSize", chunkInfo.getChunkSize());
            result.put("totalChunks", chunkInfo.getTotalChunks());
            result.put("uploadedChunks", chunkInfo.getUploadedChunks());
            result.put("chunkHashes", chunkInfo.getChunkHashes());

            return R.ok(result);
        } catch (IOException e) {
            log.error("IO错误: {}", e.getMessage(), e);
            return R.fail("创建临时目录失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("初始化分片上传失败: {}", e.getMessage(), e);
            return R.fail("初始化分片上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("上传分片")
    @PostMapping("/uploadChunk")
    public R<Map<String, Object>> uploadChunk(
            @RequestParam("file") MultipartFile file,
            @RequestParam("uploadId") String uploadId,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("chunkHash") String clientChunkHash) { // 接收客户端计算的哈希
        // ... (逻辑基本同 File_VideoController.uploadChunk)
        // 校验 uploadId, chunkNumber
        // 保存分片到 chunkInfo.getTempDir() / "chunk_" + chunkNumber
        // 服务端计算分片哈希 serverChunkHash，与 clientChunkHash 对比
        // 更新 chunkInfo.getUploadedChunks()[chunkNumber] 和 chunkInfo.getChunkHashes()[chunkNumber]
        // 更新 CHUNK_LAST_ACCESS_TIME
        // 返回上传进度 {uploadedChunks, totalChunks, isCompleted}
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        // ... (详细实现参考 File_VideoController)
        if (chunkInfo == null) return R.fail("无效上传ID");
        // 示例：
        try {
            File chunkFile = new File(chunkInfo.getTempDir(), "chunk_" + chunkNumber);
            file.transferTo(chunkFile);
            // ... 哈希校验 ...
            chunkInfo.getUploadedChunks()[chunkNumber] = true;
            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());
            // ... 返回结果 ...
            Map<String, Object> result = new HashMap<>();
            result.put("message", "分片 " + chunkNumber + " 上传成功");
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("分片上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("完成分片上传（异步）")
    @PostMapping("/completeMultipartUpload")
    public R<Map<String, Object>> completeMultipartUpload(
            @RequestParam("uploadId") String uploadId,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String clientFilename) {
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        if (chunkInfo == null) {
            return R.fail("无效的上传ID: " + uploadId);
        }
        if (!minioInitialized) { // 确保MinIO客户端和桶已准备好
            return R.fail("MinIO服务未初始化，无法完成上传");
        }

        String effectiveFilename = StringUtils.isBlank(clientFilename) ?
                IdUtil.fastSimpleUUID() + getFileExtension(chunkInfo.getOriginalFileName()) : clientFilename;
        String finalObjectName = buildObjectName(dir, effectiveFilename, null); // 使用 dir 和 effectiveFilename 构建

        chunkInfo.setFileName(effectiveFilename); // 存储最终文件名（不含路径）
        chunkInfo.setObjectName(finalObjectName); // 存储最终完整对象路径

        log.info("请求完成分片上传. UploadID: {}, 最终对象名: {}", uploadId, finalObjectName);

        // ... (异步合并与上传逻辑, 基本同 File_VideoController.completeMultipartUpload)
        // 注意: 上传到MinIO时使用 chunkInfo.getObjectName() 和 configManager.getMinioBucket()
        // MergeProgress 的状态和百分比更新
        // 成功后，MERGE_PROGRESS_MAP 中记录的 objectName 应为 finalObjectName
        // 清理临时文件和映射条目

        // 示例启动异步任务：
        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "QUEUED", 0, null, false));
        Future<?> mergeTask = mergeExecutor.submit(() -> {
            Thread.currentThread().setName("merge-upload-" + uploadId);
            boolean success = false;
            try {
                // ... (检查所有分片是否上传 - 更新进度)
                // ... (合并分片到临时文件 mergedFile - 更新进度)
                File mergedFile = new File(chunkInfo.getTempDir(), chunkInfo.getFileName()); // 使用刚设置的最终文件名
                // ... (合并逻辑)

                // ... (上传到MinIO - 更新进度)
                try (InputStream is = Files.newInputStream(mergedFile.toPath())) {
                    minioClient.putObject(
                            PutObjectArgs.builder()
                                    .bucket(configManager.getMinioBucket())
                                    .object(chunkInfo.getObjectName()) // 使用 ChunkInfo 中更新的完整对象名
                                    .contentType(Files.probeContentType(mergedFile.toPath())) //自动识别或指定
                                    .stream(is, mergedFile.length(), -1)
                                    .build()
                    );
                }
                success = true;
            } catch (Exception e) {
                // ... (错误处理和进度更新)
            } finally {
                if (success) {
                    MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "COMPLETED", 100, null, false));
                }
                // ... (清理逻辑, 参考File_VideoController)
                ACTIVE_MERGE_TASKS.remove(uploadId);
            }
        });
        ACTIVE_MERGE_TASKS.put(uploadId, mergeTask);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "文件合并与上传任务已启动");
        response.put("uploadId", uploadId);
        response.put("objectName", finalObjectName); // 返回最终的 objectName
        return R.ok(response);
    }


    @ApiOperation("取消分片上传任务")
    @PostMapping("/cancelMultipartUpload") // 建议使用POST进行有状态更改的操作
    public R<String> cancelMultipartUpload(@RequestParam("uploadId") String uploadId) {
        // ... (逻辑同 File_VideoController.cancelMultipartUpload)
        // 中断 ACTIVE_MERGE_TASKS 中的任务
        // 清理 CHUNK_INFO_MAP, CHUNK_LAST_ACCESS_TIME, MERGE_PROGRESS_MAP (或标记为CANCELLED)
        // 删除临时分片目录
        return R.ok("上传任务 " + uploadId + " 已取消");
    }

    @ApiOperation("获取合并进度")
    @GetMapping("/getMergeProgress")
    public R<Map<String, Object>> getMergeProgress(@RequestParam("uploadId") String uploadId) {
        // ... (逻辑同 File_VideoController.getMergeProgress)
        // 从 MERGE_PROGRESS_MAP 获取进度
        // 如果 "COMPLETED", 从 CHUNK_INFO_MAP 获取 objectName, 生成预览URL (如果需要)
        MergeProgress progress = MERGE_PROGRESS_MAP.get(uploadId);
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);

        if (progress == null) return R.fail("未找到上传ID的进度: " + uploadId);

        Map<String, Object> result = new HashMap<>();
        result.put("status", progress.getStatus());
        result.put("percent", progress.getPercent());
        // ... (其他进度信息)

        if ("COMPLETED".equals(progress.getStatus()) && chunkInfo != null && StringUtils.isNotBlank(chunkInfo.getObjectName())) {
            result.put("objectName", chunkInfo.getObjectName());
            // 可选: 生成预签名URL
            // String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()... .object(chunkInfo.getObjectName())...);
            // result.put("url", url);
        }
        return R.ok(result);
    }

    @ApiOperation("文件断点/分段下载、视频流播放")
    @GetMapping("/stream/**")
    public void streamFile(HttpServletRequest request, HttpServletResponse response) {
        String objectName = extractPathFromPattern(request, "/stream/**");
        if (!StringUtils.isNotBlank(objectName)) {
            sendError(response, HttpServletResponse.SC_BAD_REQUEST, "请求的 objectName 为空");
            return;
        }
        // 当前分片逻辑主要基于MinIO，所以stream也默认从MinIO获取
        // 如果未来分片支持多种OSS，这里需要判断
        if (!minioInitialized) {
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "MinIO服务未初始化，无法提供流式服务");
            return;
        }
        try {
            // (核心逻辑参考 File_VideoController.streamFromMinio)
            // 使用 minioClient, configManager.getMinioBucket(), objectName
            // 处理HTTP Range请求，设置正确的响应头 (Content-Type, Content-Length, Content-Range, Accept-Ranges)
            streamMinioObject(minioClient, configManager.getMinioBucket(), objectName, request, response); // 复用之前的 streamMinioObject 或更详细的实现
        } catch (Exception e) {
            log.error("Stream error for object {}: {}", objectName, e.getMessage(), e);
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "流式传输文件失败.");
        }
    }

    @Scheduled(fixedRate = 60 * 60 * 1000) // 每小时清理过期的分片任务
    public void cleanupExpiredChunkUploads() {
        // ... (逻辑同 File_VideoController.cleanupExpiredChunks)
        log.info("Running scheduled cleanup of expired chunk uploads for AllFileController.");
    }

    // =====================================================================================
    // 3️⃣ 文本类
    // =====================================================================================

    @ApiOperation("文本内容上传（保存为纯文本文件）")
    @PostMapping("/uploadText")
    public R<String> uploadText(
            @RequestParam("text") String text,
            @RequestParam("dir") String dir,
            @RequestParam("filename") String filename) { // filename应包含.txt或由服务器添加
        if (!StringUtils.isNotBlank(text) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("文本内容、目录和文件名不能为空");
        }
        try (InputStream inputStream = new ByteArrayInputStream(text.getBytes(StandardCharsets.UTF_8))) {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            // 确保文件名有.txt后缀 (可选，取决于具体需求)
            String effectiveFilename = filename.toLowerCase().endsWith(".txt") ? filename : filename + ".txt";
            String objectName = buildObjectName(dir, effectiveFilename, null);
            long size = text.getBytes(StandardCharsets.UTF_8).length;

            objectName = service.uploadStream(inputStream, bucketName, objectName, size, "text/plain; charset=utf-8");
            return R.ok(objectName);
        } catch (Exception e) {
            log.error("Upload text failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("文本上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("字符串转.html文件格式上传")
    @PostMapping("/uploadHtml")
    public R<String> uploadHtml(
            @RequestParam("htmlContent") String htmlContent,
            @RequestParam("dir") String dir,
            @RequestParam("filename") String filename) { // filename应包含.html或由服务器添加
        if (!StringUtils.isNotBlank(htmlContent) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("HTML内容、目录和文件名不能为空");
        }
        try (InputStream inputStream = new ByteArrayInputStream(htmlContent.getBytes(StandardCharsets.UTF_8))) {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String effectiveFilename = filename.toLowerCase().endsWith(".html") ? filename : filename + ".html";
            String objectName = buildObjectName(dir, effectiveFilename, null);
            long size = htmlContent.getBytes(StandardCharsets.UTF_8).length;

            objectName = service.uploadStream(inputStream, bucketName, objectName, size, "text/html; charset=utf-8");
            return R.ok(objectName);
        } catch (Exception e) {
            log.error("Upload HTML failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("HTML上传失败: " + e.getMessage());
        }
    }

    // =====================================================================================
    // 4️⃣ 图片类
    // =====================================================================================
    private static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB

    @ApiOperation("同时上传缩略图和原图")
    @PostMapping(value = "/upload2Image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<UploadImageVO> upload2Image(
            @RequestParam("file") MultipartFile file,
            @RequestParam("dir") String dir, // 明确要求dir
            @RequestParam(value = "filename", required = false) String filename) {
        if (file.isEmpty()) return R.fail("图片文件不能为空");
        if (file.getSize() > MAX_IMAGE_SIZE) return R.fail("图片大小不能超过10MB");
        // 可选：图片格式校验 (e.g., using FileUtil.isImage from original FileController)

        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();

            // 1. 上传原图
            String originalFileExtension = getFileExtension(file.getOriginalFilename());
            String baseFilename = StringUtils.isBlank(filename) ? IdUtil.fastSimpleUUID() :
                    (filename.contains(".") ? filename.substring(0, filename.lastIndexOf(".")) : filename);

            String originEffectiveFilename = baseFilename + originalFileExtension;
            String originObjectName = buildObjectName(dir, originEffectiveFilename, null);
            FileInfo originFileInfo = service.upload(file, originObjectName);

            UploadImageVO vo = new UploadImageVO();
            vo.setOriginUrl(originFileInfo.getFileurl()); // 假设 FileInfo 中有 getFileurl()

            // 2. 生成并上传缩略图 (简化：如果文件大于30KB，则尝试生成)
            //    实际缩略图生成逻辑 (ImageUtil.compressForScale) 需要引入或实现
            if (file.getSize() > 30 * 1024) { // 30KB
                // 伪代码：生成缩略图
                // byte[] thumbBytes = ImageUtil.compressForScale(file.getBytes(), 30); // 假设压缩到30KB以内或按比例
                // InputStream thumbInputStream = new ByteArrayInputStream(thumbBytes);
                // String thumbFilename = baseFilename + "_thumb" + originalFileExtension;
                // String thumbObjectName = buildObjectName(dir, thumbFilename, null);
                // FileInfo thumbFileInfo = service.uploadStream(thumbInputStream, bucketName, thumbObjectName, thumbBytes.length, file.getContentType());
                // vo.setThumbUrl(thumbFileInfo.getFileurl());
                vo.setThumbUrl(originFileInfo.getFileurl()); // 简化：如果无 ImageUtil，则缩略图URL也用原图
                log.warn("Thumbnail generation skipped/simplified in upload2Image for {}", originObjectName);
            } else {
                vo.setThumbUrl(originFileInfo.getFileurl());
            }
            return R.ok(vo);
        } catch (Exception e) {
            log.error("Upload2Image failed. Error: {}", e.getMessage(), e);
            return R.fail("图片及缩略图上传失败: " + e.getMessage());
        }
    }


    @ApiOperation("Base64图上传")
    @PostMapping("/uploadBase64")
    public R<String> uploadBase64(@RequestBody Map<String, String> payload) {
        // payload 示例: {"base64Data": "data:image/png;base64,iVBORw0KG...", "dir": "images/avatars", "filename": "avatar.png"}
        String base64Data = payload.get("base64Data");
        String dir = payload.get("dir");
        String filename = payload.get("filename"); // 客户端应提供带后缀的文件名

        if (!StringUtils.isNotBlank(base64Data) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("Base64数据、目录和文件名不能为空");
        }

        try {
            // 解码Base64 (注意处理 "data:image/png;base64," 前缀)
            String actualBase64Data = base64Data;
            if (base64Data.contains(",")) {
                actualBase64Data = base64Data.substring(base64Data.indexOf(",") + 1);
            }
            byte[] imageBytes = Base64.getDecoder().decode(actualBase64Data);

            try (InputStream inputStream = new ByteArrayInputStream(imageBytes)) {
                OssService service = getCurrentOssService();
                String bucketName = getCurrentBucketName();
                String objectName = buildObjectName(dir, filename, null); // filename应由客户端提供，包含正确后缀

                // 从文件名或Base64前缀中推断ContentType (简化)
                String contentType = "application/octet-stream"; // 默认
                if (filename.toLowerCase().endsWith(".png")) contentType = "image/png";
                else if (filename.toLowerCase().endsWith(".jpg") || filename.toLowerCase().endsWith(".jpeg"))
                    contentType = "image/jpeg";
                else if (filename.toLowerCase().endsWith(".gif")) contentType = "image/gif";
                //更可靠的方式是从 "data:image/png;base64," 前缀中提取

                objectName = service.uploadStream(inputStream, bucketName, objectName, imageBytes.length, contentType);
                return R.ok(objectName);
            }
        } catch (Exception e) {
            log.error("Upload Base64 image failed. Error: {}", e.getMessage(), e);
            return R.fail("Base64图片上传失败: " + e.getMessage());
        }
    }


    // --- 内部类定义 (ChunkInfo, MergeProgress, FileInfo, UploadImageVO) ---
    // 这些类需要从 File_VideoController 和 FileController 迁移或重新定义
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChunkInfo {
        private String uploadId;
        private String originalFileName; // 用户上传的原始文件名
        private String fileName;         // MinIO上存储的文件名 (通常是UUID化的, 不含路径, 在complete时设置)
        private String objectName;       // MinIO上存储的完整对象路径 (dir/fileName, 在complete时设置)
        private long totalSize;
        private int chunkSize;
        private int totalChunks;
        private boolean[] uploadedChunks;
        private String[] chunkHashes;
        private String tempDir;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MergeProgress {
        private int current;
        private int total;
        private String status;
        private int percent;
        private String errorMessage;
        private boolean cancelled;
    }

    // FileInfo 和 UploadImageVO 结构保持和原项目一致
    // @Data public static class FileInfo { private String filename; private String fileurl; /* ...其他字段... */ }
    // @Data public static class UploadImageVO { private String originUrl; private String thumbUrl; }


    // --- JUnit测试示例 (不使用main方法) ---
    @Test
    public void testBuildObjectName() {
        // 仅为示例，实际controller中的@Test方法不会被Spring Boot扫描执行为HTTP接口
        // 需要在专门的测试类 (src/test/java) 中编写
        String result1 = buildObjectName("path/to", "file.txt", null);
        // Assert.assertEquals("path/to/file.txt", result1);

        String result2 = buildObjectName("path/to/", null, "original.jpg");
        // Assert.assertTrue(result2.startsWith("path/to/"));
        // Assert.assertTrue(result2.endsWith(".jpg"));
        System.out.println(result1);
        System.out.println(result2);
    }
}