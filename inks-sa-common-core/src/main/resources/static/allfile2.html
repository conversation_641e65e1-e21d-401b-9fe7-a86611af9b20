<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AllFileController 接口测试</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
            color: #343a40;
        }
        .main-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .main-header h1 {
            font-weight: 600;
            font-size: 2.5rem;
        }
        .main-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .base-url-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .api-group-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4e73df;
            display: flex;
            align-items: center;
        }
        .api-group-title .fas {
            margin-right: 10px;
            color: #4e73df;
        }
        .endpoint-card {
            background-color: #fff;
            border: none;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .endpoint-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .endpoint-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 500;
            display: flex;
            align-items: center;
            padding: 15px 20px;
        }
        .method-pill {
            font-size: 0.8rem;
            font-weight: bold;
            padding: 0.3em 0.8em;
            border-radius: 50px;
            margin-right: 10px;
            color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .method-pill.post { background-color: #28a745; } /* Green */
        .method-pill.get { background-color: #007bff; }  /* Blue */
        .method-pill.delete { background-color: #dc3545; } /* Red */
        .method-pill.put { background-color: #ffc107; color: #212529; } /* Yellow */

        .response-area {
            margin-top: 15px;
            padding: 20px 40px 20px 20px; /* Add padding on right for close button */
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            position: relative;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .response-area.info {
            background-color: #f8f9fa;
            color: #343a40;
            border-left: 5px solid #007bff;
        }
        .response-area.success {
            background-color: #f8f9fa;
            color: #343a40;
            border-left: 5px solid #28a745;
        }
        .response-area.danger {
            background-color: #f8f9fa;
            color: #343a40;
            border-left: 5px solid #dc3545;
        }
        .response-area.warning {
            background-color: #f8f9fa;
            color: #343a40;
            border-left: 5px solid #ffc107;
        }
        
        #globalResponseAreaContainer {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 40%; /* Reduced from taking up full width */
            max-width: 600px;
            z-index: 1050;
        }
        
        .close-response-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.2rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }
        .close-response-btn:hover {
            background-color: rgba(108, 117, 125, 0.2);
            color: #343a40;
        }
        
        .form-control, .custom-file-input {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        .btn {
            border-radius: 8px;
            padding: 8px 20px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-success {
            background-color: #1cc88a;
            border-color: #1cc88a;
        }
        .btn-success:hover {
            background-color: #17a673;
            border-color: #17a673;
            transform: translateY(-2px);
        }
        .btn-info {
            background-color: #36b9cc;
            border-color: #36b9cc;
        }
        .btn-info:hover {
            background-color: #2c9faf;
            border-color: #2c9faf;
            transform: translateY(-2px);
        }
        
        .sharding-steps > .card { margin-bottom: 15px; }
        .sharding-steps .step-number {
            font-weight: bold;
            margin-right: 8px;
            color: #4e73df;
        }
        #chunkUploadIdInfo {
            font-weight: bold;
            color: #1cc88a;
            background-color: rgba(28, 200, 138, 0.1);
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        /* Animation for the response area */
        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .animate-response {
            animation: slideIn 0.3s ease forwards;
        }
    </style>
</head>
<body>
<div class="container">
    <header class="main-header">
        <h1>AllFileController 接口测试</h1>
        <p>统一文件管理系统 API 测试工具</p>
    </header>

    <section class="base-url-section form-inline">
        <label for="baseUrlInput" class="mr-2"><strong>基础URL:</strong></label>
        <input type="text" class="form-control col-md-6 mr-2" id="baseUrlInput" value="http://192.168.99.96:8081/File">
        <button class="btn btn-info btn-sm" onclick="updateBaseUrl()">修改</button>
    </section>

    <div id="globalResponseAreaContainer">
        <div id="globalResponseArea" class="response-area" style="display:none;">
            <button type="button" class="close-response-btn" onclick="closeResponseArea()">&times;</button>
            <div id="responseContent"></div>
        </div>
    </div>


    <section class="api-group">
        <h2 class="api-group-title"><i class="fas fa-upload"></i>通用文件上传类</h2>
        <div class="endpoint-card">
            <div class="card-header">
                <span class="method-pill post">POST</span> /upload
            </div>
            <div class="card-body">
                <form id="formUpload" enctype="multipart/form-data">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="uploadFile">选择文件:</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="uploadFile" name="file" required>
                                <label class="custom-file-label" for="uploadFile">未选择任何文件</label>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="uploadDir">目录 (dir):</label>
                            <input type="text" class="form-control" id="uploadDir" name="dir" value="general/uploads" placeholder="例: images/avatars" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="uploadFilename">文件名 (可选):</label>
                        <input type="text" class="form-control" id="uploadFilename" name="filename" placeholder="留空则自动生成">
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> 上传文件</button>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /uploadByPath</div>
            <div class="card-body">
                <form id="formUploadByPath">
                    <div class="form-group">
                        <label for="uploadPathFilepath">文件路径:</label>
                        <input type="text" class="form-control" id="uploadPathFilepath" name="filepath" placeholder="例: C:/temp/file.txt" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="uploadPathDir">目录 (dir):</label>
                            <input type="text" class="form-control" id="uploadPathDir" name="dir" value="path/uploads" placeholder="例: documents" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="uploadPathFilename">文件名 (可选):</label>
                            <input type="text" class="form-control" id="uploadPathFilename" name="filename" placeholder="留空则自动生成">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> 指定路径上传</button>
                    <small class="form-text text-muted">注意: 此端点要求服务器有权访问提供的文件路径。</small>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /uploadInputStream</div>
            <div class="card-body">
                <form id="formUploadInputStream" enctype="multipart/form-data">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="inputStreamFile">选择文件 (for stream):</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="inputStreamFile" name="file" required>
                                <label class="custom-file-label" for="inputStreamFile">未选择任何文件</label>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="inputStreamDir">目录 (dir):</label>
                            <input type="text" class="form-control" id="inputStreamDir" name="dir" value="stream/uploads" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inputStreamFilename">文件名 (可选):</label>
                        <input type="text" class="form-control" id="inputStreamFilename" name="filename" placeholder="e.g., data.bin">
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-paper-plane"></i> 上传文件流</button>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /remove</div>
            <div class="card-body">
                <form id="formRemove">
                    <div class="form-group">
                        <label for="removeObjectname">对象名 (objectname):</label>
                        <input type="text" class="form-control" id="removeObjectname" name="objectname" placeholder="e.g., general/uploads/yourfile.txt" required>
                    </div>
                    <button type="submit" class="btn btn-danger"><i class="fas fa-trash"></i> 删除文件</button>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill get">GET</span> /proxy/**</div>
            <div class="card-body">
                <div class="form-group">
                    <label for="proxyObjectName">对象名 (路径中 /proxy/ 之后的部分):</label>
                    <input type="text" class="form-control" id="proxyObjectName" placeholder="e.g., general/uploads/image.jpg">
                </div>
                <button type="button" class="btn btn-info" onclick="testProxy()"><i class="fas fa-external-link-alt"></i> 测试代理/下载</button>
                <a id="proxyLink" href="#" target="_blank" style="display:none;" class="ml-2 btn btn-outline-secondary btn-sm">打开代理文件</a>
            </div>
        </div>
    </section>

    <section class="api-group">
        <h2 class="api-group-title"><i class="fas fa-file-medical"></i>大文件分片上传</h2>
        <p class="text-muted">此部分用于测试分片上传的各个后端接口。当前 UploadId: <span id="chunkUploadIdInfo">N/A</span></p>
        <div class="sharding-steps">
            <div class="endpoint-card" id="stepInit">
                <div class="card-header"><span class="method-pill post">POST</span> <span class="step-number">1.</span> /initMultipartUpload</div>
                <div class="card-body">
                    <form id="formInitMultipart">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="initFileName">客户端原始文件名:</label>
                                <input type="text" class="form-control" id="initFileName" name="fileName" placeholder="largefile.mp4" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="initFileSize">文件总大小 (bytes):</label>
                                <input type="number" class="form-control" id="initFileSize" name="fileSize" placeholder="104857600" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="initChunkSize">分片大小 (bytes):</label>
                                <input type="number" class="form-control" id="initChunkSize" name="chunkSize" value="5242880" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="initUploadIdResume">现有 UploadId (可选, 用于恢复):</label>
                                <input type="text" class="form-control" id="initUploadIdResume" name="uploadId">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-play-circle"></i> 初始化分片上传</button>
                    </form>
                </div>
            </div>

            <div class="endpoint-card" id="stepUploadChunk">
                <div class="card-header"><span class="method-pill post">POST</span> <span class="step-number">2.</span> /uploadChunk</div>
                <div class="card-body">
                    <p class="text-muted small">选择一个小文件代表一个分片进行测试。在实际应用中，此步骤会为每个分片重复执行。</p>
                    <form id="formUploadChunk" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="chunkUploadId">Upload ID (来自初始化步骤):</label>
                            <input type="text" class="form-control" id="chunkUploadId" name="uploadId" placeholder="将从初始化响应中自动填充" required>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="chunkFile">选择分片文件:</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="chunkFile" name="file" required>
                                    <label class="custom-file-label" for="chunkFile">选择代表分片的文件</label>
                                </div>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="chunkNumber">分片序号 (0-indexed):</label>
                                <input type="number" class="form-control" id="chunkNumber" name="chunkNumber" placeholder="0" required>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="chunkHash">客户端分片哈希:</label>
                                <input type="text" class="form-control" id="chunkHash" name="clientChunkHash" placeholder="模拟输入哈希值" required>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-arrow-circle-up"></i> 上传分片</button>
                    </form>
                </div>
            </div>

            <div class="endpoint-card" id="stepComplete">
                <div class="card-header"><span class="method-pill post">POST</span> <span class="step-number">3.</span> /completeMultipartUpload</div>
                <div class="card-body">
                    <form id="formCompleteMultipart">
                        <div class="form-group">
                            <label for="completeUploadId">Upload ID (来自初始化步骤):</label>
                            <input type="text" class="form-control" id="completeUploadId" name="uploadId" placeholder="将从初始化响应中自动填充" required>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="completeDir">最终存储目录 (dir):</label>
                                <input type="text" class="form-control" id="completeDir" name="dir" value="sharded/completed" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="completeFilename">最终文件名 (可选):</label>
                                <input type="text" class="form-control" id="completeFilename" name="filename" placeholder="e.g., final_video.mp4">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success"><i class="fas fa-check-circle"></i> 完成分片上传</button>
                    </form>
                </div>
            </div>

            <div class="endpoint-card" id="stepProgress">
                <div class="card-header"><span class="method-pill get">GET</span> <span class="step-number">4.</span> /getMergeProgress (可选)</div>
                <div class="card-body">
                    <form id="formGetMergeProgress">
                        <div class="form-group">
                            <label for="progressUploadId">Upload ID:</label>
                            <input type="text" class="form-control" id="progressUploadId" name="uploadId" placeholder="将从初始化响应中自动填充" required>
                        </div>
                        <button type="submit" class="btn btn-info"><i class="fas fa-spinner fa-spin" style="display:none;" id="progressSpinner"></i><i class="fas fa-tasks"></i> 获取合并进度</button>
                    </form>
                    <div id="mergeProgressDisplay" class="mt-2" style="display:none;">
                        <p><strong>状态:</strong> <span id="progressStatus"></span></p>
                        <div class="progress">
                            <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="endpoint-card" id="stepCancel">
                <div class="card-header"><span class="method-pill post">POST</span> <span class="step-number">5.</span> /cancelMultipartUpload (可选)</div>
                <div class="card-body">
                    <form id="formCancelMultipart">
                        <div class="form-group">
                            <label for="cancelUploadId">Upload ID:</label>
                            <input type="text" class="form-control" id="cancelUploadId" name="uploadId" placeholder="将从初始化响应中自动填充" required>
                        </div>
                        <button type="submit" class="btn btn-warning"><i class="fas fa-times-circle"></i> 取消分片上传</button>
                    </form>
                </div>
            </div>
            <div class="endpoint-card">
                <div class="card-header"><span class="method-pill get">GET</span> /stream/**</div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="streamObjectName">对象名 (路径中 /stream/ 之后的部分):</label>
                        <input type="text" class="form-control" id="streamObjectName" placeholder="e.g., sharded/completed/final_video.mp4">
                    </div>
                    <button type="button" class="btn btn-info" onclick="testStream()"><i class="fas fa-play"></i> 测试流播放/下载</button>
                    <a id="streamLink" href="#" target="_blank" style="display:none;" class="ml-2 btn btn-outline-secondary btn-sm">打开流文件</a>
                    <div class="mt-2">
                        <video id="videoPlayer" width="100%" height="auto" controls style="display:none; max-width:480px;"></video>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="api-group">
        <h2 class="api-group-title"><i class="fas fa-file-alt"></i>文本内容上传</h2>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /uploadText</div>
            <div class="card-body">
                <form id="formUploadText">
                    <div class="form-group">
                        <label for="textUploadContent">文本内容:</label>
                        <textarea class="form-control" id="textUploadContent" name="text" rows="3" required>Hello, this is a test text content.</textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="textUploadDir">目录 (dir):</label>
                            <input type="text" class="form-control" id="textUploadDir" name="dir" value="text/files" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="textUploadFilename">文件名 (e.g., notes.txt):</label>
                            <input type="text" class="form-control" id="textUploadFilename" name="filename" value="test.txt" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-save"></i> 上传文本</button>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /uploadHtml</div>
            <div class="card-body">
                <form id="formUploadHtml">
                    <div class="form-group">
                        <label for="htmlUploadContent">HTML 内容:</label>
                        <textarea class="form-control" id="htmlUploadContent" name="htmlContent" rows="3" required>&lt;!DOCTYPE html&gt;&lt;html&gt;&lt;body&gt;&lt;h1&gt;Test HTML&lt;/h1&gt;&lt;/body&gt;&lt;/html&gt;</textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="htmlUploadDir">目录 (dir):</label>
                            <input type="text" class="form-control" id="htmlUploadDir" name="dir" value="html/pages" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="htmlUploadFilename">文件名 (e.g., page.html):</label>
                            <input type="text" class="form-control" id="htmlUploadFilename" name="filename" value="test.html" required>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-code"></i> 上传HTML</button>
                </form>
            </div>
        </div>
    </section>

    <section class="api-group">
        <h2 class="api-group-title"><i class="fas fa-image"></i>图片处理类</h2>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /upload2Image</div>
            <div class="card-body">
                <form id="formUpload2Image" enctype="multipart/form-data">
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="image2File">选择图片文件:</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="image2File" name="file" accept="image/*" required>
                                <label class="custom-file-label" for="image2File">未选择任何图片</label>
                            </div>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="image2Dir">目录 (dir):</label>
                            <input type="text" class="form-control" id="image2Dir" name="dir" value="images/gallery" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="image2Filename">文件名 (可选, 无后缀):</label>
                        <input type="text" class="form-control" id="image2Filename" name="filename" placeholder="e.g., myphoto">
                    </div>
                    <button type="submit" class="btn btn-success"><i class="fas fa-images"></i> 上传图片和缩略图</button>
                </form>
            </div>
        </div>
        <div class="endpoint-card">
            <div class="card-header"><span class="method-pill post">POST</span> /uploadBase64</div>
            <div class="card-body">
                <form id="formUploadBase64_direct_submit_prevent"> <div class="form-group">
                    <label for="base64Data">Base64 数据:</label>
                    <textarea class="form-control" id="base64Data" name="base64Data" rows="3" placeholder="data:image/png;base64,iVBORw0KGgo..." required></textarea>
                </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="base64Dir">目录 (dir):</label>
                            <input type="text" class="form-control" id="base64Dir" name="dir" value="images/base64" required>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="base64Filename">文件名 (e.g., avatar.png):</label>
                            <input type="text" class="form-control" id="base64Filename" name="filename" placeholder="myimage.png" required>
                        </div>
                    </div>
                    <button type="button" id="submitBase64Btn" class="btn btn-success"><i class="fas fa-cloud-upload-alt"></i> 上传Base64图片</button>
                </form>
            </div>
        </div>
    </section>

</div>

<script>
    let API_BASE_URL = document.getElementById('baseUrlInput').value;
    const globalResponseArea = document.getElementById('globalResponseArea');
    let currentChunkUploadId = null;

    function updateBaseUrl() {
        API_BASE_URL = document.getElementById('baseUrlInput').value;
        displayResponse({ message: `Base URL updated to: ${API_BASE_URL}`}, 'info', 3000);
    }

    function displayResponse(data, statusType = 'info', autoDismissDelay = null) {
        const responseContent = document.getElementById('responseContent');
        globalResponseArea.className = `response-area ${statusType} animate-response`;
        
        if (typeof data === 'string') {
            responseContent.textContent = data;
        } else {
            responseContent.textContent = JSON.stringify(data, null, 2);
        }
        
        globalResponseArea.style.display = 'block';

        if (autoDismissDelay) {
            setTimeout(() => {
                closeResponseArea();
            }, autoDismissDelay);
        }
    }

    function closeResponseArea() {
        globalResponseArea.style.display = 'none';
    }

    function handleError(error, formId) {
        console.error(`API Error for ${formId}:`, error);
        let message = error.message || 'An unknown error occurred';
        if (error.response && typeof error.response.message === 'string') { // Assuming R.fail structure
            message = error.response.message;
        } else if (error.data && typeof error.data.message === 'string') {
            message = error.data.message;
        }
        displayResponse({ error: message, details: error.data || error }, 'danger');
    }

    async function handleFormSubmit(event, endpoint, method = 'POST', isJsonPayload = false) {
        event.preventDefault();
        const form = event.target;
        let body;
        let headers = {};
        const url = `${API_BASE_URL}${endpoint}`;

        if (isJsonPayload) {
            const formData = new FormData(form);
            const object = {};
            formData.forEach((value, key) => object[key] = value);
            body = JSON.stringify(object);
            headers['Content-Type'] = 'application/json';
        } else if (form.enctype === 'multipart/form-data') {
            body = new FormData(form);
        } else {
            body = new URLSearchParams(new FormData(form));
            headers['Content-Type'] = 'application/x-www-form-urlencoded';
        }

        try {
            const fetchOptions = { method, headers };
            if (method !== 'GET' && method !== 'HEAD') {
                fetchOptions.body = body;
            }

            const response = await fetch(url, fetchOptions);
            const responseData = await response.json(); // Assuming all responses are JSON

            if (!response.ok) {
                // Pass the parsed JSON error response to handleError
                throw { message: `HTTP error ${response.status} for ${form.id || 'request'}. Server said: ${responseData.message || JSON.stringify(responseData)}`, data: responseData, status: response.status };
            }
            displayResponse(responseData, 'success');
            return responseData; // Return data for further processing if needed
        } catch (error) {
            handleError(error, form.id);
            throw error; // Re-throw to stop further processing in calling function
        }
    }

    // Update file input label
    document.querySelectorAll('.custom-file-input').forEach(input => {
        input.addEventListener('change', function(e) {
            var fileName = e.target.files[0] ? e.target.files[0].name : "未选择任何文件";
            var nextSibling = e.target.nextElementSibling;
            nextSibling.innerText = fileName;
        });
    });

    // Specific handler for Base64 upload
    document.getElementById('submitBase64Btn').addEventListener('click', async function(event) {
        const payload = {
            base64Data: document.getElementById('base64Data').value,
            dir: document.getElementById('base64Dir').value,
            filename: document.getElementById('base64Filename').value
        };
        if (!payload.base64Data || !payload.dir || !payload.filename) {
            displayResponse({error: "Base64 data, directory, and filename are required."}, "warning");
            return;
        }
        try {
            const response = await fetch(`${API_BASE_URL}/uploadBase64`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const responseData = await response.json();
            if (!response.ok) {
                throw { message: `HTTP error! status: ${response.status}`, data: responseData };
            }
            displayResponse(responseData, 'success');
        } catch (error) {
            handleError(error, 'formUploadBase64');
        }
    });


    // --- General File Uploads ---
    document.getElementById('formUpload').addEventListener('submit', (e) => handleFormSubmit(e, `/upload`));
    document.getElementById('formUploadByPath').addEventListener('submit', (e) => handleFormSubmit(e, `/uploadByPath`));
    document.getElementById('formUploadInputStream').addEventListener('submit', (e) => handleFormSubmit(e, `/uploadInputStream`));
    document.getElementById('formRemove').addEventListener('submit', (e) => handleFormSubmit(e, `/remove`));

    // --- Sharding Upload Logic ---
    function updateChunkUploadIdDisplay() {
        document.getElementById('chunkUploadIdInfo').textContent = currentChunkUploadId || 'N/A';
        const uploadIdInputs = [
            document.getElementById('chunkUploadId'),
            document.getElementById('completeUploadId'),
            document.getElementById('progressUploadId'),
            document.getElementById('cancelUploadId')
        ];
        uploadIdInputs.forEach(input => {
            if (input && currentChunkUploadId) input.value = currentChunkUploadId;
        });
    }

    document.getElementById('formInitMultipart').addEventListener('submit', async (e) => {
        try {
            const data = await handleFormSubmit(e, `/initMultipartUpload`);
            if (data && data.data && data.data.uploadId) { // Assuming R.ok() wraps data in 'data' field
                currentChunkUploadId = data.data.uploadId;
                updateChunkUploadIdDisplay();
                displayResponse(data.data, 'success'); // Display only the inner data object for clarity
            } else if (data && data.uploadId) { // If response is directly the map
                currentChunkUploadId = data.uploadId;
                updateChunkUploadIdDisplay();
                displayResponse(data, 'success');
            }
        } catch (error) { /* error already handled by handleFormSubmit */ }
    });

    document.getElementById('formUploadChunk').addEventListener('submit', (e) => handleFormSubmit(e, `/uploadChunk`));
    document.getElementById('formCompleteMultipart').addEventListener('submit', (e) => handleFormSubmit(e, `/completeMultipartUpload`));
    document.getElementById('formCancelMultipart').addEventListener('submit', async (e) => {
        try {
            await handleFormSubmit(e, `/cancelMultipartUpload`);
            currentChunkUploadId = null; // Clear upload ID on successful cancel
            updateChunkUploadIdDisplay();
        } catch (error) { /* error already handled */ }
    });

    document.getElementById('formGetMergeProgress').addEventListener('submit', async function(event) {
        event.preventDefault();
        const uploadId = document.getElementById('progressUploadId').value;
        const spinner = document.getElementById('progressSpinner');
        const progressDisplay = document.getElementById('mergeProgressDisplay');
        const progressBar = document.getElementById('progressBar');
        const progressStatus = document.getElementById('progressStatus');

        if (!uploadId) {
            displayResponse({ error: 'Upload ID is required for Get Merge Progress' }, 'warning');
            return;
        }
        spinner.style.display = 'inline-block';
        try {
            // Use a GET request with query parameter
            const response = await fetch(`${API_BASE_URL}/getMergeProgress?uploadId=${encodeURIComponent(uploadId)}`, { method: 'GET' });
            const responseData = await response.json();

            if (!response.ok) {
                throw { message: `HTTP error! status: ${response.status}`, data: responseData };
            }
            displayResponse(responseData, 'success'); // Show full JSON response

            // Update progress UI
            const progress = responseData.data || responseData; // Handle if wrapped in 'data' or not
            if (progress) {
                progressStatus.textContent = progress.status || 'N/A';
                const percent = progress.percent || 0;
                progressBar.style.width = `${percent}%`;
                progressBar.textContent = `${percent}%`;
                progressBar.setAttribute('aria-valuenow', percent);
                progressDisplay.style.display = 'block';

                if (progress.status === "COMPLETED" && progress.objectName) {
                    document.getElementById('streamObjectName').value = progress.objectName;
                }
            }

        } catch (error) {
            handleError(error, 'formGetMergeProgress');
            progressDisplay.style.display = 'none';
        } finally {
            spinner.style.display = 'none';
        }
    });


    // --- Text Uploads ---
    document.getElementById('formUploadText').addEventListener('submit', (e) => handleFormSubmit(e, `/uploadText`));
    document.getElementById('formUploadHtml').addEventListener('submit', (e) => handleFormSubmit(e, `/uploadHtml`));

    // --- Image Uploads ---
    document.getElementById('formUpload2Image').addEventListener('submit', (e) => handleFormSubmit(e, `/upload2Image`));

    // Proxy and Stream test functions
    function testProxy() {
        const objectName = document.getElementById('proxyObjectName').value;
        if (!objectName) {
            displayResponse({ error: 'Object Name for proxy is required.' }, 'warning', 3000);
            return;
        }
        const url = `${API_BASE_URL}/proxy/${objectName.replace(/^\//, '')}`; // Remove leading slash if any
        const link = document.getElementById('proxyLink');
        link.href = url;
        link.textContent = `打开代理: ${objectName}`;
        link.style.display = 'inline-block';
        displayResponse({ message: `代理 URL 已生成。点击链接测试。`, url: url }, 'info', 5000);
    }

    function testStream() {
        const objectName = document.getElementById('streamObjectName').value;
        if (!objectName) {
            displayResponse({ error: 'Object Name for stream is required.' }, 'warning', 3000);
            return;
        }
        const url = `${API_BASE_URL}/stream/${objectName.replace(/^\//, '')}`; // Remove leading slash if any
        const link = document.getElementById('streamLink');
        link.href = url;
        link.textContent = `打开流: ${objectName}`;
        link.style.display = 'inline-block';

        const videoPlayer = document.getElementById('videoPlayer');
        if (objectName.match(/\.(mp4|webm|ogv|mov|avi)$/i)) { // Added more common video types
            videoPlayer.src = url;
            videoPlayer.style.display = 'block';
            videoPlayer.load();
            displayResponse({ message: `流 URL 已生成。尝试在播放器中播放或点击链接。`, url: url }, 'info', 5000);
        } else {
            videoPlayer.style.display = 'none';
            displayResponse({ message: `流 URL 已生成。点击链接测试 (可能是下载或浏览器直接显示)。`, url: url }, 'info', 5000);
        }
    }
</script>
</body>
</html>