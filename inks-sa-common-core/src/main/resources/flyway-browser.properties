# Flyway ???????????
# ???????????????????????????

# ???????? (??)
# ??????????????
# flyway.browser.path=/usr/bin/google-chrome
# flyway.browser.path=/usr/bin/firefox
# flyway.browser.path=C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe

# ??OS ??????????:
# flyway.browser.path=/usr/bin/fnos-browser
# flyway.browser.path=/opt/fnos/browser/fnos-browser
# flyway.browser.path=/usr/local/bin/fnos-chrome

# ??Linux???????:
# flyway.browser.path=/usr/bin/chromium
# flyway.browser.path=/usr/bin/chromium-browser
# flyway.browser.path=/snap/bin/chromium

# macOS ??:
# flyway.browser.path=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
# flyway.browser.path=/Applications/Firefox.app/Contents/MacOS/firefox

# Windows ??:
# flyway.browser.path=C:\\Program Files\\Mozilla Firefox\\firefox.exe
# flyway.browser.path=C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe

# Docker????:
# ?Docker???????????????????:
# - HOST_IP: ???IP?? (??: localhost)
# - HOST_PORT: ????? (??: ????)
# - DOCKER_CONTAINER: ??Docker?? (????)

# Docker????:
# docker run -p 8080:8080 -e HOST_IP=localhost -e HOST_PORT=8080 your-image

# ????:
# 1. ????????????????????
# 2. ??????????: -Dflyway.browser.path=/path/to/browser
# 3. ??????????: export FLYWAY_BROWSER_PATH=/path/to/browser
# 4. Docker??: ?? docker-run-examples.sh ? docker-compose-example.yml
