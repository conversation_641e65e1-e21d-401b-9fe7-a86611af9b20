# Flyway 浏览器自动打开功能

## 功能说明

增强的浏览器自动打开功能，支持多种操作系统和环境，特别针对飞牛OS进行了优化。

## 支持的系统

- ✅ Windows (所有版本)
- ✅ macOS (所有版本)
- ✅ Linux (所有发行版)
- ✅ 飞牛OS (特别优化)
- ✅ Docker容器 (特别优化)
- ✅ 容器环境 (Kubernetes等)

## 自动检测的浏览器

### Windows
- Google Chrome
- Mozilla Firefox
- Microsoft Edge

### macOS
- Google Chrome
- Mozilla Firefox
- Safari
- Microsoft Edge

### Linux/飞牛OS
- google-chrome / google-chrome-stable
- chromium / chromium-browser
- firefox / mozilla-firefox
- opera
- konqueror
- epiphany
- midori
- fnos-browser (飞牛OS专用)
- fnos-chrome (飞牛OS专用)

## 配置自定义浏览器

### 方法1: 系统属性 (最高优先级)
```bash
java -Dflyway.browser.path=/path/to/browser -jar your-app.jar
```

### 方法2: 环境变量
```bash
export FLYWAY_BROWSER_PATH=/path/to/browser
java -jar your-app.jar
```

### 方法3: 配置文件 (最低优先级)
编辑 `flyway-browser.properties` 文件:
```properties
flyway.browser.path=/path/to/browser
```

## 飞牛OS 用户推荐配置

```bash
# 方法1: 环境变量
export FLYWAY_BROWSER_PATH=/usr/bin/fnos-browser

# 方法2: 系统属性
java -Dflyway.browser.path=/opt/fnos/browser/fnos-browser -jar your-app.jar
```

## Docker 环境配置

### 基本配置
```bash
# 基本端口映射
docker run -d \
  --name inks-sa-common \
  -p 8080:8080 \
  -e HOST_IP=localhost \
  -e HOST_PORT=8080 \
  your-registry/inks-sa-common:latest
```

### 远程访问配置
```bash
# 远程访问 (替换为实际IP)
docker run -d \
  --name inks-sa-common \
  -p 8080:8080 \
  -e HOST_IP=************* \
  -e HOST_PORT=8080 \
  your-registry/inks-sa-common:latest
```

### X11转发 (Linux宿主机)
```bash
# 允许X11连接
xhost +local:docker

# 运行容器
docker run -d \
  --name inks-sa-common \
  -p 8080:8080 \
  -e DISPLAY=$DISPLAY \
  -v /tmp/.X11-unix:/tmp/.X11-unix \
  -e HOST_IP=localhost \
  -e HOST_PORT=8080 \
  your-registry/inks-sa-common:latest
```

### Docker Compose
参考 `docker-compose-example.yml` 文件获取完整配置示例。

## 故障排除

### 通用问题
如果自动打开失败，系统会显示详细的帮助信息，包括:
- 系统环境信息
- 推荐的浏览器路径
- 配置方法说明

### Docker环境问题
1. **无法自动打开浏览器**
   - 正常现象，Docker容器无法直接打开宿主机浏览器
   - 手动访问: `http://localhost:8080/flyway-progress.html`

2. **端口无法访问**
   - 检查端口映射: `docker port container-name`
   - 检查防火墙设置
   - 确认HOST_IP和HOST_PORT配置正确

3. **X11转发失败**
   - 确保执行了 `xhost +local:docker`
   - 检查DISPLAY环境变量
   - 确认X11套接字挂载正确

## 技术实现

使用多层级的浏览器启动策略:
1. 自定义浏览器路径 (用户配置)
2. Java Desktop API
3. 系统命令 (xdg-open, open, rundll32等)
4. 常见浏览器路径遍历

## 调试信息

系统会输出详细的调试信息，包括:
- 操作系统类型
- Java版本
- Desktop API支持状态
- 飞牛OS检测结果
- Docker环境检测结果
- 每种启动方法的尝试结果

## 环境变量参考

### Docker专用环境变量
- `HOST_IP`: 宿主机IP地址 (默认: localhost)
- `HOST_PORT`: 宿主机端口 (默认: 容器端口)
- `DOCKER_CONTAINER`: Docker环境标识 (自动检测)
- `DISPLAY`: X11显示器 (Linux X11转发)

### 通用环境变量
- `FLYWAY_BROWSER_PATH`: 自定义浏览器路径
- `SPRING_PROFILES_ACTIVE`: Spring配置文件

## 文件参考

- `docker-run-examples.sh`: Docker运行命令示例
- `docker-compose-example.yml`: Docker Compose配置示例
- `flyway-browser.properties`: 浏览器配置文件
