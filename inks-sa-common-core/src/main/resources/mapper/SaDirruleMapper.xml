<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaDirruleMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaDirrulePojo">
        <include refid="selectSaDirruleVo"/>
        where Sa_DirRule.id = #{key} 
    </select>
    <sql id="selectSaDirruleVo">
         select
id, DirName, Description, PublicMark, BlackUserids, BlackUserNames, WhiteUserids, WhiteUserNames, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, De<PERSON><PERSON>, <PERSON>anti<PERSON>, Tenant<PERSON><PERSON>, Revision        from Sa_DirRule
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaDirrulePojo">
        <include refid="selectSaDirruleVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DirRule.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.dirname != null ">
   and Sa_DirRule.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_DirRule.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.blackuserids != null ">
   and Sa_DirRule.BlackUserids like concat('%', #{SearchPojo.blackuserids}, '%')
</if>
<if test="SearchPojo.blackusernames != null ">
   and Sa_DirRule.BlackUserNames like concat('%', #{SearchPojo.blackusernames}, '%')
</if>
<if test="SearchPojo.whiteuserids != null ">
   and Sa_DirRule.WhiteUserids like concat('%', #{SearchPojo.whiteuserids}, '%')
</if>
<if test="SearchPojo.whiteusernames != null ">
   and Sa_DirRule.WhiteUserNames like concat('%', #{SearchPojo.whiteusernames}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DirRule.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DirRule.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DirRule.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DirRule.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DirRule.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Sa_DirRule.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_DirRule.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_DirRule.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_DirRule.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_DirRule.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_DirRule.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_DirRule.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_DirRule.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_DirRule.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_DirRule.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_DirRule.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_DirRule.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DirRule.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.dirname != null ">
   or Sa_DirRule.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_DirRule.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.blackuserids != null ">
   or Sa_DirRule.BlackUserids like concat('%', #{SearchPojo.blackuserids}, '%')
</if>
<if test="SearchPojo.blackusernames != null ">
   or Sa_DirRule.BlackUserNames like concat('%', #{SearchPojo.blackusernames}, '%')
</if>
<if test="SearchPojo.whiteuserids != null ">
   or Sa_DirRule.WhiteUserids like concat('%', #{SearchPojo.whiteuserids}, '%')
</if>
<if test="SearchPojo.whiteusernames != null ">
   or Sa_DirRule.WhiteUserNames like concat('%', #{SearchPojo.whiteusernames}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DirRule.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DirRule.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DirRule.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DirRule.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DirRule.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Sa_DirRule.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_DirRule.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_DirRule.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_DirRule.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_DirRule.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_DirRule.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_DirRule.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_DirRule.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_DirRule.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_DirRule.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_DirRule.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_DirRule.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DirRule.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DirRule(id, DirName, Description, PublicMark, BlackUserids, BlackUserNames, WhiteUserids, WhiteUserNames, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{dirname}, #{description}, #{publicmark}, #{blackuserids}, #{blackusernames}, #{whiteuserids}, #{whiteusernames}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{modulecode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DirRule
        <set>
            <if test="dirname != null ">
                DirName =#{dirname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="blackuserids != null ">
                BlackUserids =#{blackuserids},
            </if>
            <if test="blackusernames != null ">
                BlackUserNames =#{blackusernames},
            </if>
            <if test="whiteuserids != null ">
                WhiteUserids =#{whiteuserids},
            </if>
            <if test="whiteusernames != null ">
                WhiteUserNames =#{whiteusernames},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DirRule where id = #{key} 
    </delete>

    <select id="getList" resultType="inks.sa.common.core.domain.pojo.SaDirrulePojo">
        <include refid="selectSaDirruleVo"/>
        where 1 = 1
    </select>
</mapper>

