<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaDeptMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaDeptPojo">
        <include refid="selectSaDeptVo"/>
        where Sa_Dept.id = #{key} 
    </select>
    <sql id="selectSaDeptVo">
         select
id, Parentid, Ancestors, DeptCode, DeptName, EnabledMark, Leader, Phone, Email, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_Dept
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaDeptPojo">
        <include refid="selectSaDeptVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Dept.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.parentid != null ">
   and Sa_Dept.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.ancestors != null ">
   and Sa_Dept.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   and Sa_Dept.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_Dept.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.leader != null ">
   and Sa_Dept.Leader like concat('%', #{SearchPojo.leader}, '%')
</if>
<if test="SearchPojo.phone != null ">
   and Sa_Dept.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Sa_Dept.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Dept.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Dept.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Dept.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Dept.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Dept.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Dept.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Dept.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Dept.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Dept.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Dept.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or Sa_Dept.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.ancestors != null ">
   or Sa_Dept.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   or Sa_Dept.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_Dept.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.leader != null ">
   or Sa_Dept.Leader like concat('%', #{SearchPojo.leader}, '%')
</if>
<if test="SearchPojo.phone != null ">
   or Sa_Dept.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Sa_Dept.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Dept.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Dept.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Dept.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Dept.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Dept.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Dept.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Dept.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Dept.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Dept.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Dept.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Dept(id, Parentid, Ancestors, DeptCode, DeptName, EnabledMark, Leader, Phone, Email, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{parentid}, #{ancestors}, #{deptcode}, #{deptname}, #{enabledmark}, #{leader}, #{phone}, #{email}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Dept
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="ancestors != null ">
                Ancestors =#{ancestors},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="leader != null ">
                Leader =#{leader},
            </if>
            <if test="phone != null ">
                Phone =#{phone},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Dept where id = #{key} 
    </delete>

    <select id="getListByParentid" resultType="inks.sa.common.core.domain.pojo.SaDeptPojo">
        <include refid="selectSaDeptVo"/>
        where Parentid = #{parentid}
    </select>
</mapper>

