<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SadgformatitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SadgformatitemPojo">
        select id,
        Pid,
        ItemCode,
        ItemName,
        DefWidth,
        MinWidth,
        DisplayMark,
        Fixed,
        Sortable,
        OrderField,
        Overflow,
        Formatter,
        ClassName,
        AlignType,
        EventName,
        EditMark,
        OperationMark,
        RowNum,
        Remark,
        Tenantid,
        Revision
        from Sa_DgFormatItem
        where Sa_DgFormatItem.id = #{key}
        and Sa_DgFormatItem.Tenantid = #{tid}
    </select>
    <sql id="selectSadgformatitemVo">
        select id,
               Pid,
               <PERSON>em<PERSON><PERSON>,
               ItemName,
               Def<PERSON>idth,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>splayMark,
               Fixed,
               Sortable,
               OrderField,
               Overflow,
               Formatter,
               ClassName,
               AlignType,
               EventName,
               EditMark,
               OperationMark,
               RowNum,
               Remark,
               Tenantid,
               Revision
        from Sa_DgFormatItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SadgformatitemPojo">
        <include refid="selectSadgformatitemVo"/>
        where 1 = 1 and Sa_DgFormatItem.Tenantid =#{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DgFormatItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_DgFormatItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and Sa_DgFormatItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and Sa_DgFormatItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
            and Sa_DgFormatItem.defwidth like concat('%', #{SearchPojo.defwidth}, '%')
        </if>
        <if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
            and Sa_DgFormatItem.minwidth like concat('%', #{SearchPojo.minwidth}, '%')
        </if>
        <if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
            and Sa_DgFormatItem.orderfield like concat('%', #{SearchPojo.orderfield}, '%')
        </if>
        <if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
            and Sa_DgFormatItem.formatter like concat('%', #{SearchPojo.formatter}, '%')
        </if>
        <if test="SearchPojo.classname != null and SearchPojo.classname != ''">
            and Sa_DgFormatItem.classname like concat('%', #{SearchPojo.classname}, '%')
        </if>
        <if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
            and Sa_DgFormatItem.aligntype like concat('%', #{SearchPojo.aligntype}, '%')
        </if>
        <if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
            and Sa_DgFormatItem.eventname like concat('%', #{SearchPojo.eventname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_DgFormatItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_DgFormatItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or Sa_DgFormatItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or Sa_DgFormatItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
                or Sa_DgFormatItem.DefWidth like concat('%', #{SearchPojo.defwidth}, '%')
            </if>
            <if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
                or Sa_DgFormatItem.MinWidth like concat('%', #{SearchPojo.minwidth}, '%')
            </if>
            <if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
                or Sa_DgFormatItem.OrderField like concat('%', #{SearchPojo.orderfield}, '%')
            </if>
            <if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
                or Sa_DgFormatItem.Formatter like concat('%', #{SearchPojo.formatter}, '%')
            </if>
            <if test="SearchPojo.classname != null and SearchPojo.classname != ''">
                or Sa_DgFormatItem.ClassName like concat('%', #{SearchPojo.classname}, '%')
            </if>
            <if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
                or Sa_DgFormatItem.AlignType like concat('%', #{SearchPojo.aligntype}, '%')
            </if>
            <if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
                or Sa_DgFormatItem.EventName like concat('%', #{SearchPojo.eventname}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_DgFormatItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.sa.common.core.domain.pojo.SadgformatitemPojo">
        select id,
        Pid,
        ItemCode,
        ItemName,
        DefWidth,
        MinWidth,
        DisplayMark,
        Fixed,
        Sortable,
        OrderField,
        Overflow,
        Formatter,
        ClassName,
        AlignType,
        EventName,
        EditMark,
        OperationMark,
        RowNum,
        Remark,
        Tenantid,
        Revision
        from Sa_DgFormatItem
        where Sa_DgFormatItem.Pid = #{Pid}
        and Sa_DgFormatItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_DgFormatItem(id, Pid, ItemCode, ItemName, DefWidth, MinWidth, DisplayMark, Fixed, Sortable,
        OrderField, Overflow, Formatter, ClassName, AlignType, EventName, EditMark,
        OperationMark, RowNum, Remark, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemcode}, #{itemname}, #{defwidth}, #{minwidth}, #{displaymark}, #{fixed},
        #{sortable}, #{orderfield}, #{overflow}, #{formatter}, #{classname}, #{aligntype}, #{eventname},
        #{editmark}, #{operationmark}, #{rownum}, #{remark}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DgFormatItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="itemcode != null">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null">
                ItemName = #{itemname},
            </if>
            <if test="defwidth != null">
                DefWidth = #{defwidth},
            </if>
            <if test="minwidth != null">
                MinWidth = #{minwidth},
            </if>
            <if test="displaymark != null">
                DisplayMark = #{displaymark},
            </if>
            <if test="fixed != null">
                Fixed = #{fixed},
            </if>
            <if test="sortable != null">
                Sortable = #{sortable},
            </if>
            <if test="orderfield != null">
                OrderField = #{orderfield},
            </if>
            <if test="overflow != null">
                Overflow = #{overflow},
            </if>
            <if test="formatter != null">
                Formatter = #{formatter},
            </if>
            <if test="classname != null">
                ClassName = #{classname},
            </if>
            <if test="aligntype != null">
                AlignType = #{aligntype},
            </if>
            <if test="eventname != null">
                EventName = #{eventname},
            </if>
            <if test="editmark != null">
                EditMark = #{editmark},
            </if>
            <if test="operationmark != null">
                OperationMark = #{operationmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_DgFormatItem
        where id = #{key}
        and Tenantid = #{tid}
    </delete>
</mapper>

