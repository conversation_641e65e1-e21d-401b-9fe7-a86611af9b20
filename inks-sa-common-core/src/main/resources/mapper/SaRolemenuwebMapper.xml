<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaRolemenuwebMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaRolemenuwebPojo">
        <include refid="selectSaRolemenuwebVo"/>
        where Sa_RoleMenuWeb.id = #{key} 
    </select>
    <sql id="selectSaRolemenuwebVo">
         select
id, <PERSON><PERSON>, <PERSON>vid, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>yid, <PERSON>reate<PERSON>ate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Sa_RoleMenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaRolemenuwebPojo">
        <include refid="selectSaRolemenuwebVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_RoleMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.roleid != null ">
   and Sa_RoleMenuWeb.Roleid like concat('%', #{SearchPojo.roleid}, '%')
</if>
<if test="SearchPojo.navid != null ">
   and Sa_RoleMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_RoleMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_RoleMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_RoleMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_RoleMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_RoleMenuWeb.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.roleid != null ">
   or Sa_RoleMenuWeb.Roleid like concat('%', #{SearchPojo.roleid}, '%')
</if>
<if test="SearchPojo.navid != null ">
   or Sa_RoleMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_RoleMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_RoleMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_RoleMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_RoleMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_RoleMenuWeb.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_RoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{roleid}, #{navid}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_RoleMenuWeb
        <set>
            <if test="roleid != null ">
                Roleid =#{roleid},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_RoleMenuWeb where id = #{key} 
    </delete>

    <select id="getNavidsByUserid" resultType="java.lang.String">
        SELECT DISTINCT Sa_RoleMenuWeb.Navid
        FROM Sa_RoleMenuWeb
        JOIN Sa_UserRole ON Sa_RoleMenuWeb.Roleid = Sa_UserRole.Roleid
        WHERE Sa_UserRole.Userid = #{userid}    </select>

    <select id="getNavidsByRoleid" resultType="java.lang.String">
        SELECT DISTINCT Navid
        FROM Sa_RoleMenuWeb
        WHERE Roleid = #{roleid}
    </select>

    <delete id="batchDelete">
        delete from Sa_RoleMenuWeb where Sa_RoleMenuWeb.Navid in
        <foreach collection="deleteNavids" item="navid" open="(" separator="," close=")">
            #{navid}
        </foreach>
        and Sa_RoleMenuWeb.Roleid=#{roleid}
    </delete>

    <select id="batchInsert" resultType="java.lang.Integer">
        insert into Sa_RoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values
        <foreach collection="rolemenuwebPojoList" item="pojo" separator=",">
            (
            #{pojo.id}, #{pojo.roleid}, #{pojo.navid}, #{pojo.rownum}, #{pojo.createby}, #{pojo.createbyid}, #{pojo.createdate}, #{pojo.lister}, #{pojo.listerid}, #{pojo.modifydate}, #{pojo.tenantid}, #{pojo.tenantname}, #{pojo.revision}
            )
        </foreach>
    </select>
</mapper>

