<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaCompanyMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaCompanyPojo">
        select
        id, Name, EnglishName, CreditCode, Address, BankAccount, BankOfDeposit, ContactPerson, Tel, Remark, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision from Sa_Company
        where Sa_Company.id = #{key}
    </select>
    <sql id="selectSaCompanyVo">
        select id,
               Name,
               EnglishName,
               CreditCode,
               Address,
               BankAccount,
               BankOfDeposit,
               ContactPerson,
               Tel,
               Remark,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON>d,
               <PERSON>difyDate,
               <PERSON>antid,
               TenantName,
               Revision
        from Sa_Company
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaCompanyPojo">
        <include refid="selectSaCompanyVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Company.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.name != null">
            and Sa_Company.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.englishname != null">
            and Sa_Company.EnglishName like concat('%', #{SearchPojo.englishname}, '%')
        </if>
        <if test="SearchPojo.creditcode != null">
            and Sa_Company.CreditCode like concat('%', #{SearchPojo.creditcode}, '%')
        </if>
        <if test="SearchPojo.address != null">
            and Sa_Company.Address like concat('%', #{SearchPojo.address}, '%')
        </if>
        <if test="SearchPojo.bankaccount != null">
            and Sa_Company.BankAccount like concat('%', #{SearchPojo.bankaccount}, '%')
        </if>
        <if test="SearchPojo.bankofdeposit != null">
            and Sa_Company.BankOfDeposit like concat('%', #{SearchPojo.bankofdeposit}, '%')
        </if>
        <if test="SearchPojo.contactperson != null">
            and Sa_Company.ContactPerson like concat('%', #{SearchPojo.contactperson}, '%')
        </if>
        <if test="SearchPojo.tel != null">
            and Sa_Company.Tel like concat('%', #{SearchPojo.tel}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Company.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Company.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Company.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Company.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Company.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Company.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.name != null">
                or Sa_Company.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.englishname != null">
                or Sa_Company.EnglishName like concat('%', #{SearchPojo.englishname}, '%')
            </if>
            <if test="SearchPojo.creditcode != null">
                or Sa_Company.CreditCode like concat('%', #{SearchPojo.creditcode}, '%')
            </if>
            <if test="SearchPojo.address != null">
                or Sa_Company.Address like concat('%', #{SearchPojo.address}, '%')
            </if>
            <if test="SearchPojo.bankaccount != null">
                or Sa_Company.BankAccount like concat('%', #{SearchPojo.bankaccount}, '%')
            </if>
            <if test="SearchPojo.bankofdeposit != null">
                or Sa_Company.BankOfDeposit like concat('%', #{SearchPojo.bankofdeposit}, '%')
            </if>
            <if test="SearchPojo.contactperson != null">
                or Sa_Company.ContactPerson like concat('%', #{SearchPojo.contactperson}, '%')
            </if>
            <if test="SearchPojo.tel != null">
                or Sa_Company.Tel like concat('%', #{SearchPojo.tel}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Company.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Company.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Company.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Company.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Company.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Company.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Company(id, Name, EnglishName, CreditCode, Address, BankAccount, BankOfDeposit, ContactPerson,
        Tel, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{name}, #{englishname}, #{creditcode}, #{address}, #{bankaccount}, #{bankofdeposit},
        #{contactperson}, #{tel}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
        #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Company
        <set>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="englishname != null">
                EnglishName =#{englishname},
            </if>
            <if test="creditcode != null">
                CreditCode =#{creditcode},
            </if>
            <if test="address != null">
                Address =#{address},
            </if>
            <if test="bankaccount != null">
                BankAccount =#{bankaccount},
            </if>
            <if test="bankofdeposit != null">
                BankOfDeposit =#{bankofdeposit},
            </if>
            <if test="contactperson != null">
                ContactPerson =#{contactperson},
            </if>
            <if test="tel != null">
                Tel =#{tel},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Company where id = #{key}
    </delete>
</mapper>

