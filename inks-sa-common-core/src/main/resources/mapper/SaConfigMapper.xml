<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaConfigMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
        where Sa_Config.id = #{key}
    </select>
    <sql id="selectSaConfigVo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgOption,
               CfgIcon,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               Remark,
               CreateBy,
               CreateByid,
               <PERSON>reate<PERSON>ate,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               Revision,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10
        from Sa_Config
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Config.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <select id="getEntityByCfgKey" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
        where CfgKey=#{cfgKey}
    </select>
    <select id="getEntityByKeyUser" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
        where Sa_Config.CfgKey = #{key}
        and Sa_Config.Userid = #{userid}
    </select>
    <select id="getEntityByKey" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
        where Sa_Config.CfgKey = #{key}
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null">
            and Sa_Config.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.cfgname != null">
            and Sa_Config.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
        </if>
        <if test="SearchPojo.cfgkey != null">
            and Sa_Config.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
        </if>
        <if test="SearchPojo.cfgvalue != null">
            and Sa_Config.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
        </if>
        <if test="SearchPojo.cfgoption != null">
            and Sa_Config.CfgOption like concat('%', #{SearchPojo.cfgoption}, '%')
        </if>
        <if test="SearchPojo.cfgicon != null">
            and Sa_Config.CfgIcon like concat('%', #{SearchPojo.cfgicon}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Config.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Config.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Config.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Config.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Config.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Config.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Config.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Config.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Config.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Config.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Config.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Config.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Config.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Config.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Config.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null">
                or Sa_Config.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.cfgname != null">
                or Sa_Config.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
            </if>
            <if test="SearchPojo.cfgkey != null">
                or Sa_Config.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
            </if>
            <if test="SearchPojo.cfgvalue != null">
                or Sa_Config.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
            </if>
            <if test="SearchPojo.cfgoption != null">
                or Sa_Config.CfgOption like concat('%', #{SearchPojo.cfgoption}, '%')
            </if>
            <if test="SearchPojo.cfgicon != null">
                or Sa_Config.CfgIcon like concat('%', #{SearchPojo.cfgicon}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Config.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Config.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Config.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Config.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Config.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Config.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Config.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Config.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Config.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Config.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Config.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Config.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Config.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Config.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Config.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Config(id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon,
        AllowUi, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Userid,Tenantid,
        Revision, Custom6, Custom7, Custom8, Custom9, Custom10)
        values (#{id}, #{parentid}, #{cfgname}, #{cfgkey}, #{cfgvalue}, #{cfgtype}, #{cfglevel}, #{ctrltype},
        #{cfgoption}, #{cfgicon}, #{allowui}, #{rownum}, #{enabledmark}, #{allowdelete}, #{remark}, #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{userid},#{tenantid}, #{revision}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
        #{custom10})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Config
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="cfgname != null">
                CfgName =#{cfgname},
            </if>
            <if test="cfgkey != null">
                CfgKey =#{cfgkey},
            </if>
            <if test="cfgvalue != null">
                CfgValue =#{cfgvalue},
            </if>
            <if test="cfgtype != null">
                CfgType =#{cfgtype},
            </if>
            <if test="cfglevel != null">
                CfgLevel =#{cfglevel},
            </if>
            <if test="ctrltype != null">
                CtrlType =#{ctrltype},
            </if>
            <if test="cfgoption != null">
                CfgOption =#{cfgoption},
            </if>
            <if test="cfgicon != null">
                CfgIcon =#{cfgicon},
            </if>
            <if test="allowui != null">
                AllowUi =#{allowui},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Config
        where id = #{key}
    </delete>

    <select id="getSystemRegistrkey" resultType="java.lang.String">
        select CfgValue
        from Sa_Config
        where CfgKey = 'system.registrkey'
    </select>

    <select id="getCfgValueByCfgKey" resultType="java.lang.String">
        select CfgValue
        from Sa_Config
        where CfgKey = #{cfgKey}
    </select>

    <select id="getAllList" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        <include refid="selectSaConfigVo"/>
    </select>
</mapper>

