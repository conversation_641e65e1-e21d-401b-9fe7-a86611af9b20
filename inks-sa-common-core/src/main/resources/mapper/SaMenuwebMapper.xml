<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaMenuwebMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaMenuwebPojo">
        <include refid="selectSaMenuwebVo"/>
        where Sa_MenuWeb.Navid = #{key} 
    </select>
    <sql id="selectSaMenuwebVo">
         select
Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl, NavigateUrl, MvcUrl, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, Remark, PermissionCode, Functionid, FunctionCode, FunctionName, Lister, <PERSON>reateDate, ModifyDate, DeleteMark, DeleteLister, DeleteDate        from Sa_MenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaMenuwebPojo">
        <include refid="selectSaMenuwebVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_MenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.navpid != null ">
   and Sa_MenuWeb.NavPid like concat('%', #{SearchPojo.navpid}, '%')
</if>
<if test="SearchPojo.navtype != null ">
   and Sa_MenuWeb.NavType like concat('%', #{SearchPojo.navtype}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   and Sa_MenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   and Sa_MenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navgroup != null ">
   and Sa_MenuWeb.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   and Sa_MenuWeb.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.iconurl != null ">
   and Sa_MenuWeb.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
</if>
<if test="SearchPojo.navigateurl != null ">
   and Sa_MenuWeb.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
</if>
<if test="SearchPojo.mvcurl != null ">
   and Sa_MenuWeb.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
</if>
<if test="SearchPojo.moduletype != null ">
   and Sa_MenuWeb.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Sa_MenuWeb.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rolecode != null ">
   and Sa_MenuWeb.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
</if>
<if test="SearchPojo.imageindex != null ">
   and Sa_MenuWeb.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
</if>
<if test="SearchPojo.imagestyle != null ">
   and Sa_MenuWeb.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_MenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   and Sa_MenuWeb.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   and Sa_MenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and Sa_MenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and Sa_MenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_MenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   and Sa_MenuWeb.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.navpid != null ">
   or Sa_MenuWeb.NavPid like concat('%', #{SearchPojo.navpid}, '%')
</if>
<if test="SearchPojo.navtype != null ">
   or Sa_MenuWeb.NavType like concat('%', #{SearchPojo.navtype}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   or Sa_MenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   or Sa_MenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navgroup != null ">
   or Sa_MenuWeb.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   or Sa_MenuWeb.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.iconurl != null ">
   or Sa_MenuWeb.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
</if>
<if test="SearchPojo.navigateurl != null ">
   or Sa_MenuWeb.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
</if>
<if test="SearchPojo.mvcurl != null ">
   or Sa_MenuWeb.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
</if>
<if test="SearchPojo.moduletype != null ">
   or Sa_MenuWeb.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Sa_MenuWeb.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rolecode != null ">
   or Sa_MenuWeb.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
</if>
<if test="SearchPojo.imageindex != null ">
   or Sa_MenuWeb.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
</if>
<if test="SearchPojo.imagestyle != null ">
   or Sa_MenuWeb.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_MenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   or Sa_MenuWeb.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   or Sa_MenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or Sa_MenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or Sa_MenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_MenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.deletelister != null ">
   or Sa_MenuWeb.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_MenuWeb(Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl, NavigateUrl, MvcUrl, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, Remark, PermissionCode, Functionid, FunctionCode, FunctionName, Lister, CreateDate, ModifyDate, DeleteMark, DeleteLister, DeleteDate)
        values (#{navid}, #{navpid}, #{navtype}, #{navcode}, #{navname}, #{navgroup}, #{rownum}, #{imagecss}, #{iconurl}, #{navigateurl}, #{mvcurl}, #{moduletype}, #{modulecode}, #{rolecode}, #{imageindex}, #{imagestyle}, #{enabledmark}, #{remark}, #{permissioncode}, #{functionid}, #{functioncode}, #{functionname}, #{lister}, #{createdate}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletedate})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_MenuWeb
        <set>
            <if test="navpid != null ">
                NavPid =#{navpid},
            </if>
            <if test="navtype != null ">
                NavType =#{navtype},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navgroup != null ">
                NavGroup =#{navgroup},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="iconurl != null ">
                IconUrl =#{iconurl},
            </if>
            <if test="navigateurl != null ">
                NavigateUrl =#{navigateurl},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="moduletype != null ">
                ModuleType =#{moduletype},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="imageindex != null ">
                ImageIndex =#{imageindex},
            </if>
            <if test="imagestyle != null ">
                ImageStyle =#{imagestyle},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
        </set>
        where Navid = #{navid} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_MenuWeb where Navid = #{key} 
    </delete>

    <select id="getListByPid" resultType="inks.sa.common.core.domain.pojo.SaMenuwebPojo">
        <include refid="selectSaMenuwebVo"/>
        where Sa_MenuWeb.NavPid = #{key}
        order by Sa_MenuWeb.RowNum
    </select>

    <select id="getListAll" resultType="inks.sa.common.core.domain.pojo.SaMenuwebPojo">
        <include refid="selectSaMenuwebVo"/>
        order by Sa_MenuWeb.RowNum
    </select>

    <select id="getListByNavids" resultType="inks.sa.common.core.domain.pojo.SaMenuwebPojo">
        <include refid="selectSaMenuwebVo"/>
        where Sa_MenuWeb.Navid in
        <foreach collection="navids" item="navid" open="(" separator="," close=")">
            #{navid}
        </foreach>
        order by Sa_MenuWeb.RowNum
    </select>
</mapper>

