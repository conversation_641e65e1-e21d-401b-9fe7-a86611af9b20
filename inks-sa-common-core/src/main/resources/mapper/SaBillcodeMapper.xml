<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaBillcodeMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaBillcodePojo">
        SELECT Sa_BillCode.id,
        Sa_BillCode.ModuleCode,
        Sa_BillCode.BillName,
        Sa_BillCode.Prefix1,
        Sa_BillCode.Suffix1,
        Sa_BillCode.Prefix2,
        Sa_BillCode.Suffix2,
        Sa_BillCode.Prefix3,
        Sa_BillCode.Suffix3,
        Sa_BillCode.Prefix4,
        Sa_BillCode.Suffix4,
        Sa_BillCode.Prefix5,
        Sa_BillCode.Suffix5,
        Sa_BillCode.CountType,
        Sa_BillCode.Step,
        Sa_BillCode.CurrentNum,
        Sa_BillCode.TableName,
        Sa_BillCode.DateColumn,
        Sa_BillCode.ColumnName,
        Sa_BillCode.DbFilter,
        Sa_BillCode.AllowEdit,
        Sa_BillCode.AllowDelete,
        Sa_BillCode.Param1,
        Sa_BillCode.Param2,
        Sa_BillCode.Param3,
        Sa_BillCode.Param4,
        Sa_BillCode.Param5,
        Sa_BillCode.Remark,
        Sa_BillCode.Lister,
        Sa_BillCode.CreateDate,
        Sa_BillCode.ModifyDate,
        Sa_BillCode.Tenantid
        FROM Sa_BillCode
        where Sa_BillCode.id = #{key}
    </select>
    <sql id="selectSaBillcodeVo">
        SELECT Sa_BillCode.id,
               Sa_BillCode.ModuleCode,
               Sa_BillCode.BillName,
               Sa_BillCode.Prefix1,
               Sa_BillCode.Suffix1,
               Sa_BillCode.Prefix2,
               Sa_BillCode.Suffix2,
               Sa_BillCode.Prefix3,
               Sa_BillCode.Suffix3,
               Sa_BillCode.Prefix4,
               Sa_BillCode.Suffix4,
               Sa_BillCode.Prefix5,
               Sa_BillCode.Suffix5,
               Sa_BillCode.CountType,
               Sa_BillCode.Step,
               Sa_BillCode.CurrentNum,
               Sa_BillCode.TableName,
               Sa_BillCode.DateColumn,
               Sa_BillCode.ColumnName,
               Sa_BillCode.DbFilter,
               Sa_BillCode.AllowEdit,
               Sa_BillCode.AllowDelete,
               Sa_BillCode.Param1,
               Sa_BillCode.Param2,
               Sa_BillCode.Param3,
               Sa_BillCode.Param4,
               Sa_BillCode.Param5,
               Sa_BillCode.Remark,
               Sa_BillCode.Lister,
               Sa_BillCode.CreateDate,
               Sa_BillCode.ModifyDate,
               Sa_BillCode.Tenantid
        FROM Sa_BillCode
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaBillcodePojo">
        <include refid="selectSaBillcodeVo"/>
        where 1 = 1
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_BillCode.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            and Sa_BillCode.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billname != null and SearchPojo.billname != ''">
            and Sa_BillCode.BillName like concat('%', #{SearchPojo.billname}, '%')
        </if>
        <if test="SearchPojo.prefix1 != null and SearchPojo.prefix1 != ''">
            and Sa_BillCode.Prefix1 like concat('%', #{SearchPojo.prefix1}, '%')
        </if>
        <if test="SearchPojo.suffix1 != null and SearchPojo.suffix1 != ''">
            and Sa_BillCode.Suffix1 like concat('%', #{SearchPojo.suffix1}, '%')
        </if>
        <if test="SearchPojo.prefix2 != null and SearchPojo.prefix2 != ''">
            and Sa_BillCode.Prefix2 like concat('%', #{SearchPojo.prefix2}, '%')
        </if>
        <if test="SearchPojo.suffix2 != null and SearchPojo.suffix2 != ''">
            and Sa_BillCode.Suffix2 like concat('%', #{SearchPojo.suffix2}, '%')
        </if>
        <if test="SearchPojo.prefix3 != null and SearchPojo.prefix3 != ''">
            and Sa_BillCode.Prefix3 like concat('%', #{SearchPojo.prefix3}, '%')
        </if>
        <if test="SearchPojo.suffix3 != null and SearchPojo.suffix3 != ''">
            and Sa_BillCode.Suffix3 like concat('%', #{SearchPojo.suffix3}, '%')
        </if>
        <if test="SearchPojo.prefix4 != null and SearchPojo.prefix4 != ''">
            and Sa_BillCode.Prefix4 like concat('%', #{SearchPojo.prefix4}, '%')
        </if>
        <if test="SearchPojo.suffix4 != null and SearchPojo.suffix4 != ''">
            and Sa_BillCode.Suffix4 like concat('%', #{SearchPojo.suffix4}, '%')
        </if>
        <if test="SearchPojo.prefix5 != null and SearchPojo.prefix5 != ''">
            and Sa_BillCode.Prefix5 like concat('%', #{SearchPojo.prefix5}, '%')
        </if>
        <if test="SearchPojo.suffix5 != null and SearchPojo.suffix5 != ''">
            and Sa_BillCode.Suffix5 like concat('%', #{SearchPojo.suffix5}, '%')
        </if>
        <if test="SearchPojo.counttype != null and SearchPojo.counttype != ''">
            and Sa_BillCode.CountType like concat('%', #{SearchPojo.counttype}, '%')
        </if>
        <if test="SearchPojo.tablename != null and SearchPojo.tablename != ''">
            and Sa_BillCode.TableName like concat('%', #{SearchPojo.tablename}, '%')
        </if>
        <if test="SearchPojo.datecolumn != null and SearchPojo.datecolumn != ''">
            and Sa_BillCode.DateColumn like concat('%', #{SearchPojo.datecolumn}, '%')
        </if>
        <if test="SearchPojo.columnname != null and SearchPojo.columnname != ''">
            and Sa_BillCode.ColumnName like concat('%', #{SearchPojo.columnname}, '%')
        </if>
        <if test="SearchPojo.dbfilter != null and SearchPojo.dbfilter != ''">
            and Sa_BillCode.DbFilter like concat('%', #{SearchPojo.dbfilter}, '%')
        </if>
        <if test="SearchPojo.param1 != null and SearchPojo.param1 != ''">
            and Sa_BillCode.Param1 like concat('%', #{SearchPojo.param1}, '%')
        </if>
        <if test="SearchPojo.param2 != null and SearchPojo.param2 != ''">
            and Sa_BillCode.Param2 like concat('%', #{SearchPojo.param2}, '%')
        </if>
        <if test="SearchPojo.param3 != null and SearchPojo.param3 != ''">
            and Sa_BillCode.Param3 like concat('%', #{SearchPojo.param3}, '%')
        </if>
        <if test="SearchPojo.param4 != null and SearchPojo.param4 != ''">
            and Sa_BillCode.Param4 like concat('%', #{SearchPojo.param4}, '%')
        </if>
        <if test="SearchPojo.param5 != null and SearchPojo.param5 != ''">
            and Sa_BillCode.Param5 like concat('%', #{SearchPojo.param5}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_BillCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Sa_BillCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            or Sa_BillCode.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billname != null and SearchPojo.billname != ''">
            or Sa_BillCode.BillName like concat('%', #{SearchPojo.billname}, '%')
        </if>
        <if test="SearchPojo.prefix1 != null and SearchPojo.prefix1 != ''">
            or Sa_BillCode.Prefix1 like concat('%', #{SearchPojo.prefix1}, '%')
        </if>
        <if test="SearchPojo.suffix1 != null and SearchPojo.suffix1 != ''">
            or Sa_BillCode.Suffix1 like concat('%', #{SearchPojo.suffix1}, '%')
        </if>
        <if test="SearchPojo.prefix2 != null and SearchPojo.prefix2 != ''">
            or Sa_BillCode.Prefix2 like concat('%', #{SearchPojo.prefix2}, '%')
        </if>
        <if test="SearchPojo.suffix2 != null and SearchPojo.suffix2 != ''">
            or Sa_BillCode.Suffix2 like concat('%', #{SearchPojo.suffix2}, '%')
        </if>
        <if test="SearchPojo.prefix3 != null and SearchPojo.prefix3 != ''">
            or Sa_BillCode.Prefix3 like concat('%', #{SearchPojo.prefix3}, '%')
        </if>
        <if test="SearchPojo.suffix3 != null and SearchPojo.suffix3 != ''">
            or Sa_BillCode.Suffix3 like concat('%', #{SearchPojo.suffix3}, '%')
        </if>
        <if test="SearchPojo.prefix4 != null and SearchPojo.prefix4 != ''">
            or Sa_BillCode.Prefix4 like concat('%', #{SearchPojo.prefix4}, '%')
        </if>
        <if test="SearchPojo.suffix4 != null and SearchPojo.suffix4 != ''">
            or Sa_BillCode.Suffix4 like concat('%', #{SearchPojo.suffix4}, '%')
        </if>
        <if test="SearchPojo.prefix5 != null and SearchPojo.prefix5 != ''">
            or Sa_BillCode.Prefix5 like concat('%', #{SearchPojo.prefix5}, '%')
        </if>
        <if test="SearchPojo.suffix5 != null and SearchPojo.suffix5 != ''">
            or Sa_BillCode.Suffix5 like concat('%', #{SearchPojo.suffix5}, '%')
        </if>
        <if test="SearchPojo.counttype != null and SearchPojo.counttype != ''">
            or Sa_BillCode.CountType like concat('%', #{SearchPojo.counttype}, '%')
        </if>
        <if test="SearchPojo.tablename != null and SearchPojo.tablename != ''">
            or Sa_BillCode.TableName like concat('%', #{SearchPojo.tablename}, '%')
        </if>
        <if test="SearchPojo.datecolumn != null and SearchPojo.datecolumn != ''">
            or Sa_BillCode.DateColumn like concat('%', #{SearchPojo.datecolumn}, '%')
        </if>
        <if test="SearchPojo.columnname != null and SearchPojo.columnname != ''">
            or Sa_BillCode.ColumnName like concat('%', #{SearchPojo.columnname}, '%')
        </if>
        <if test="SearchPojo.dbfilter != null and SearchPojo.dbfilter != ''">
            or Sa_BillCode.DbFilter like concat('%', #{SearchPojo.dbfilter}, '%')
        </if>
        <if test="SearchPojo.param1 != null and SearchPojo.param1 != ''">
            or Sa_BillCode.Param1 like concat('%', #{SearchPojo.param1}, '%')
        </if>
        <if test="SearchPojo.param2 != null and SearchPojo.param2 != ''">
            or Sa_BillCode.Param2 like concat('%', #{SearchPojo.param2}, '%')
        </if>
        <if test="SearchPojo.param3 != null and SearchPojo.param3 != ''">
            or Sa_BillCode.Param3 like concat('%', #{SearchPojo.param3}, '%')
        </if>
        <if test="SearchPojo.param4 != null and SearchPojo.param4 != ''">
            or Sa_BillCode.Param4 like concat('%', #{SearchPojo.param4}, '%')
        </if>
        <if test="SearchPojo.param5 != null and SearchPojo.param5 != ''">
            or Sa_BillCode.Param5 like concat('%', #{SearchPojo.param5}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or Sa_BillCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or Sa_BillCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_BillCode(id, ModuleCode, BillName, Prefix1, Suffix1, Prefix2, Suffix2, Prefix3, Suffix3, Prefix4,
        Suffix4, Prefix5, Suffix5, CountType, Step, CurrentNum, TableName, DateColumn,
        ColumnName, DbFilter, AllowEdit, AllowDelete, Param1, Param2, Param3, Param4, Param5,
        Remark, Lister, CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{modulecode}, #{billname}, #{prefix1}, #{suffix1}, #{prefix2}, #{suffix2}, #{prefix3},
        #{suffix3}, #{prefix4}, #{suffix4}, #{prefix5}, #{suffix5}, #{counttype}, #{step}, #{currentnum},
        #{tablename}, #{datecolumn}, #{columnname}, #{dbfilter}, #{allowedit}, #{allowdelete}, #{param1},
        #{param2}, #{param3}, #{param4}, #{param5}, #{remark}, #{lister}, #{createdate}, #{modifydate},
        #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_BillCode
        <set>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="billname != null">
                BillName =#{billname},
            </if>
            <if test="prefix1 != null">
                Prefix1 =#{prefix1},
            </if>
            <if test="suffix1 != null">
                Suffix1 =#{suffix1},
            </if>
            <if test="prefix2 != null">
                Prefix2 =#{prefix2},
            </if>
            <if test="suffix2 != null">
                Suffix2 =#{suffix2},
            </if>
            <if test="prefix3 != null">
                Prefix3 =#{prefix3},
            </if>
            <if test="suffix3 != null">
                Suffix3 =#{suffix3},
            </if>
            <if test="prefix4 != null">
                Prefix4 =#{prefix4},
            </if>
            <if test="suffix4 != null">
                Suffix4 =#{suffix4},
            </if>
            <if test="prefix5 != null">
                Prefix5 =#{prefix5},
            </if>
            <if test="suffix5 != null">
                Suffix5 =#{suffix5},
            </if>
            <if test="counttype != null">
                CountType =#{counttype},
            </if>
            <if test="step != null">
                Step =#{step},
            </if>
            <if test="currentnum != null">
                CurrentNum =#{currentnum},
            </if>
            <if test="tablename != null">
                TableName =#{tablename},
            </if>
            <if test="datecolumn != null">
                DateColumn =#{datecolumn},
            </if>
            <if test="columnname != null">
                ColumnName =#{columnname},
            </if>
            <if test="dbfilter != null">
                DbFilter =#{dbfilter},
            </if>
            <if test="allowedit != null">
                AllowEdit =#{allowedit},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="param1 != null">
                Param1 =#{param1},
            </if>
            <if test="param2 != null">
                Param2 =#{param2},
            </if>
            <if test="param3 != null">
                Param3 =#{param3},
            </if>
            <if test="param4 != null">
                Param4 =#{param4},
            </if>
            <if test="param5 != null">
                Param5 =#{param5},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantid != null">
                Tenantid =#{tenantid},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_BillCode
        where id = #{key}
    </delete>

    <select id="getEntityByModuleCode" resultType="inks.sa.common.core.domain.SaBillcodeEntity">
        <include refid="selectSaBillcodeVo"/>
        where Sa_BillCode.ModuleCode=#{ModuleCode}
        <if test="tid != null and tid != ''">
            and Sa_BillCode.Tenantid = #{tid}
        </if>
    </select>

    <select id="getSerialNo" resultType="java.util.HashMap">
        select IFNULL(max(substring(RefNo,${subindex})),1) as RefNo from ${entity.TableName}
        where
        <if test="entity.CountType == 'year'">
            DATE_FORMAT(${entity.DateColumn}, '%Y')= DATE_FORMAT(#{currentDate}, '%Y')
        </if>
        <if test="entity.CountType == 'month'">
            DATE_FORMAT(${entity.DateColumn}, '%Y-%m')= DATE_FORMAT(#{currentDate}, '%Y-%m')
        </if>
        <if test="entity.CountType == 'day'">
            DATE_FORMAT(${entity.DateColumn}, '%Y-%m-%d')= DATE_FORMAT(#{currentDate}, '%Y-%m-%d')
        </if>
        <if test="entity.Dbfilter != null and entity.Dbfilter != ''">
            ${entity.Dbfilter}
        </if>
        Order by CreateDate Desc
        LIMIT 1
    </select>

    <select id="getMaxCode" resultType="java.lang.String">
            SELECT ${column}
            FROM ${tablename}
            WHERE 1=1
            ORDER BY ${column} DESC
            LIMIT 1
    </select>
</mapper>

