<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaWebnavMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaWebnavPojo">
        select
        Navid, NavCode, NavName, NavContent, RowNum, EnabledMark, PermissionCode, Remark, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Revision from Sa_WebNav
        where Sa_WebNav.Navid = #{key}
    </select>
    <sql id="selectSaWebnavVo">
        select Navid,
               NavCode,
               NavName,
               NavContent,
               RowNum,
               EnabledMark,
               PermissionCode,
               Remark,
               Create<PERSON><PERSON>,
               <PERSON><PERSON><PERSON>yid,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Revision
        from Sa_WebNav
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaWebnavPojo">
        <include refid="selectSaWebnavVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_WebNav.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.navcode != null">
            and Sa_WebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null">
            and Sa_WebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navcontent != null">
            and Sa_WebNav.NavContent like concat('%', #{SearchPojo.navcontent}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null">
            and Sa_WebNav.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_WebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_WebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_WebNav.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_WebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_WebNav.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.navcode != null">
                or Sa_WebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null">
                or Sa_WebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.navcontent != null">
                or Sa_WebNav.NavContent like concat('%', #{SearchPojo.navcontent}, '%')
            </if>
            <if test="SearchPojo.permissioncode != null">
                or Sa_WebNav.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_WebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_WebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_WebNav.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_WebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_WebNav.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_WebNav(Navid, NavCode, NavName, NavContent, RowNum, EnabledMark, PermissionCode, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{navid}, #{navcode}, #{navname}, #{navcontent}, #{rownum}, #{enabledmark}, #{permissioncode},
        #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_WebNav
        <set>
            <if test="navcode != null">
                NavCode =#{navcode},
            </if>
            <if test="navname != null">
                NavName =#{navname},
            </if>
            <if test="navcontent != null">
                NavContent =#{navcontent},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="permissioncode != null">
                PermissionCode =#{permissioncode},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where Navid = #{navid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_WebNav where Navid = #{key}
    </delete>
</mapper>

