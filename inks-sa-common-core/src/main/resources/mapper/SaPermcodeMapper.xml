<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaPermcodeMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaPermcodePojo">
        <include refid="selectSaPermcodeVo"/>
        where Sa_PermCode.Permid = #{key}
    </select>
    <sql id="selectSaPermcodeVo">
        select Permid,
               Parentid,
               PermType,
               PermCode,
               PermName,
               RowNum,
               IsPublic,
               EnabledMark,
               AllowDelete,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from Sa_PermCode
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaPermcodePojo">
        <include refid="selectSaPermcodeVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_PermCode.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.permid != null">
            and Sa_PermCode.Permid like concat('%', #{SearchPojo.permid}, '%')
        </if>
        <if test="SearchPojo.parentid != null">
            and Sa_PermCode.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.permtype != null">
            and Sa_PermCode.PermType like concat('%', #{SearchPojo.permtype}, '%')
        </if>
        <if test="SearchPojo.permcode != null">
            and Sa_PermCode.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null">
            and Sa_PermCode.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_PermCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_PermCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_PermCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_PermCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_PermCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.permid != null">
                or Sa_PermCode.Permid like concat('%', #{SearchPojo.permid}, '%')
            </if>
            <if test="SearchPojo.parentid != null">
                or Sa_PermCode.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.permtype != null">
                or Sa_PermCode.PermType like concat('%', #{SearchPojo.permtype}, '%')
            </if>
            <if test="SearchPojo.permcode != null">
                or Sa_PermCode.PermCode like concat('%', #{SearchPojo.permcode}, '%')
            </if>
            <if test="SearchPojo.permname != null">
                or Sa_PermCode.PermName like concat('%', #{SearchPojo.permname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_PermCode.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_PermCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_PermCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_PermCode.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_PermCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_PermCode(Permid, Parentid, PermType, PermCode, PermName, RowNum, IsPublic, EnabledMark,
        AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{permid}, #{parentid}, #{permtype}, #{permcode}, #{permname}, #{rownum}, #{ispublic}, #{enabledmark},
        #{allowdelete}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_PermCode
        <set>
            <if test="permid != null">
                Permid =#{permid},
            </if>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="permtype != null">
                PermType =#{permtype},
            </if>
            <if test="permcode != null">
                PermCode =#{permcode},
            </if>
            <if test="permname != null">
                PermName =#{permname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="ispublic != null">
                IsPublic =#{ispublic},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where Permid= #{permid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_PermCode where Permid= #{key}
    </delete>
</mapper>

