<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaValidatorMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaValidatorPojo">
        <include refid="selectSaValidatorVo"/>
        where Sa_Validator.id = #{key} 
    </select>
    <sql id="selectSaValidatorVo">
         select
id, ValiCode, ValiTitle, SqlMark, SqlStr, Expression, TipMsg, TipMsgEn, RequiredMark, ItemLoopMark, EnabledMark, RowNum, Remark, CreateBy, <PERSON>reate<PERSON>yid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, TenantName, Revision        from Sa_Validator
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaValidatorPojo">
        <include refid="selectSaValidatorVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Validator.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.valicode != null ">
   and Sa_Validator.ValiCode like concat('%', #{SearchPojo.valicode}, '%')
</if>
<if test="SearchPojo.valititle != null ">
   and Sa_Validator.ValiTitle like concat('%', #{SearchPojo.valititle}, '%')
</if>
<if test="SearchPojo.sqlstr != null ">
   and Sa_Validator.SqlStr like concat('%', #{SearchPojo.sqlstr}, '%')
</if>
<if test="SearchPojo.expression != null ">
   and Sa_Validator.Expression like concat('%', #{SearchPojo.expression}, '%')
</if>
<if test="SearchPojo.tipmsg != null ">
   and Sa_Validator.TipMsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null ">
   and Sa_Validator.TipMsgEn like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Validator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Validator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Validator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Validator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Validator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Validator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Validator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Validator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Validator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Validator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Validator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.valicode != null ">
   or Sa_Validator.ValiCode like concat('%', #{SearchPojo.valicode}, '%')
</if>
<if test="SearchPojo.valititle != null ">
   or Sa_Validator.ValiTitle like concat('%', #{SearchPojo.valititle}, '%')
</if>
<if test="SearchPojo.sqlstr != null ">
   or Sa_Validator.SqlStr like concat('%', #{SearchPojo.sqlstr}, '%')
</if>
<if test="SearchPojo.expression != null ">
   or Sa_Validator.Expression like concat('%', #{SearchPojo.expression}, '%')
</if>
<if test="SearchPojo.tipmsg != null ">
   or Sa_Validator.TipMsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null ">
   or Sa_Validator.TipMsgEn like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Validator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Validator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Validator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Validator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Validator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Validator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Validator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Validator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Validator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Validator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Validator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Validator(id, ValiCode, ValiTitle, SqlMark, SqlStr, Expression, TipMsg, TipMsgEn, RequiredMark, ItemLoopMark, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{valicode}, #{valititle}, #{sqlmark}, #{sqlstr}, #{expression}, #{tipmsg}, #{tipmsgen}, #{requiredmark}, #{itemloopmark}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Validator
        <set>
            <if test="valicode != null ">
                ValiCode =#{valicode},
            </if>
            <if test="valititle != null ">
                ValiTitle =#{valititle},
            </if>
            <if test="sqlmark != null">
                SqlMark =#{sqlmark},
            </if>
            <if test="sqlstr != null ">
                SqlStr =#{sqlstr},
            </if>
            <if test="expression != null ">
                Expression =#{expression},
            </if>
            <if test="tipmsg != null ">
                TipMsg =#{tipmsg},
            </if>
            <if test="tipmsgen != null ">
                TipMsgEn =#{tipmsgen},
            </if>
            <if test="requiredmark != null">
                RequiredMark =#{requiredmark},
            </if>
            <if test="itemloopmark != null">
                ItemLoopMark =#{itemloopmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Validator where id = #{key} 
    </delete>

    <select id="getListByValicodeEnabled" resultType="inks.sa.common.core.domain.pojo.SaValidatorPojo">
        <include refid="selectSaValidatorVo"/>
        where ValiCode = #{valicode}
        and EnabledMark = 1 order by RowNum
    </select>
</mapper>

