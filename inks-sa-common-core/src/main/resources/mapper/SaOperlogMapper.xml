<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaOperlogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaOperlogPojo">
        <include refid="selectSaOperlogVo"/>
        where Sa_OperLog.id = #{key} 
    </select>
    <sql id="selectSaOperlogVo">
         select
id, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid        from Sa_OperLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaOperlogPojo">
        <include refid="selectSaOperlogVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_OperLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.opertitle != null ">
   and Sa_OperLog.OperTitle like concat('%', #{SearchPojo.opertitle}, '%')
</if>
<if test="SearchPojo.method != null ">
   and Sa_OperLog.Method like concat('%', #{SearchPojo.method}, '%')
</if>
<if test="SearchPojo.requestmethod != null ">
   and Sa_OperLog.RequestMethod like concat('%', #{SearchPojo.requestmethod}, '%')
</if>
<if test="SearchPojo.operuserid != null ">
   and Sa_OperLog.OperUserid like concat('%', #{SearchPojo.operuserid}, '%')
</if>
<if test="SearchPojo.opername != null ">
   and Sa_OperLog.OperName like concat('%', #{SearchPojo.opername}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_OperLog.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.operurl != null ">
   and Sa_OperLog.OperUrl like concat('%', #{SearchPojo.operurl}, '%')
</if>
<if test="SearchPojo.operip != null ">
   and Sa_OperLog.OperIp like concat('%', #{SearchPojo.operip}, '%')
</if>
<if test="SearchPojo.operlocation != null ">
   and Sa_OperLog.OperLocation like concat('%', #{SearchPojo.operlocation}, '%')
</if>
<if test="SearchPojo.operparam != null ">
   and Sa_OperLog.OperParam like concat('%', #{SearchPojo.operparam}, '%')
</if>
<if test="SearchPojo.jsonresult != null ">
   and Sa_OperLog.JsonResult like concat('%', #{SearchPojo.jsonresult}, '%')
</if>
<if test="SearchPojo.errormsg != null ">
   and Sa_OperLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.opertitle != null ">
   or Sa_OperLog.OperTitle like concat('%', #{SearchPojo.opertitle}, '%')
</if>
<if test="SearchPojo.method != null ">
   or Sa_OperLog.Method like concat('%', #{SearchPojo.method}, '%')
</if>
<if test="SearchPojo.requestmethod != null ">
   or Sa_OperLog.RequestMethod like concat('%', #{SearchPojo.requestmethod}, '%')
</if>
<if test="SearchPojo.operuserid != null ">
   or Sa_OperLog.OperUserid like concat('%', #{SearchPojo.operuserid}, '%')
</if>
<if test="SearchPojo.opername != null ">
   or Sa_OperLog.OperName like concat('%', #{SearchPojo.opername}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_OperLog.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.operurl != null ">
   or Sa_OperLog.OperUrl like concat('%', #{SearchPojo.operurl}, '%')
</if>
<if test="SearchPojo.operip != null ">
   or Sa_OperLog.OperIp like concat('%', #{SearchPojo.operip}, '%')
</if>
<if test="SearchPojo.operlocation != null ">
   or Sa_OperLog.OperLocation like concat('%', #{SearchPojo.operlocation}, '%')
</if>
<if test="SearchPojo.operparam != null ">
   or Sa_OperLog.OperParam like concat('%', #{SearchPojo.operparam}, '%')
</if>
<if test="SearchPojo.jsonresult != null ">
   or Sa_OperLog.JsonResult like concat('%', #{SearchPojo.jsonresult}, '%')
</if>
<if test="SearchPojo.errormsg != null ">
   or Sa_OperLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_OperLog(id, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName, DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg, OperTime, Tenantid)
        values (#{id}, #{opertitle}, #{businesstype}, #{method}, #{requestmethod}, #{operatortype}, #{operuserid}, #{opername}, #{deptname}, #{operurl}, #{operip}, #{operlocation}, #{operparam}, #{jsonresult}, #{status}, #{errormsg}, #{opertime}, #{tenantid})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_OperLog
        <set>
            <if test="opertitle != null ">
                OperTitle =#{opertitle},
            </if>
            <if test="businesstype != null">
                BusinessType =#{businesstype},
            </if>
            <if test="method != null ">
                Method =#{method},
            </if>
            <if test="requestmethod != null ">
                RequestMethod =#{requestmethod},
            </if>
            <if test="operatortype != null">
                OperatorType =#{operatortype},
            </if>
            <if test="operuserid != null ">
                OperUserid =#{operuserid},
            </if>
            <if test="opername != null ">
                OperName =#{opername},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="operurl != null ">
                OperUrl =#{operurl},
            </if>
            <if test="operip != null ">
                OperIp =#{operip},
            </if>
            <if test="operlocation != null ">
                OperLocation =#{operlocation},
            </if>
            <if test="operparam != null ">
                OperParam =#{operparam},
            </if>
            <if test="jsonresult != null ">
                JsonResult =#{jsonresult},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="errormsg != null ">
                ErrorMsg =#{errormsg},
            </if>
            <if test="opertime != null">
                OperTime =#{opertime},
            </if>
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_OperLog where id = #{key} 
    </delete>

    <delete id="deleteByTime">
        delete from Sa_OperLog where OperTime between #{dateRange.StartDate} and #{dateRange.EndDate}
    </delete>
</mapper>

