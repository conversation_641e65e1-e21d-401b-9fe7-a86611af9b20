<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaReportsMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        where Sa_Reports.id = #{key}
    </select>
    <sql id="selectSaReportsVo">
         select
id, GenGroupid, ModuleCode, RptType, RptName, RptData, PageRow, TempUrl, FileName, PrinterSn, RowNum, EnabledMark, GrfData, PaperLength, PaperWidth, Remark, CreateBy, <PERSON>reate<PERSON>yid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenanti<PERSON>, TenantName, Revision        from Sa_Reports
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Reports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>


    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Reports.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_Reports.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rpttype != null">
            and Sa_Reports.RptType like concat('%', #{SearchPojo.rpttype}, '%')
        </if>
        <if test="SearchPojo.rptname != null">
            and Sa_Reports.RptName like concat('%', #{SearchPojo.rptname}, '%')
        </if>
        <if test="SearchPojo.rptdata != null">
            and Sa_Reports.RptData like concat('%', #{SearchPojo.rptdata}, '%')
        </if>
        <if test="SearchPojo.tempurl != null">
            and Sa_Reports.TempUrl like concat('%', #{SearchPojo.tempurl}, '%')
        </if>
        <if test="SearchPojo.filename != null">
            and Sa_Reports.FileName like concat('%', #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.printersn != null">
            and Sa_Reports.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
        </if>
        <if test="SearchPojo.grfdata != null">
            and Sa_Reports.GrfData like concat('%', #{SearchPojo.grfdata}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Reports.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Reports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Reports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Reports.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Reports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Reports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Reports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Reports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Reports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Reports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Reports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Reports.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_Reports.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.rpttype != null">
                or Sa_Reports.RptType like concat('%', #{SearchPojo.rpttype}, '%')
            </if>
            <if test="SearchPojo.rptname != null">
                or Sa_Reports.RptName like concat('%', #{SearchPojo.rptname}, '%')
            </if>
            <if test="SearchPojo.rptdata != null">
                or Sa_Reports.RptData like concat('%', #{SearchPojo.rptdata}, '%')
            </if>
            <if test="SearchPojo.tempurl != null">
                or Sa_Reports.TempUrl like concat('%', #{SearchPojo.tempurl}, '%')
            </if>
            <if test="SearchPojo.filename != null">
                or Sa_Reports.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.printersn != null">
                or Sa_Reports.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
            </if>
            <if test="SearchPojo.grfdata != null">
                or Sa_Reports.GrfData like concat('%', #{SearchPojo.grfdata}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Reports.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Reports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Reports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Reports.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Reports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Reports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Reports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Reports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Reports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Reports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Reports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Reports(id, GenGroupid, ModuleCode, RptType, RptName, RptData, PageRow, TempUrl, FileName, PrinterSn, RowNum, EnabledMark, GrfData, PaperLength, PaperWidth, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{rpttype}, #{rptname}, #{rptdata}, #{pagerow}, #{tempurl}, #{filename}, #{printersn}, #{rownum}, #{enabledmark}, #{grfdata}, #{paperlength}, #{paperwidth}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Reports
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="rpttype != null">
                RptType =#{rpttype},
            </if>
            <if test="rptname != null">
                RptName =#{rptname},
            </if>
            <if test="rptdata != null">
                RptData =#{rptdata},
            </if>
            <if test="pagerow != null">
                PageRow =#{pagerow},
            </if>
            <if test="tempurl != null">
                TempUrl =#{tempurl},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="printersn != null">
                PrinterSn =#{printersn},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="grfdata != null">
                GrfData =#{grfdata},
            </if>
            <if test="paperlength != null">
                PaperLength =#{paperlength},
            </if>
            <if test="paperwidth != null">
                PaperWidth =#{paperwidth},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Reports
        where id = #{key}
    </delete>

    <!--查询指定行数据-->
    <select id="getListByDef" resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        # where Sa_Reports.Tenantid = 'default'
        where 1=1
        <if test="moduleCode != null">
            and Sa_Reports.ModuleCode =#{moduleCode}
        </if>
        order by ModuleCode
    </select>
    <select id="getEntityByNameCode" resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        where ModuleCode = #{moduleCode}
        and rptname=#{rptname}
        Order by RowNum LIMIT 1
    </select>


    <!--查询指定行数据-->
    <select id="getPageListAll" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and CiReports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <select id="getListByModuleCode" resultType="inks.sa.common.core.domain.pojo.SaReportsPojo">
        <include refid="selectSaReportsVo"/>
        where ModuleCode = #{moduleCode}
        Order by RowNum
    </select>



</mapper>

