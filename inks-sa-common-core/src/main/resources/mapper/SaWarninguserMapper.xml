<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaWarninguserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaWarninguserPojo">
        <include refid="selectSaWarninguserVo"/>
        where Sa_WarningUser.id = #{key} 
    </select>
    <sql id="selectSaWarninguserVo">
        SELECT Sa_WarningUser.id,
               Sa_WarningUser.Warnid,
               Sa_WarningUser.DiffNum,
               Sa_WarningUser.RowNum,
               Sa_WarningUser.Remark,
               Sa_WarningUser.CreateBy,
               Sa_WarningUser.CreateByid,
               Sa_WarningUser.CreateDate,
               Sa_WarningUser.Lister,
               Sa_WarningUser.Listerid,
               Sa_WarningUser.ModifyDate,
               Sa_WarningUser.Userid,
               Sa_WarningUser.RealName,
               Sa_WarningUser.Tenantid,
               Sa_WarningUser.TenantName,
               Sa_WarningUser.Revision,
               Sa_Warning.WarnCode,
               Sa_Warning.WarnName,
               Sa_Warning.WarnField,
               Sa_Warning.SvcCode,
               Sa_Warning.WarnApi,
               Sa_Warning.WebPath,
               Sa_Warning.ImageCss,
               Sa_Warning.TagTitle,
               Sa_Warning.PermCode
        FROM Sa_WarningUser
                 LEFT JOIN Sa_Warning ON Sa_WarningUser.Warnid = Sa_Warning.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaWarninguserPojo">
        <include refid="selectSaWarninguserVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_WarningUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.warnid != null ">
   and Sa_WarningUser.Warnid like concat('%', #{SearchPojo.warnid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_WarningUser.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_WarningUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_WarningUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_WarningUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_WarningUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.userid != null ">
   and Sa_WarningUser.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and Sa_WarningUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_WarningUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.warnid != null ">
   or Sa_WarningUser.Warnid like concat('%', #{SearchPojo.warnid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_WarningUser.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_WarningUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_WarningUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_WarningUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_WarningUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.userid != null ">
   or Sa_WarningUser.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or Sa_WarningUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_WarningUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_WarningUser(id, Warnid, DiffNum, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Userid, RealName, Tenantid, TenantName, Revision)
        values (#{id}, #{warnid}, #{diffnum}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{userid}, #{realname}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_WarningUser
        <set>
            <if test="warnid != null ">
                Warnid =#{warnid},
            </if>
            <if test="diffnum != null">
                DiffNum =#{diffnum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_WarningUser where id = #{key} 
    </delete>
    
    <select id="getListByUser" resultType="inks.sa.common.core.domain.pojo.SaWarninguserPojo">
        <include refid="selectSaWarninguserVo"/>
        where Sa_WarningUser.Userid =#{userid}
        Order by RowNum
    </select>

</mapper>

