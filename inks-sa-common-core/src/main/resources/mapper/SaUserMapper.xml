<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="inks.sa.common.core.mapper.SaUserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        where Sa_User.id = #{key}
    </select>
    <sql id="selectSaUserVo">
        select Sa_User.id,
               Sa_User.UserName,
               Sa_User.RealName,
               Sa_User.Password,
               Sa_User.Phone,
               Sa_User.Email,
               Sa_User.EmailAuthCode,
               Sa_User.Sex,
               Sa_User.Avatar,
               Sa_User.DirName,
               Sa_User.FileName,
               Sa_User.RoleType,
               Sa_User.AdminMark,
               Sa_User.UserState,
               Sa_User.Remark,
               Sa_User.CreateBy,
               Sa_User.CreateDate,
               Sa_User.Lister,
               Sa_User.ModifyDate,
               Sa_User.Revision,
               auth_openid.AuthUuid as wxopenid,
               auth_wxe.AuthUuid as wxeuserid,
               auth_ding.AuthUuid as dinguserid
        from Sa_User
                 LEFT JOIN Sa_JustAuth AS auth_openid ON Sa_User.id = auth_openid.UserId and auth_openid.AuthType = 'openid'
                 LEFT JOIN Sa_JustAuth AS auth_wxe ON Sa_User.id = auth_wxe.UserId and auth_wxe.AuthType = 'wxe'
                 LEFT JOIN Sa_JustAuth AS auth_ding ON Sa_User.id = auth_ding.UserId and auth_ding.AuthType = 'ding'
    </sql>


    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_User.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null">
            and Sa_User.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_User.RealName like concat('%',
            #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.phone != null">
            and Sa_User.Phone like concat('%',
            #{SearchPojo.phone}, '%')
        </if>
        <if test="SearchPojo.email != null">
            and Sa_User.Email like concat('%',
            #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.avatar != null">
            and Sa_User.Avatar like concat('%',
            #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_User.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_User.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_User.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null">
                or Sa_User.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_User.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.phone != null">
                or Sa_User.Phone like concat('%', #{SearchPojo.phone}, '%')
            </if>
            <if test="SearchPojo.email != null">
                or Sa_User.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.avatar != null">
                or Sa_User.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_User.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_User.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_User.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_User(id, UserName, RealName, Password, Phone, Email, EmailAuthCode, Sex, Avatar, DirName, FileName, RoleType, AdminMark, UserState, Remark, CreateBy, CreateDate, Lister, ModifyDate, Revision)
        values (#{id}, #{username}, #{realname}, #{password}, #{phone}, #{email}, #{emailauthcode}, #{sex}, #{avatar}, #{dirname}, #{filename}, #{roletype}, #{adminmark}, #{userstate}, #{remark}, #{createby}, #{createdate}, #{lister}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_User
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="password != null ">
                Password =#{password},
            </if>
            <if test="phone != null ">
                Phone =#{phone},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="emailauthcode != null ">
                EmailAuthCode =#{emailauthcode},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="dirname != null ">
                DirName =#{dirname},
            </if>
            <if test="filename != null ">
                FileName =#{filename},
            </if>
            <if test="roletype != null">
                RoleType =#{roletype},
            </if>
            <if test="adminmark != null">
                AdminMark =#{adminmark},
            </if>
            <if test="userstate != null">
                UserState =#{userstate},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_User
        where id = #{key}
    </delete>

    <select id="getEntityByOpenid" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE auth_openid.AuthUuid = #{openid}
    </select>

    <select id="getUserInfo" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.id = #{id}
    </select>

    <select id="getEntityByUNameAndPass" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE LOWER(Sa_User.Username) = LOWER(#{username})
        and Sa_User.Password = #{password}
        ORDER BY Sa_User.CreateDate DESC limit 1
    </select>

    <select id="checkPasswordByUserid" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.id = #{userid}
        and Password = #{password}
        ORDER BY CreateDate DESC
        limit 1
    </select>

    <select id="getAdminMarkByUserid" resultType="int">
        select AdminMark
        from Sa_User
        where id = #{userid}
    </select>


    <update id="updateAdminPassword">
        update Sa_User
        set Password = #{encryptPassword}
        where UserName = 'admin'
    </update>

    <select id="checkUsername" resultType="int">
        select count(1)
        from Sa_User
        where LOWER(Sa_User.Username) = LOWER(#{username})
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="getEntityByDingUserid" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE auth_ding.AuthUuid = #{dinguserid}
    </select>

    <select id="getEntityByJustAuth" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        where Sa_User.id = (select Userid from Sa_JustAuth where AuthUuid = #{authuuid} and AuthType = #{authtype})
    </select>

    <select id="getLoginUserByJustAuth" resultType="inks.common.core.domain.LoginUser">
        select Sa_User.id        AS userid,
               Sa_User.UserName,
               Sa_User.RealName,
               ''                AS password,
               Sa_User.AdminMark AS isadmin
        from Sa_User
        where Sa_User.id = (select Userid from Sa_JustAuth where AuthUuid = #{authuuid} and AuthType = #{authtype})
    </select>

    <select id="getUserInfosByUserids" resultType="inks.sa.common.core.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.id in
        <foreach collection="useridList" item="userid" index="index" open="(" separator="," close=")">
            #{userid}
        </foreach>
    </select>

    <select id="countUser" resultType="int">
        select count(1) from Sa_User
    </select>

    <select id="getUserIds" resultType="java.lang.String">
        select id from Sa_User
    </select>

    <select id="checkUseridUsed" resultType="java.lang.String">
    </select>
</mapper>