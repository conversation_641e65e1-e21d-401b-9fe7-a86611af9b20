<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaBillgroupMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaBillgroupPojo">
        select id,
        Parentid,
        ModuleCode,
        GroupCode,
        GroupName,
        EnabledMark,
        RowNum,
        Remark,
        Lister,
        CreateDate,
        ModifyDate,
        Tenantid
        from Sa_BillGroup
        where Sa_BillGroup.id = #{key}
    </select>
    <sql id="selectSaBillgroupVo">
        select id,
               Parentid,
               ModuleCode,
               GroupCode,
               GroupName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               Tenantid
        from Sa_BillGroup
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaBillgroupPojo">
        <include refid="selectSaBillgroupVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_BillGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null">
            and Sa_BillGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_BillGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Sa_BillGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_BillGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_BillGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_BillGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null">
                or Sa_BillGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_BillGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Sa_BillGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_BillGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_BillGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_BillGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_BillGroup(id, Parentid, ModuleCode, GroupCode, GroupName, EnabledMark, RowNum, Remark, Lister,
        CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{parentid}, #{modulecode}, #{groupcode}, #{groupname}, #{enabledmark}, #{rownum}, #{remark},
        #{lister}, #{createdate}, #{modifydate}, #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_BillGroup
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="groupcode != null">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_BillGroup
        where id = #{key}
    </delete>

    <!--获得EntityByModuleCode-->
    <select id="getListByModuleCode" resultType="inks.sa.common.core.domain.pojo.SaBillgroupPojo">
        select id,
        Parentid,
        ModuleCode,
        GroupCode,
        GroupName,
        EnabledMark,
        RowNum,
        Remark,
        Lister,
        CreateDate,
        ModifyDate,
        Tenantid
        from Sa_BillGroup
        where ModuleCode = #{moduleCode}
        Order by RowNum
    </select>
</mapper>

