<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaPermissionMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaPermissionPojo">
        <include refid="selectSaPermissionVo"/>
        where Sa_Permission.id = #{key}
    </select>
    <sql id="selectSaPermissionVo">
        select id,
               ResourceType,
               Resourceid,
               Permid,
               PermCode,
               PermName,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from Sa_Permission
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaPermissionPojo">
        <include refid="selectSaPermissionVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Permission.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.id != null">
            and Sa_Permission.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.resourcetype != null">
            and Sa_Permission.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
        </if>
        <if test="SearchPojo.resourceid != null">
            and Sa_Permission.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
        </if>
        <if test="SearchPojo.permid != null">
            and Sa_Permission.Permid like concat('%', #{SearchPojo.permid}, '%')
        </if>
        <if test="SearchPojo.permcode != null">
            and Sa_Permission.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null">
            and Sa_Permission.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Permission.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Permission.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Permission.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Permission.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Permission.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.id != null">
                or Sa_Permission.id like concat('%', #{SearchPojo.id}, '%')
            </if>
            <if test="SearchPojo.resourcetype != null">
                or Sa_Permission.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
            </if>
            <if test="SearchPojo.resourceid != null">
                or Sa_Permission.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
            </if>
            <if test="SearchPojo.permid != null">
                or Sa_Permission.Permid like concat('%', #{SearchPojo.permid}, '%')
            </if>
            <if test="SearchPojo.permcode != null">
                or Sa_Permission.PermCode like concat('%', #{SearchPojo.permcode}, '%')
            </if>
            <if test="SearchPojo.permname != null">
                or Sa_Permission.PermName like concat('%', #{SearchPojo.permname}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Permission.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Permission.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Permission.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Permission.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Permission.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Permission(id, ResourceType, Resourceid, Permid, PermCode, PermName, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{resourcetype}, #{resourceid}, #{permid}, #{permcode}, #{permname}, #{createby}, #{createbyid},
        #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Permission
        <set>
            <if test="id != null">
                id =#{id},
            </if>
            <if test="resourcetype != null">
                ResourceType =#{resourcetype},
            </if>
            <if test="resourceid != null">
                Resourceid =#{resourceid},
            </if>
            <if test="permid != null">
                Permid =#{permid},
            </if>
            <if test="permcode != null">
                PermCode =#{permcode},
            </if>
            <if test="permname != null">
                PermName =#{permname},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id= #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Permission where id= #{key}
    </delete>


    <!--按角色查询权限-->
    <select id="getListByRole" resultType="inks.sa.common.core.domain.pojo.SaPermissionPojo">
        SELECT Sa_Permission.id,
        Sa_Permission.ResourceType,
        Sa_Permission.Resourceid,
        Sa_Permission.Permid,
        Sa_Permission.CreateBy,
        Sa_Permission.CreateByid,
        Sa_Permission.CreateDate,
        Sa_Permission.Lister,
        Sa_Permission.Listerid,
        Sa_Permission.ModifyDate,
        Sa_Permission.Tenantid,
        Sa_Permission.TenantName,
        Sa_Permission.Revision,
        Sa_PermCode.Parentid,
        Sa_PermCode.PermType,
        Sa_PermCode.PermCode,
        Sa_PermCode.PermName
        FROM Sa_PermCode
        RIGHT JOIN Sa_Permission ON Sa_Permission.Permid = Sa_PermCode.Permid
        where Sa_Permission.ResourceType = 'Role'
        and Sa_Permission.Resourceid = #{key}
    </select>

    <!--    获取一个用户的所有权限-->
    <select id="getUserAllPerm" resultType="inks.sa.common.core.domain.pojo.SaPermissionPojo">
        SELECT Sa_Permission.id,
        Sa_Permission.ResourceType,
        Sa_Permission.Resourceid,
        Sa_Permission.Permid,
        Sa_Permission.CreateBy,
        Sa_Permission.CreateByid,
        Sa_Permission.CreateDate,
        Sa_Permission.Lister,
        Sa_Permission.Listerid,
        Sa_Permission.ModifyDate,
        Sa_Permission.Tenantid,
        Sa_Permission.TenantName,
        Sa_Permission.Revision,
        Sa_PermCode.Parentid,
        Sa_PermCode.PermType,
        Sa_PermCode.PermCode,
        Sa_PermCode.PermName
        FROM Sa_PermCode
        RIGHT JOIN Sa_Permission ON Sa_Permission.Permid = Sa_PermCode.Permid
        where Sa_Permission.Resourceid in (SELECT Roleid
        FROM Sa_UserRole
        WHERE Userid = #{key}
        UNION
        SELECT #{key} AS Roleid)
    </select>
</mapper>

