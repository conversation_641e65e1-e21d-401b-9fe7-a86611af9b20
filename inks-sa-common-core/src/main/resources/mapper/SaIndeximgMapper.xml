<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaIndeximgMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaIndeximgPojo">
        select
        id, PictureUrl, DirName, FileName, Prodid, Demandid, Seq, Status, Type, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
        Custom10, Tenantid, Revision from Sa_IndexImg
        where Sa_IndexImg.id= #{key}
    </select>
    <sql id="selectSaIndeximgVo">
        select id,
               PictureUrl,
               Dir<PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>did,
               Demandid,
               Seq,
               Status,
               Type,
               <PERSON>reate<PERSON>y,
               <PERSON><PERSON><PERSON>yid,
               <PERSON><PERSON><PERSON>ate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Sa_IndexImg
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaIndeximgPojo">
        <include refid="selectSaIndeximgVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_IndexImg.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.id != null">
            and Sa_IndexImg.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.pictureurl != null">
            and Sa_IndexImg.PictureUrl like concat('%', #{SearchPojo.pictureurl}, '%')
        </if>
        <if test="SearchPojo.dirname != null">
            and Sa_IndexImg.DirName like concat('%', #{SearchPojo.dirname}, '%')
        </if>
        <if test="SearchPojo.filename != null">
            and Sa_IndexImg.FileName like concat('%', #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.prodid != null">
            and Sa_IndexImg.Prodid like concat('%', #{SearchPojo.prodid}, '%')
        </if>
        <if test="SearchPojo.demandid != null">
            and Sa_IndexImg.Demandid like concat('%', #{SearchPojo.demandid}, '%')
        </if>
        <if test="SearchPojo.type != null">
            and Sa_IndexImg.Type like concat('%', #{SearchPojo.type}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_IndexImg.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_IndexImg.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_IndexImg.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_IndexImg.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_IndexImg.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_IndexImg.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_IndexImg.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_IndexImg.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_IndexImg.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_IndexImg.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_IndexImg.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_IndexImg.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_IndexImg.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_IndexImg.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.id != null">
                or Sa_IndexImg.id like concat('%', #{SearchPojo.id}, '%')
            </if>
            <if test="SearchPojo.pictureurl != null">
                or Sa_IndexImg.PictureUrl like concat('%', #{SearchPojo.pictureurl}, '%')
            </if>
            <if test="SearchPojo.dirname != null">
                or Sa_IndexImg.DirName like concat('%', #{SearchPojo.dirname}, '%')
            </if>
            <if test="SearchPojo.filename != null">
                or Sa_IndexImg.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.prodid != null">
                or Sa_IndexImg.Prodid like concat('%', #{SearchPojo.prodid}, '%')
            </if>
            <if test="SearchPojo.demandid != null">
                or Sa_IndexImg.Demandid like concat('%', #{SearchPojo.demandid}, '%')
            </if>
            <if test="SearchPojo.type != null">
                or Sa_IndexImg.Type like concat('%', #{SearchPojo.type}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_IndexImg.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_IndexImg.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_IndexImg.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_IndexImg.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_IndexImg.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_IndexImg.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_IndexImg.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_IndexImg.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_IndexImg.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_IndexImg.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_IndexImg.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_IndexImg.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_IndexImg.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_IndexImg.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_IndexImg(id, PictureUrl, DirName, FileName, Prodid, Demandid, Seq, Status, Type, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
        Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pictureurl}, #{dirname}, #{filename}, #{prodid}, #{demandid}, #{seq}, #{status}, #{type},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2},
        #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_IndexImg
        <set>
            <if test="id != null">
                id =#{id},
            </if>
            <if test="pictureurl != null">
                PictureUrl =#{pictureurl},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="prodid != null">
                Prodid =#{prodid},
            </if>
            <if test="demandid != null">
                Demandid =#{demandid},
            </if>
            <if test="seq != null">
                Seq =#{seq},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="type != null">
                Type =#{type},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where = #{$pk.name}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_IndexImg
        where id = #{key}
    </delete>
</mapper>

