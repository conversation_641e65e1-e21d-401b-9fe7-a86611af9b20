<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaUserroleMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaUserrolePojo">
        <include refid="selectSaUserroleVo"/>
        where Sa_UserRole.id = #{key}
    </select>


    <sql id="selectSaUserroleVo">
        SELECT Sa_User.UserName,
               Sa_User.RealName,
               Sa_UserRole.id,
               Sa_UserRole.Roleid,
               Sa_UserRole.Userid,
               Sa_UserRole.Lister,
               Sa_UserRole.CreateDate,
               Sa_UserRole.ModifyDate,
               Sa_UserRole.Tenantid,
               Sa_Role.RoleCode,
               Sa_Role.RoleName,
               Sa_UserRole.RowNum,
               Sa_UserRole.CreateBy,
               Sa_UserRole.CreateByid,
               Sa_UserRole.Listerid,
               Sa_UserRole.TenantName,
               Sa_UserRole.Revision
        FROM Sa_User
                 RIGHT JOIN Sa_UserRole ON Sa_UserRole.Userid = Sa_User.id
                 LEFT JOIN Sa_Role ON Sa_Role.Roleid = Sa_UserRole.Roleid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaUserrolePojo">
        <include refid="selectSaUserroleVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_UserRole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.id != null">
            and Sa_UserRole.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.roleid != null">
            and Sa_UserRole.Roleid like concat('%', #{SearchPojo.roleid}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_UserRole.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_UserRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_UserRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_UserRole.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_UserRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_UserRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.id != null">
                or Sa_UserRole.id like concat('%', #{SearchPojo.id}, '%')
            </if>
            <if test="SearchPojo.roleid != null">
                or Sa_UserRole.Roleid like concat('%', #{SearchPojo.roleid}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_UserRole.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_UserRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_UserRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_UserRole.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_UserRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_UserRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_UserRole(id, Roleid, Userid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid,
        ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{roleid}, #{userid}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister},
        #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_UserRole
        <set>
            <if test="id != null">
                id =#{id},
            </if>
            <if test="roleid != null">
                Roleid =#{roleid},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_UserRole where id = #{key}
    </delete>


    <select id="getListByRole" resultType="inks.sa.common.core.domain.pojo.SaUserrolePojo">
        <include refid="selectSaUserroleVo"/>
        where Sa_UserRole.Roleid =#{key}
        order by Sa_Role.RoleCode
    </select>

    <!--查询List-->
    <select id="getListByUser" resultType="inks.sa.common.core.domain.pojo.SaUserrolePojo">
        <include refid="selectSaUserroleVo"/>
        where Sa_UserRole.Userid =#{key}
        order by Sa_User.UserName
    </select>

    <!--查询List-->
    <select id="getPermByUser" resultType="inks.sa.common.core.domain.pojo.SaPermcodePojo">
        SELECT DISTINCT Sa_PermCode.Permid,
        Sa_PermCode.Parentid,
        Sa_PermCode.PermType,
        Sa_PermCode.PermCode,
        Sa_PermCode.PermName,
        Sa_PermCode.RowNum,
        Sa_PermCode.IsPublic,
        Sa_PermCode.EnabledMark,
        Sa_PermCode.AllowDelete,
        Sa_PermCode.Remark,
        Sa_PermCode.CreateBy,
        Sa_PermCode.CreateByid,
        Sa_PermCode.CreateDate,
        Sa_PermCode.Lister,
        Sa_PermCode.Listerid,
        Sa_PermCode.ModifyDate,
        Sa_PermCode.Revision
        FROM Sa_UserRole
        RIGHT JOIN Sa_Role ON Sa_UserRole.Roleid = Sa_Role.Roleid
        RIGHT JOIN Sa_Permission ON Sa_Role.Roleid = Sa_Permission.Resourceid
        RIGHT JOIN Sa_PermCode ON Sa_Permission.Permid = Sa_PermCode.Permid
        where Sa_UserRole.Userid = #{key}
    </select>

    <select id="getUseridsByRoleid" resultType="java.lang.String">
        SELECT DISTINCT Userid
        FROM Sa_UserRole
        where Roleid =#{resourceid}
    </select>

    <select id="getConfigListAll" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        select *
        from Sa_Config
        where AllowUi = 1
        Order by CfgKey
    </select>

    <select id="getConfigListByUserid" resultType="inks.sa.common.core.domain.pojo.SaConfigPojo">
        select *
        from Sa_Config
        where AllowUi = 1
        and Sa_Config.Userid = #{userid}
        Order by CfgKey
    </select>
</mapper>

