<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaDeptuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaDeptuserPojo">
        <include refid="selectSaDeptuserVo"/>
        where Sa_DeptUser.id = #{key} 
    </select>
    <sql id="selectSaDeptuserVo">
        select id,
               Deptid,
               DeptCode,
               DeptName,
               Userid,
               UserName,
               RealName,
               IsAdmin,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_DeptUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaDeptuserPojo">
        <include refid="selectSaDeptuserVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DeptUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.deptid != null ">
   and Sa_DeptUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   and Sa_DeptUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_DeptUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.userid != null ">
   and Sa_DeptUser.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   and Sa_DeptUser.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and Sa_DeptUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DeptUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DeptUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DeptUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DeptUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_DeptUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_DeptUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_DeptUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_DeptUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_DeptUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DeptUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.deptid != null ">
   or Sa_DeptUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptcode != null ">
   or Sa_DeptUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_DeptUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.userid != null ">
   or Sa_DeptUser.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   or Sa_DeptUser.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or Sa_DeptUser.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DeptUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DeptUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DeptUser.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DeptUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_DeptUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_DeptUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_DeptUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_DeptUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_DeptUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DeptUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DeptUser(id, Deptid, DeptCode, DeptName, Userid, UserName, RealName, IsAdmin, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{deptid}, #{deptcode}, #{deptname}, #{userid}, #{username}, #{realname}, #{isadmin}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DeptUser
        <set>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DeptUser where id = #{key} 
    </delete>

    <select id="getEntityByUser" resultType="inks.sa.common.core.domain.pojo.SaDeptuserPojo">
        <include refid="selectSaDeptuserVo"/>
        where Sa_DeptUser.Userid = #{userid}
    </select>

    <select id="countByUserid" resultType="int">
        select count(*) from Sa_DeptUser where Userid = #{userid}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <!--   获取顶级部门和超级管理员信息 拿到两行数据作为临时表笛卡尔join-->
    <select id="getEntityBySuperAdmin" resultType="inks.sa.common.core.domain.pojo.SaDeptuserPojo">
        SELECT ''         AS id,
               dept.id AS Deptid,
               dept.DeptCode,
               dept.DeptName,
               user.id AS Userid,
               user.UserName,
               user.RealName,
               1          AS IsDeptAdmin
        FROM (SELECT * FROM Sa_Dept WHERE Parentid = 'root' limit 1) AS dept
                 CROSS JOIN (SELECT * FROM Sa_User WHERE AdminMark = 2 limit 1) AS user
        LIMIT 1
    </select>

    <select id="getUseridListInDeptids" resultType="java.lang.String">
        SELECT DISTINCT Userid
        FROM Sa_DeptUser
        WHERE Deptid in
        <foreach collection="deptids" item="deptid" open="(" separator="," close=")">
            #{deptid}
        </foreach>
    </select>

    <select id="getDeptIDByUserId" resultType="java.lang.String">
        SELECT DISTINCT Deptid
        FROM Sa_DeptUser
        WHERE Userid = #{userid}
    </select>
</mapper>

