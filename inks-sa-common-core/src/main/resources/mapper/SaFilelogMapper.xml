<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaFilelogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaFilelogPojo">
        <include refid="selectSaFilelogVo"/>
        where Sa_FileLog.id = #{key} 
    </select>
    <sql id="selectSaFilelogVo">
         select
id, OpType, UsedMark, FileOriName, BucketName, DirName, FileName, FileUrl, ModuleCode, Module, FileSize, ContentType, FileSuffix, Storage, RowNum, Remark, CreateBy, <PERSON>reateByid, <PERSON>reateDate, Lister, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_FileLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaFilelogPojo">
        <include refid="selectSaFilelogVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_FileLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.fileoriname != null ">
   and Sa_FileLog.FileOriName like concat('%', #{SearchPojo.fileoriname}, '%')
</if>
<if test="SearchPojo.bucketname != null ">
   and Sa_FileLog.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
</if>
<if test="SearchPojo.dirname != null ">
   and Sa_FileLog.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.filename != null ">
   and Sa_FileLog.FileName like concat('%', #{SearchPojo.filename}, '%')
</if>
<if test="SearchPojo.fileurl != null ">
   and Sa_FileLog.FileUrl like concat('%', #{SearchPojo.fileurl}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Sa_FileLog.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.module != null ">
   and Sa_FileLog.Module like concat('%', #{SearchPojo.module}, '%')
</if>
<if test="SearchPojo.contenttype != null ">
   and Sa_FileLog.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
</if>
<if test="SearchPojo.filesuffix != null ">
   and Sa_FileLog.FileSuffix like concat('%', #{SearchPojo.filesuffix}, '%')
</if>
<if test="SearchPojo.storage != null ">
   and Sa_FileLog.Storage like concat('%', #{SearchPojo.storage}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_FileLog.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_FileLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_FileLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_FileLog.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_FileLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_FileLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_FileLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_FileLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_FileLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_FileLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_FileLog.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_FileLog.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_FileLog.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_FileLog.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_FileLog.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.fileoriname != null ">
   or Sa_FileLog.FileOriName like concat('%', #{SearchPojo.fileoriname}, '%')
</if>
<if test="SearchPojo.bucketname != null ">
   or Sa_FileLog.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
</if>
<if test="SearchPojo.dirname != null ">
   or Sa_FileLog.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.filename != null ">
   or Sa_FileLog.FileName like concat('%', #{SearchPojo.filename}, '%')
</if>
<if test="SearchPojo.fileurl != null ">
   or Sa_FileLog.FileUrl like concat('%', #{SearchPojo.fileurl}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Sa_FileLog.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.module != null ">
   or Sa_FileLog.Module like concat('%', #{SearchPojo.module}, '%')
</if>
<if test="SearchPojo.contenttype != null ">
   or Sa_FileLog.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
</if>
<if test="SearchPojo.filesuffix != null ">
   or Sa_FileLog.FileSuffix like concat('%', #{SearchPojo.filesuffix}, '%')
</if>
<if test="SearchPojo.storage != null ">
   or Sa_FileLog.Storage like concat('%', #{SearchPojo.storage}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_FileLog.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_FileLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_FileLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_FileLog.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_FileLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_FileLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_FileLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_FileLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_FileLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_FileLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_FileLog.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_FileLog.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_FileLog.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_FileLog.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_FileLog.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_FileLog(id, OpType, UsedMark, FileOriName, BucketName, DirName, FileName, FileUrl, ModuleCode, Module, FileSize, ContentType, FileSuffix, Storage, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{optype}, #{usedmark}, #{fileoriname}, #{bucketname}, #{dirname}, #{filename}, #{fileurl}, #{modulecode}, #{module}, #{filesize}, #{contenttype}, #{filesuffix}, #{storage}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FileLog
        <set>
            <if test="optype != null">
                OpType =#{optype},
            </if>
            <if test="usedmark != null">
                UsedMark =#{usedmark},
            </if>
            <if test="fileoriname != null ">
                FileOriName =#{fileoriname},
            </if>
            <if test="bucketname != null ">
                BucketName =#{bucketname},
            </if>
            <if test="dirname != null ">
                DirName =#{dirname},
            </if>
            <if test="filename != null ">
                FileName =#{filename},
            </if>
            <if test="fileurl != null ">
                FileUrl =#{fileurl},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="module != null ">
                Module =#{module},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="contenttype != null ">
                ContentType =#{contenttype},
            </if>
            <if test="filesuffix != null ">
                FileSuffix =#{filesuffix},
            </if>
            <if test="storage != null ">
                Storage =#{storage},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FileLog where id = #{key} 
    </delete>
</mapper>

