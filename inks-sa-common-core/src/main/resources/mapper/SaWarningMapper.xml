<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaWarningMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaWarningPojo">
        <include refid="selectSaWarningVo"/>
        where Sa_Warning.id = #{key} 
    </select>
    <sql id="selectSaWarningVo">
         select
id, GenGroupid, ModuleCode, WarnCode, WarnName, WarnField, SvcCode, WarnApi, WebPath, ImageCss, TagTitle, PermCode, RowNum, EnabledMark, Remark, CreateBy, CreateByid, <PERSON>reate<PERSON><PERSON>, <PERSON>er, <PERSON>erid, ModifyDate, Revision        from Sa_Warning
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaWarningPojo">
        <include refid="selectSaWarningVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Warning.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.gengroupid != null ">
   and Sa_Warning.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Sa_Warning.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.warncode != null ">
   and Sa_Warning.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
</if>
<if test="SearchPojo.warnname != null ">
   and Sa_Warning.WarnName like concat('%', #{SearchPojo.warnname}, '%')
</if>
<if test="SearchPojo.warnfield != null ">
   and Sa_Warning.WarnField like concat('%', #{SearchPojo.warnfield}, '%')
</if>
<if test="SearchPojo.svccode != null ">
   and Sa_Warning.SvcCode like concat('%', #{SearchPojo.svccode}, '%')
</if>
<if test="SearchPojo.warnapi != null ">
   and Sa_Warning.WarnApi like concat('%', #{SearchPojo.warnapi}, '%')
</if>
<if test="SearchPojo.webpath != null ">
   and Sa_Warning.WebPath like concat('%', #{SearchPojo.webpath}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   and Sa_Warning.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.tagtitle != null ">
   and Sa_Warning.TagTitle like concat('%', #{SearchPojo.tagtitle}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   and Sa_Warning.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Warning.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Warning.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Warning.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Warning.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Warning.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or Sa_Warning.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Sa_Warning.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.warncode != null ">
   or Sa_Warning.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
</if>
<if test="SearchPojo.warnname != null ">
   or Sa_Warning.WarnName like concat('%', #{SearchPojo.warnname}, '%')
</if>
<if test="SearchPojo.warnfield != null ">
   or Sa_Warning.WarnField like concat('%', #{SearchPojo.warnfield}, '%')
</if>
<if test="SearchPojo.svccode != null ">
   or Sa_Warning.SvcCode like concat('%', #{SearchPojo.svccode}, '%')
</if>
<if test="SearchPojo.warnapi != null ">
   or Sa_Warning.WarnApi like concat('%', #{SearchPojo.warnapi}, '%')
</if>
<if test="SearchPojo.webpath != null ">
   or Sa_Warning.WebPath like concat('%', #{SearchPojo.webpath}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   or Sa_Warning.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.tagtitle != null ">
   or Sa_Warning.TagTitle like concat('%', #{SearchPojo.tagtitle}, '%')
</if>
<if test="SearchPojo.permcode != null ">
   or Sa_Warning.PermCode like concat('%', #{SearchPojo.permcode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Warning.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Warning.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Warning.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Warning.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Warning.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Warning(id, GenGroupid, ModuleCode, WarnCode, WarnName, WarnField, SvcCode, WarnApi, WebPath, ImageCss, TagTitle, PermCode, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{warncode}, #{warnname}, #{warnfield}, #{svccode}, #{warnapi}, #{webpath}, #{imagecss}, #{tagtitle}, #{permcode}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Warning
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="warncode != null ">
                WarnCode =#{warncode},
            </if>
            <if test="warnname != null ">
                WarnName =#{warnname},
            </if>
            <if test="warnfield != null ">
                WarnField =#{warnfield},
            </if>
            <if test="svccode != null ">
                SvcCode =#{svccode},
            </if>
            <if test="warnapi != null ">
                WarnApi =#{warnapi},
            </if>
            <if test="webpath != null ">
                WebPath =#{webpath},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="tagtitle != null ">
                TagTitle =#{tagtitle},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Warning where id = #{key} 
    </delete>
</mapper>

