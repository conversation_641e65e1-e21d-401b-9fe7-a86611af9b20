<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaLogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaLogPojo">
        <include refid="selectSaLogVo"/>
        where Sa_Log.id = #{key} 
    </select>
    <sql id="selectSaLogVo">
         select
id, LogType, LogLevel, Message, RequestUrl, IpAddress, Module, OperationType, CreateDate, Userid, RealName, UserAgent, HttpMethod, ResourceType, Resourceid, Result, ErrorMessage, CustomData, Tenantid, Revision        from Sa_Log
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.sa.common.core.domain.pojo.SaLogPojo">
        <include refid="selectSaLogVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Log.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.logtype != null ">
   and Sa_Log.LogType like concat('%', #{SearchPojo.logtype}, '%')
</if>
<if test="SearchPojo.loglevel != null ">
   and Sa_Log.LogLevel like concat('%', #{SearchPojo.loglevel}, '%')
</if>
<if test="SearchPojo.message != null ">
   and Sa_Log.Message like concat('%', #{SearchPojo.message}, '%')
</if>
<if test="SearchPojo.requesturl != null ">
   and Sa_Log.RequestUrl like concat('%', #{SearchPojo.requesturl}, '%')
</if>
<if test="SearchPojo.ipaddress != null ">
   and Sa_Log.IpAddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.module != null ">
   and Sa_Log.Module like concat('%', #{SearchPojo.module}, '%')
</if>
<if test="SearchPojo.operationtype != null ">
   and Sa_Log.OperationType like concat('%', #{SearchPojo.operationtype}, '%')
</if>
<if test="SearchPojo.userid != null ">
   and Sa_Log.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and Sa_Log.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.useragent != null ">
   and Sa_Log.UserAgent like concat('%', #{SearchPojo.useragent}, '%')
</if>
<if test="SearchPojo.httpmethod != null ">
   and Sa_Log.HttpMethod like concat('%', #{SearchPojo.httpmethod}, '%')
</if>
<if test="SearchPojo.resourcetype != null ">
   and Sa_Log.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
</if>
<if test="SearchPojo.resourceid != null ">
   and Sa_Log.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
</if>
<if test="SearchPojo.result != null ">
   and Sa_Log.Result like concat('%', #{SearchPojo.result}, '%')
</if>
<if test="SearchPojo.errormessage != null ">
   and Sa_Log.ErrorMessage like concat('%', #{SearchPojo.errormessage}, '%')
</if>
<if test="SearchPojo.customdata != null ">
   and Sa_Log.CustomData like concat('%', #{SearchPojo.customdata}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.logtype != null ">
   or Sa_Log.LogType like concat('%', #{SearchPojo.logtype}, '%')
</if>
<if test="SearchPojo.loglevel != null ">
   or Sa_Log.LogLevel like concat('%', #{SearchPojo.loglevel}, '%')
</if>
<if test="SearchPojo.message != null ">
   or Sa_Log.Message like concat('%', #{SearchPojo.message}, '%')
</if>
<if test="SearchPojo.requesturl != null ">
   or Sa_Log.RequestUrl like concat('%', #{SearchPojo.requesturl}, '%')
</if>
<if test="SearchPojo.ipaddress != null ">
   or Sa_Log.IpAddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.module != null ">
   or Sa_Log.Module like concat('%', #{SearchPojo.module}, '%')
</if>
<if test="SearchPojo.operationtype != null ">
   or Sa_Log.OperationType like concat('%', #{SearchPojo.operationtype}, '%')
</if>
<if test="SearchPojo.userid != null ">
   or Sa_Log.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or Sa_Log.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.useragent != null ">
   or Sa_Log.UserAgent like concat('%', #{SearchPojo.useragent}, '%')
</if>
<if test="SearchPojo.httpmethod != null ">
   or Sa_Log.HttpMethod like concat('%', #{SearchPojo.httpmethod}, '%')
</if>
<if test="SearchPojo.resourcetype != null ">
   or Sa_Log.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
</if>
<if test="SearchPojo.resourceid != null ">
   or Sa_Log.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
</if>
<if test="SearchPojo.result != null ">
   or Sa_Log.Result like concat('%', #{SearchPojo.result}, '%')
</if>
<if test="SearchPojo.errormessage != null ">
   or Sa_Log.ErrorMessage like concat('%', #{SearchPojo.errormessage}, '%')
</if>
<if test="SearchPojo.customdata != null ">
   or Sa_Log.CustomData like concat('%', #{SearchPojo.customdata}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Log(id, LogType, LogLevel, Message, RequestUrl, IpAddress, Module, OperationType, CreateDate, Userid, RealName, UserAgent, HttpMethod, ResourceType, Resourceid, Result, ErrorMessage, CustomData, Tenantid, Revision)
        values (#{id}, #{logtype}, #{loglevel}, #{message}, #{requesturl}, #{ipaddress}, #{module}, #{operationtype}, #{createdate}, #{userid}, #{realname}, #{useragent}, #{httpmethod}, #{resourcetype}, #{resourceid}, #{result}, #{errormessage}, #{customdata}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Log
        <set>
            <if test="logtype != null ">
                LogType =#{logtype},
            </if>
            <if test="loglevel != null ">
                LogLevel =#{loglevel},
            </if>
            <if test="message != null ">
                Message =#{message},
            </if>
            <if test="requesturl != null ">
                RequestUrl =#{requesturl},
            </if>
            <if test="ipaddress != null ">
                IpAddress =#{ipaddress},
            </if>
            <if test="module != null ">
                Module =#{module},
            </if>
            <if test="operationtype != null ">
                OperationType =#{operationtype},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="useragent != null ">
                UserAgent =#{useragent},
            </if>
            <if test="httpmethod != null ">
                HttpMethod =#{httpmethod},
            </if>
            <if test="resourcetype != null ">
                ResourceType =#{resourcetype},
            </if>
            <if test="resourceid != null ">
                Resourceid =#{resourceid},
            </if>
            <if test="result != null ">
                Result =#{result},
            </if>
            <if test="errormessage != null ">
                ErrorMessage =#{errormessage},
            </if>
            <if test="customdata != null ">
                CustomData =#{customdata},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Log where id = #{key} 
    </delete>
</mapper>

