<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.sa.common.core.mapper.SaRoleMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.sa.common.core.domain.pojo.SaRolePojo">
        <include refid="selectSaRoleVo"/>
        where Sa_Role.Roleid= #{key}
    </select>
    <sql id="selectSaRoleVo">
        select Roleid,
               RoleCode,
               RoleName,
               Functionid,
               FunctionCode,
               FunctionName,
               EnabledMark,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from Sa_Role
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.sa.common.core.domain.pojo.SaRolePojo">
        <include refid="selectSaRoleVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Role.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.roleid != null">
            and Sa_Role.Roleid like concat('%', #{SearchPojo.roleid}, '%')
        </if>
        <if test="SearchPojo.rolecode != null">
            and Sa_Role.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.rolename != null">
            and Sa_Role.RoleName like concat('%', #{SearchPojo.rolename}, '%')
        </if>
        <if test="SearchPojo.functionid != null">
            and Sa_Role.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null">
            and Sa_Role.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null">
            and Sa_Role.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Role.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Role.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Role.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Role.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Role.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Role.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.roleid != null">
                or Sa_Role.Roleid like concat('%', #{SearchPojo.roleid}, '%')
            </if>
            <if test="SearchPojo.rolecode != null">
                or Sa_Role.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
            </if>
            <if test="SearchPojo.rolename != null">
                or Sa_Role.RoleName like concat('%', #{SearchPojo.rolename}, '%')
            </if>
            <if test="SearchPojo.functionid != null">
                or Sa_Role.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null">
                or Sa_Role.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null">
                or Sa_Role.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Role.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Role.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Role.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Role.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Role.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Role.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Role(Roleid, RoleCode, RoleName, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum,
        Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{roleid}, #{rolecode}, #{rolename}, #{functionid}, #{functioncode}, #{functionname}, #{enabledmark},
        #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
        #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Role
        <set>
            <if test="roleid != null">
                Roleid =#{roleid},
            </if>
            <if test="rolecode != null">
                RoleCode =#{rolecode},
            </if>
            <if test="rolename != null">
                RoleName =#{rolename},
            </if>
            <if test="functionid != null">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null">
                FunctionName =#{functionname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Roleid= #{roleid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Role where Roleid= #{key}
    </delete>
</mapper>

