package inks.sa.common.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaWarninguserEntity;
import inks.sa.common.core.domain.pojo.SaWarninguserPojo;
import inks.sa.common.core.mapper.SaWarninguserMapper;
import inks.sa.common.core.service.SaWarninguserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预警用户(SaWarninguser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Service("saWarninguserService")
public class SaWarninguserServiceImpl implements SaWarninguserService {
    @Resource
    private SaWarninguserMapper saWarninguserMapper;

    @Override
    public SaWarninguserPojo getEntity(String key) {
        return this.saWarninguserMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaWarninguserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWarninguserPojo> lst = saWarninguserMapper.getPageList(queryParam);
            PageInfo<SaWarninguserPojo> pageInfo = new PageInfo<SaWarninguserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaWarninguserPojo insert(SaWarninguserPojo saWarninguserPojo) {
        //初始化NULL字段
        cleanNull(saWarninguserPojo);
        SaWarninguserEntity saWarninguserEntity = new SaWarninguserEntity();
        BeanUtils.copyProperties(saWarninguserPojo, saWarninguserEntity);
        //生成雪花id
        saWarninguserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saWarninguserEntity.setRevision(1);  //乐观锁
        this.saWarninguserMapper.insert(saWarninguserEntity);
        return this.getEntity(saWarninguserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saWarninguserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWarninguserPojo update(SaWarninguserPojo saWarninguserPojo) {
        SaWarninguserEntity saWarninguserEntity = new SaWarninguserEntity();
        BeanUtils.copyProperties(saWarninguserPojo, saWarninguserEntity);
        this.saWarninguserMapper.update(saWarninguserEntity);
        return this.getEntity(saWarninguserEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saWarninguserMapper.delete(key);
    }


    private static void cleanNull(SaWarninguserPojo saWarninguserPojo) {
        if (saWarninguserPojo.getWarnid() == null) saWarninguserPojo.setWarnid("");
        if (saWarninguserPojo.getDiffnum() == null) saWarninguserPojo.setDiffnum(0);
        if (saWarninguserPojo.getRownum() == null) saWarninguserPojo.setRownum(0);
        if (saWarninguserPojo.getRemark() == null) saWarninguserPojo.setRemark("");
        if (saWarninguserPojo.getCreateby() == null) saWarninguserPojo.setCreateby("");
        if (saWarninguserPojo.getCreatebyid() == null) saWarninguserPojo.setCreatebyid("");
        if (saWarninguserPojo.getCreatedate() == null) saWarninguserPojo.setCreatedate(new Date());
        if (saWarninguserPojo.getLister() == null) saWarninguserPojo.setLister("");
        if (saWarninguserPojo.getListerid() == null) saWarninguserPojo.setListerid("");
        if (saWarninguserPojo.getModifydate() == null) saWarninguserPojo.setModifydate(new Date());
        if (saWarninguserPojo.getUserid() == null) saWarninguserPojo.setUserid("");
        if (saWarninguserPojo.getRealname() == null) saWarninguserPojo.setRealname("");
        if (saWarninguserPojo.getTenantid() == null) saWarninguserPojo.setTenantid("");
        if (saWarninguserPojo.getTenantname() == null) saWarninguserPojo.setTenantname("");
        if (saWarninguserPojo.getRevision() == null) saWarninguserPojo.setRevision(0);
    }

    @Override
    public List<SaWarninguserPojo> getListByUser(String userid, String tenantid) {
        return this.saWarninguserMapper.getListByUser(userid);
    }
    // TODO pms中的需求 分段预警
    // 需要加入分段预警，信息分来邮件和短信。
    // 最终实现，我们在PMS中的当天需求，16点先发邮件。18点还没有完成发短信
    @Override
    public List<SaWarninguserPojo> getWarnListByUser(String userid, String tenantid) {
        try {
            List<SaWarninguserPojo> lst = this.saWarninguserMapper.getListByUser(userid);
            for (SaWarninguserPojo pojo : lst) {
                // 销售订单
                if (pojo.getSvccode().equals("D01M03")) {
                    if (StringUtils.isBlank(pojo.getWarnfield())) {
                        pojo.setWarnfield("Bus_MachiningItem.ItemPlanDate");
                    }
                    //List<Map<String, Object>> lstdata = this.saWarninguserMapper.getBusMachList(pojo.getWarnfield(), pojo.getDiffnum());
                    //if (lstdata != null) {
                    //    pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    //}
                }
            }
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
