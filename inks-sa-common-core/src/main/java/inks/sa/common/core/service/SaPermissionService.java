package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaPermissionPojo;

import java.util.List;

/**
 * 权限关系(SaPermission)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:33
 */
public interface SaPermissionService {


    SaPermissionPojo getEntity(String key);

    PageInfo<SaPermissionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saPermissionPojo 实例对象
     * @return 实例对象
     */
    SaPermissionPojo insert(SaPermissionPojo saPermissionPojo);

    /**
     * 修改数据
     *
     * @param saPermissionpojo 实例对象
     * @return 实例对象
     */
    SaPermissionPojo update(SaPermissionPojo saPermissionpojo);

    int delete(String key);

    List<SaPermissionPojo> getListByRole(String key);

    List<SaPermissionPojo> getUserAllPerm(String key);
}
