package inks.sa.common.core.config.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import inks.sa.common.core.config.oss.service.OSSConfigManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * aliyun oss配置类
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({OSS.class})
public class AliYunOSSConfiguration {

    @Bean
    public OSSClient ossClient(OSSConfigManager configManager) {
        // 使用旧版构造函数（注意：3.x版本可能已弃用）
        return new OSSClient(
                configManager.getAliyunEndpoint(),
                configManager.getAliyunAccessKeyId(),
                configManager.getAliyunAccessKeySecret());
    }

    @Bean
    public Storage aliyunStorage(OSSClient ossClient) {
        return new AliYunStorage(ossClient);
    }
}