package inks.sa.common.core.config.oss.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.config.oss.utils.OssConstant;
import inks.sa.common.core.config.oss.utils.UploadImageVO;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.domain.pojo.SaDirrulePojo;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.service.SaFilelogService;
import inks.sa.common.core.service.SaRedisService;
import io.minio.*;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@RestController
//@RequestMapping("/File") // 统一的API前缀
public class FileController {

    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    @Resource
    private OSSConfigManager configManager;

    // 注入 MinIO 和 Aliyun 的 OssService 实现
    @Resource
    private OssMinioServiceImpl fileInfoMinioService;

    @Resource
    private OssAliyunServiceImpl fileInfoAliyunService;

    // MinIO Client (主要用于分片上传的特定API和流式操作)
    @Resource
    private MinioClient minioClient;

    // Aliyun OSS Client (主要用于流式操作的特定API)
    @Resource
    private OSS aliyunOssClient;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaFilelogService filelogService;

    // =====================================================================================
    // 1️⃣ 通用文件上传类
    // =====================================================================================

    @ApiOperation("通用上传文件")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<FileInfo> upload(
            @RequestParam("file") MultipartFile file,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename,
            @RequestParam(value = "auth", required = false) String auth,
            @RequestParam(value = "module", required = false) String module) {
        if (file.isEmpty()) {
            return R.fail("上传文件不能为空");
        }
        try {
            // 上传
            OssService service = getCurrentOssService();
            String objectName = buildObjectName(dir, filename, file.getOriginalFilename());
            // 目录规则鉴权
            // 此处 response 参数为 null，因为我们通过 R.fail 返回错误信息，而非直接写回 HttpServletResponse
            if (!verifyAccess(objectName, auth, null, null)) {
                return R.fail("您无权限访问该目录");
            }
            FileInfo fileInfo = service.upload(file, objectName, module);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("上传失败: " + e.getMessage());
        }
    }


    @ApiOperation("指定文件本地路径上传")
    @PostMapping("/uploadByPath")
    public R<FileInfo> uploadByPath(
            @RequestParam("filepath") String filepath,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename,
            @RequestParam(value = "module", required = false) String module) {
        File localFile = new File(filepath);
        if (!localFile.exists() || !localFile.isFile()) {
            return R.fail("指定的文件路径无效或不是文件");
        }
        try {
            OssService service = getCurrentOssService();
            String objectName = buildObjectName(dir, filename, localFile.getName());

            // 上传文件并获取FileInfo
            FileInfo fileInfo = service.putFile(filepath, objectName, module);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload by path failed for filepath: {}. Error: {}", filepath, e.getMessage(), e);
            return R.fail("路径上传失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "通过文件路径上传单个文件使用自定义连接参数", notes = "仅返回访问URL")
    //@PostMapping(value = "uploadByPathCustom")
    public R<String> uploadByPathCustom(String filepath, String ossbucket, String dir, String objectname, String osstype,
                                        String ossaccesskey, String osssecretkey,
                                        String ossendpoint) {
        if (StringUtils.isBlank(osstype)) {
            return R.fail("osstype不能为空");
        }

        try {
            File file = new File(filepath);
            if (!file.exists()) {
                return R.fail("文件不存在");
            }

            // 处理文件名
            String fileName = StringUtils.isNotBlank(objectname) ? objectname : file.getName();
            String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(file.getName());
            if (StringUtils.isNotBlank(fileSuffix)) {
                fileName = fileName.concat(".").concat(fileSuffix);
            }

            // 构建完整的对象路径
            String objectPath = StringUtils.isNotBlank(dir) ?
                    dir + "/" + fileName : fileName;

            String fileUrl;
            FileInputStream inputStream = new FileInputStream(file);

            if (osstype.equals(OssConstant.OSSTYPE_MINIO)) {
                // MinIO上传
                try {
                    MinioClient minioClient = MinioClient.builder()
                            .endpoint(ossendpoint)
                            .credentials(ossaccesskey, osssecretkey)
                            .build();

                    // 确保bucket存在
                    boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder()
                            .bucket(ossbucket)
                            .build());
                    if (!bucketExists) {
                        minioClient.makeBucket(MakeBucketArgs.builder()
                                .bucket(ossbucket)
                                .build());
                    }

                    // 上传文件
                    minioClient.putObject(PutObjectArgs.builder()
                            .bucket(ossbucket)
                            .object(objectPath)
                            .stream(inputStream, file.length(), -1)
                            .contentType(Files.probeContentType(file.toPath()))
                            .build());

                    // 构建访问URL
                    fileUrl = ossendpoint + "/" + ossbucket + "/" + objectPath;

                } catch (Exception e) {
                    throw new RuntimeException("MinIO上传失败: " + e.getMessage());
                }

            } else if (osstype.equals(OssConstant.OSSTYPE_ALIYUN)) {
                // 阿里云OSS上传
                try {
                    OSS ossClient = new OSSClientBuilder().build(
                            ossendpoint,
                            ossaccesskey,
                            osssecretkey
                    );

                    // 上传文件
                    ossClient.putObject(ossbucket, objectPath, inputStream);
                    ossClient.shutdown();

                    // 构建访问URL
                    fileUrl = "https://" + ossbucket + "." + ossendpoint + "/" + objectPath;

                } catch (Exception e) {
                    throw new RuntimeException("阿里云OSS上传失败: " + e.getMessage());
                }

            } else {
                return R.fail("不支持的osstype类型");
            }

            inputStream.close();
            return R.ok(fileUrl, "上传成功");

        } catch (Exception e) {
            log.error("文件上传失败", e);
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation("文件流上传")
    @PostMapping(value = "/uploadByStream", consumes = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public R<FileInfo> uploadByStream(
            InputStream inputStream,
            @RequestParam("dir") String dir,
            @RequestParam("filename") String filename,
            @RequestParam(value = "contentType", required = false) String contentType,
            @RequestParam(value = "size", required = false) Long size,
            @RequestParam(value = "module", required = false) String module) {
        if (inputStream == null) {
            return R.fail("上传文件流不能为空");
        }
        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String objectName = buildObjectName(dir, filename, filename);

            // 如果没有提供 contentType，尝试根据文件名推断
            if (contentType == null || contentType.isEmpty()) {
                contentType = inferContentType(filename);
            }

            // 确保有文件大小
            if (size == null) {
                // 对于未知大小的流，尝试缓冲并计算大小
                try {
                    byte[] buffer = IOUtils.toByteArray(inputStream);
                    size = (long) buffer.length;
                    inputStream = new ByteArrayInputStream(buffer);
                } catch (IOException e) {
                    log.warn("无法确定流大小，可能会影响上传性能", e);
                    size = -1L; // 使用-1表示未知大小
                }
            }

            FileInfo fileInfo = service.uploadStream(inputStream, bucketName, objectName, size, contentType, module);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload by stream failed. Error: {}", e.getMessage(), e);
            return R.fail("流上传失败: " + e.getMessage());
        }
    }


    @ApiOperation("删除指定单个文件")
    @PostMapping("/remove")
    public R<String> remove(@RequestParam("objectname") String objectname) {
        if (!StringUtils.isNotBlank(objectname)) {
            return R.fail("文件名不能为空");
        }
        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            service.remove(bucketName, objectname);
            log.info("/remove => oss文件删除成功: {}", objectname);
            return R.ok("文件删除成功: " + objectname);
        } catch (Exception e) {
            log.error("Remove failed for objectname: {}. Error: {}", objectname, e.getMessage(), e);
            return R.fail("删除失败: " + e.getMessage());
        }
    }

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();


    /**
     * 1.图片（上述后缀）一定走预览。
     * 2.content-type 以 image/、video/、audio/、text/plain 或 application/pdf 开头的，也走预览。
     * 3.其余类型（Word、Excel、PPT、ZIP、未知类型等）全部强制下载。
     * 4.如果从对象存储拿到的 content-type 为 null/空，则归为下载。
     */
    @ApiOperation("通用图片预览/文件下载（支持Range，自动识别下载类型，代理访问OSS）")
    @GetMapping("/proxy/**")
    public void proxy(HttpServletRequest request, HttpServletResponse response, String sec) {
        // 1. 获取 objectName 例如http://dev.inksyun.com:31080/utils/File/proxy/D96M03/202505/1.png?sec=b8
        // 返回objectName: D96M03/202505/1.png
        String objectName = extractPathFromPattern(request, "/proxy/**");
        /* 2. 目录规则鉴权 */
        if (!verifyAccess(objectName, null, sec, response)) {
            log.warn("proxy => access denied. object={}", objectName);
            return;
        }

        String ossType = configManager.getOssType();
        String bucketName = getCurrentBucketName(); // 获取当前配置的bucket

        try {
            // 文件名（最后一个/后的内容）
            String filename = objectName.substring(objectName.lastIndexOf('/') + 1);
            // 文件夹名（去掉最后的文件名部分）
            String dir = objectName.substring(0, objectName.lastIndexOf('/'));

            // 日志相关变量
            String fileoriname = null;
            Long filesize = null;
            String contenttype = null;
            String filesuffix = "";
            String storage = null;

            // 日志：当前完整请求路径
            String fullUrl = request.getRequestURL().toString();
            String queryString = request.getQueryString();
            if (queryString != null) {
                fullUrl += "?" + queryString;
            }

            // 文件扩展名
            int dotIdx = filename.lastIndexOf('.');
            if (dotIdx != -1) {
                filesuffix = filename.substring(dotIdx + 1);
            }

            // OSS类型分流
            if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
                storage = "minio";
                // 获取Minio对象元信息
                ObjectStat stat = minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
                filesize = stat.length();
                contenttype = stat.contentType();
                // 取原文件名（前提是上传时存入user-metadata）
                fileoriname = getUserMetaFromStat(stat, "orifilename");
                if (fileoriname != null) {
                    fileoriname = URLDecoder.decode(fileoriname, "UTF-8");
                }

                // 自动判断是否为下载模式
                boolean isDownload = shouldForceDownload(filename, contenttype);
                setContentDispositionAuto(response, isDownload, filename);

                // 日志打印
                if (isDownload) {
                    log.info("/proxy文件下载：{}", objectName);
                } else {
                    log.info("/proxy图片预览：{}", objectName);
                }

                // 文件流输出
                try (InputStream is = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).build())) {
                    response.setContentType(contenttype);
                    response.setContentLengthLong(filesize);
                    IOUtils.copy(is, response.getOutputStream());
                }
            } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
                storage = "aliyun";
                // 获取Aliyun对象元信息
                ObjectMetadata meta = aliyunOssClient.getObjectMetadata(bucketName, objectName);
                filesize = meta.getContentLength();
                contenttype = meta.getContentType();
                fileoriname = meta.getUserMetadata().get("orifilename");

                boolean isDownload = shouldForceDownload(filename, contenttype);
                setContentDispositionAuto(response, isDownload, filename);

                if (isDownload) {
                    log.info("/proxy文件下载：{}", objectName);
                } else {
                    log.info("/proxy图片预览：{}", objectName);
                }

                try (InputStream is = aliyunOssClient.getObject(bucketName, objectName).getObjectContent()) {
                    response.setContentType(contenttype);
                    response.setContentLengthLong(filesize);
                    IOUtils.copy(is, response.getOutputStream());
                }
            } else {
                sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "未知的OSS类型配置");
                return;
            }

            // 只在下载时记录日志
            boolean isDownload = shouldForceDownload(filename, contenttype);
            if (isDownload) {
                SaFilelogPojo utsFilelogPojo = new SaFilelogPojo();
                utsFilelogPojo.setOptype(1); // 下载
                utsFilelogPojo.setBucketname(bucketName);
                utsFilelogPojo.setDirname(dir);
                utsFilelogPojo.setFilename(filename);
                utsFilelogPojo.setFileurl(fullUrl);
                utsFilelogPojo.setFileoriname(fileoriname);
                utsFilelogPojo.setFilesize(filesize);
                utsFilelogPojo.setContenttype(contenttype);
                utsFilelogPojo.setFilesuffix(filesuffix);
                utsFilelogPojo.setStorage(storage);
                filelogService.insert(utsFilelogPojo);
            }
        } catch (Exception e) {
            log.error("Proxy error for object {}: {}", objectName, e.getMessage(), e);
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "代理访问文件失败: " + e.getMessage());
        }
    }

    // ==============================  文本直返  ==============================
    @ApiOperation("文本文件在线查看 (纯文本返回)")
    @GetMapping("/proxyByText/**")
    public void proxyByText(HttpServletRequest request,
                            HttpServletResponse response,
                            @RequestParam(value = "sec", required = false) String sec) {
        String objectName = extractPathFromPattern(request, "/proxyByText/**");
        /* 2. 目录规则鉴权 */
        if (!verifyAccess(objectName, null, sec, response)) {
            log.warn("proxy => access denied. object={}", objectName);
            return;
        }

        String bucketName = getCurrentBucketName();
        try (InputStream is = openObjectStream(bucketName, objectName);
             Reader reader = new InputStreamReader(is, StandardCharsets.UTF_8)) {
            response.setContentType("text/plain; charset=utf-8");
            IOUtils.copy(reader, response.getWriter());
        } catch (Exception e) {
            log.error("proxyByText error for {}: {}", objectName, e.getMessage(), e);
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文本读取失败");
        }
    }

    // ==============================  图片 → Base64  ==============================
    @ApiOperation("图片文件转 Base64 文本返回")
    @GetMapping("/proxyByBase64/**")
    public R<String> proxyByBase64(HttpServletRequest request,
                                   @RequestParam(value = "sec", required = false) String sec) {
        String objectName = extractPathFromPattern(request, "/proxyByBase64/**");
        /* 2. 目录规则鉴权 */
        if (!verifyAccess(objectName, null, sec, null)) {
            log.warn("proxy => access denied. object={}", objectName);
            return R.fail("图片转 Base64 失败");
        }
        String bucketName = getCurrentBucketName();
        try (InputStream is = openObjectStream(bucketName, objectName)) {
            byte[] bytes = IOUtils.toByteArray(is);
            String base64 = Base64.getEncoder().encodeToString(bytes);
            return R.ok(base64);
        } catch (Exception e) {
            log.error("proxyByBase64 error for {}: {}", objectName, e.getMessage(), e);
            return R.fail("图片转 Base64 失败: " + e.getMessage());
        }
    }


    private boolean shouldForceDownload(String filename, String contentType) {
        String lowerName = filename.toLowerCase();
        int dot = lowerName.lastIndexOf(".");
        String suffix = dot >= 0 ? lowerName.substring(dot + 1) : "";
        // 1. 优先根据扩展名判断（常见图片/media不下载）
        if (INLINE_SUFFIX.contains(suffix)) return false;
        // 2. 根据contentType前缀（常见可预览文档不下载）
        if (contentType != null) {
            for (String inline : INLINE_MIME) {
                if (contentType.startsWith(inline)) return false;
            }
        }
        // 其它全部强制下载
        return true;
    }

    /**
     * 设置Content-Disposition响应头（自动切换inline/attachment）
     */
    private void setContentDispositionAuto(HttpServletResponse response, boolean isDownload, String filename) {
        if (isDownload) {
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
        } else {
            response.setHeader("Content-Disposition", "inline; filename=\"" + filename + "\"");
        }
    }


    // 判断是否为图片
    private boolean isImage(String objectName) {
        String lower = objectName.toLowerCase();
        return lower.endsWith(".jpg") || lower.endsWith(".jpeg") || lower.endsWith(".png")
                || lower.endsWith(".gif") || lower.endsWith(".bmp") || lower.endsWith(".webp");
    }

    //如何拿到 user-metadata？
    //自定义元数据在 MinIO/OSS/S3 里，HTTP 头会变成：x-amz-meta-xxx 这样的格式。
    //
    //比如你上传时写 userMetadata("orifilename", "xxx.doc")，
    //MinIO 对象 HTTP 头里就有：x-amz-meta-orifilename: xxx.doc。
    private String getUserMetaFromStat(ObjectStat stat, String metaKey) {
        Map<String, List<String>> headers = stat.httpHeaders();
        // 注意大小写，建议统一用小写
        String key = "x-amz-meta-" + metaKey.toLowerCase();
        List<String> metaList = headers.get(key);
        if (metaList != null && !metaList.isEmpty()) {
            return metaList.get(0);
        }
        return null;
    }

    private String extractPathFromPattern(HttpServletRequest request, String patternPrefix) {
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        return antPathMatcher.extractPathWithinPattern(bestMatchPattern, path);
    }

    private void sendError(HttpServletResponse response, int sc, String message) {
        try {
            if (!response.isCommitted()) {
                response.sendError(sc, message);
            }
        } catch (IOException e) {
            log.error("Error sending error response: {}", e.getMessage());
        }
    }


    // =====================================================================================
    // 2️⃣ 大文件分片类 (主要适配MinIO, 与File_VideoController逻辑类似)
    // =====================================================================================
    // --- 大文件分片相关成员 (从 File_VideoController 迁移并适配) ---
    private static final ConcurrentHashMap<String, ChunkInfo> CHUNK_INFO_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Long> CHUNK_LAST_ACCESS_TIME = new ConcurrentHashMap<>();
    private static final Map<String, MergeProgress> MERGE_PROGRESS_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Future<?>> ACTIVE_MERGE_TASKS = new ConcurrentHashMap<>();

    @Value("${file.upload.temp-dir:${java.io.tmpdir}/allfile-upload-chunks}")
    private String tempDirBase;
    private Path tempDirPath;

    @Value("${file.upload.expiration-hours:24}")
    private int chunkExpirationHours;

    private ExecutorService mergeExecutor;
    private boolean minioInitialized = false; // 用于标记MinIO分片环境是否初始化

    // --- 初始化与销毁 ---
    @PostConstruct
    public void initController() {
        try {
            // 初始化分片上传的临时目录
            this.tempDirPath = Paths.get(tempDirBase);
            if (!Files.exists(this.tempDirPath)) {
                Files.createDirectories(this.tempDirPath);
                log.info("Created temporary directory for chunks: {}", this.tempDirPath.toAbsolutePath());
            }

            // 初始化用于合并操作的线程池
            mergeExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            log.info("Merge executor service initialized for AllFileController.");

            // MinIO Bucket检查 (如果MinIO是当前或默认OSS类型，或者为了分片上传功能)
            // 注意: 分片上传目前主要针对MinIO实现
            initMinioForSharding();

        } catch (Exception e) {
            log.error("Error during AllFileController initialization", e);
        }
    }

    private synchronized void initMinioForSharding() {
        if (minioInitialized) return;
        try {
            String minioBucket = configManager.getMinioBucket();
            if (minioClient != null && StringUtils.isNotBlank(minioBucket)) {
                boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioBucket).build());
                if (!bucketExists) {
                    minioClient.makeBucket(MakeBucketArgs.builder().bucket(minioBucket).build());
                    log.info("MinIO Bucket '{}' created for sharding.", minioBucket);
                }
                minioInitialized = true;
                log.info("MinIO sharding environment initialized. Bucket: '{}'", minioBucket);
            } else {
                log.warn("MinIO client or bucket not configured, sharding uploads might fail if MinIO is intended.");
            }
        } catch (Exception e) {
            log.error("Failed to initialize MinIO for sharding", e);
        }
    }

    @PreDestroy
    public void destroyController() {
        if (mergeExecutor != null && !mergeExecutor.isShutdown()) {
            // ... (安全关闭 mergeExecutor, 参考 File_VideoController)
            log.info("AllFileController merge executor service shut down.");
        }
    }

    // --- 辅助方法 ---
    private OssService getCurrentOssService() {
        String ossType = configManager.getOssType();
        if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
            return fileInfoMinioService;
        } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
            return fileInfoAliyunService;
        }
        throw new IllegalStateException("Unsupported OSS type: " + ossType);
    }

    private String getCurrentBucketName() {
        String ossType = configManager.getOssType();
        if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
            return configManager.getMinioBucket();
        } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
            return configManager.getAliyunBucket();
        }
        throw new IllegalStateException("Unsupported OSS type: " + ossType);
    }

    /**
     * 构建对象存储的完整文件路径（对象名）。
     * 规则如下：
     * 1. 文件名优先使用前端指定（clientProvidedFilename），否则用原始文件名（originalMultipartFilename）的扩展名拼接UUID，无则直接用UUID。
     * 2. 目录（dir）会自动去除首尾斜杠，并在末尾拼接当前年月（yyyyMM）作为归档目录。
     * 3. 最终路径格式为：{dir}/{yyyyMM}/{filename} 或 {yyyyMM}/{filename}。
     * <p>
     * 示例：
     * buildObjectName("image", null, "pic.jpg")  // 返回 "image/202406/xxxxxx.jpg"
     * buildObjectName("", "abc.png", null)      // 返回 "202406/abc.png"
     *
     * @param dir                       目标目录（可为空）
     * @param clientProvidedFilename    前端指定文件名（可为空）
     * @param originalMultipartFilename 原始文件名（可为空）
     * @return 规范化的对象名（包含目录和文件名）
     */
    private String buildObjectName(String dir, String clientProvidedFilename, String originalMultipartFilename) {
        String filenameToUse;
        if (StringUtils.isNotBlank(clientProvidedFilename)) {
            filenameToUse = clientProvidedFilename;
        } else if (StringUtils.isNotBlank(originalMultipartFilename)) {
            String extension = "";
            if (originalMultipartFilename.contains(".")) {
                extension = originalMultipartFilename.substring(originalMultipartFilename.lastIndexOf("."));
            }
            filenameToUse = IdUtil.fastSimpleUUID() + extension;
        } else {
            filenameToUse = IdUtil.fastSimpleUUID();
        }
        // 规范化目录
        String cleanedDir = dir.replaceAll("^/+", "").replaceAll("/+$", "");

        // 加上年月目录
        String yearMonth = new SimpleDateFormat("yyyyMM").format(new Date());

        String fullDir = cleanedDir.isEmpty() ? yearMonth : cleanedDir + "/" + yearMonth;

        return fullDir + "/" + filenameToUse;
    }

    private String getFileExtension(String fileName) {
        if (StringUtils.isNotBlank(fileName) && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf("."));
        }
        return "";
    }

    @ApiOperation("初始化分片上传")
    @PostMapping("/split/init")
    public R<Map<String, Object>> initMultipartUpload(
            @RequestParam("fileName") String clientOriginalFileName,
            @RequestParam("fileSize") Long fileSize,
            @RequestParam("chunkSize") Integer chunkSize,
            @RequestParam(value = "uploadId", required = false) String existingUploadId) {
        log.info("初始化分片上传请求. 文件名: {}, 大小: {}, 分片大小: {}, 已有uploadId: {}",
                clientOriginalFileName, fileSize, chunkSize, existingUploadId);

        if (!StringUtils.isNotBlank(clientOriginalFileName) || fileSize == null || chunkSize == null || fileSize <= 0 || chunkSize <= 0) {
            return R.fail("参数错误: 文件名、文件大小和分片大小必须有效");
        }
        if (!minioInitialized) initMinioForSharding(); // 确保MinIO环境就绪

        try {
            String uploadId = StringUtils.isNotBlank(existingUploadId) ? existingUploadId : UUID.randomUUID().toString();
            ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
            Map<String, Object> result = new HashMap<>();

            if (chunkInfo != null) { // 恢复任务
                log.info("找到已有上传任务. UploadID: {}", uploadId);
                if (chunkInfo.getOriginalFileName().equals(clientOriginalFileName) && chunkInfo.getTotalSize() == fileSize) {
                    // 对已上传的分片进行检查
                    boolean[] uploadedChunks = chunkInfo.getUploadedChunks();
                    int uploadedCount = 0;
                    for (boolean uploaded : uploadedChunks) {
                        if (uploaded) uploadedCount++;
                    }

                    log.info("恢复已存在的分片上传任务. UploadID: {}, 文件名: {}, 已上传分片: {}/{}",
                            uploadId, clientOriginalFileName, uploadedCount, uploadedChunks.length);

                    // 验证分片文件是否真实存在
                    for (int i = 0; i < uploadedChunks.length; i++) {
                        if (uploadedChunks[i]) {
                            File chunkFile = new File(chunkInfo.getTempDir(), "chunk_" + i);
                            if (!chunkFile.exists() || chunkFile.length() == 0) {
                                log.warn("已标记为上传的分片 {} 实际不存在或为空，重置状态", i);
                                uploadedChunks[i] = false;
                            }
                        }
                    }

                    result.put("resumed", true);
                    result.put("uploadedCount", uploadedCount);
                } else {
                    log.warn("UploadID {} 已存在但文件信息不匹配. 旧文件: {}({}), 新文件: {}({}). 将生成新任务.",
                            uploadId, chunkInfo.getOriginalFileName(), chunkInfo.getTotalSize(),
                            clientOriginalFileName, fileSize);
                    uploadId = UUID.randomUUID().toString();
                    chunkInfo = null;
                    result.put("resumed", false);
                }
            } else {
                log.info("未找到已有上传任务，创建新的上传任务. 新UploadID: {}", uploadId);
                result.put("resumed", false);
            }

            if (chunkInfo == null) { // 创建新任务
                Path tempUploadDir = this.tempDirPath.resolve(uploadId);
                if (!Files.exists(tempUploadDir)) Files.createDirectories(tempUploadDir);
                log.info("创建分片临时目录: {}", tempUploadDir);

                chunkInfo = new ChunkInfo();
                chunkInfo.setUploadId(uploadId);
                chunkInfo.setOriginalFileName(clientOriginalFileName); // 存储原始文件名
                chunkInfo.setTotalSize(fileSize);
                chunkInfo.setChunkSize(chunkSize);
                chunkInfo.setTempDir(tempUploadDir.toString());
                int totalChunks = (int) Math.ceil((double) fileSize / chunkSize);
                chunkInfo.setTotalChunks(totalChunks);
                chunkInfo.setUploadedChunks(new boolean[totalChunks]);
                chunkInfo.setChunkHashes(new String[totalChunks]);
                // objectName 和 fileName 将在 completeMultipartUpload 时根据传入的 dir/filename 确定
                CHUNK_INFO_MAP.put(uploadId, chunkInfo);
                log.info("初始化新的分片上传. 原始文件名: '{}', UploadID: {}, 总分片数: {}",
                        clientOriginalFileName, uploadId, totalChunks);
            }

            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());

            result.put("uploadId", uploadId);
            result.put("chunkSize", chunkInfo.getChunkSize());
            result.put("totalChunks", chunkInfo.getTotalChunks());
            result.put("uploadedChunks", chunkInfo.getUploadedChunks());
            result.put("chunkHashes", chunkInfo.getChunkHashes());

            return R.ok(result);
        } catch (IOException e) {
            log.error("IO错误: {}", e.getMessage(), e);
            return R.fail("创建临时目录失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("初始化分片上传失败: {}", e.getMessage(), e);
            return R.fail("初始化分片上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("上传分片")
    @PostMapping("/split/chunk")
    public R<Map<String, Object>> uploadChunk(
            @RequestParam("file") MultipartFile file,
            @RequestParam("uploadId") String uploadId,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("chunkHash") String clientChunkHash) {
        log.info("接收到分片上传请求. UploadID: {}, 分片号: {}", uploadId, chunkNumber);

        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        if (chunkInfo == null) {
            log.error("无效的上传ID: {}", uploadId);
            return R.fail("无效上传ID");
        }

        if (chunkNumber < 0 || chunkNumber >= chunkInfo.getTotalChunks()) {
            log.error("分片号无效: {} (总分片数: {})", chunkNumber, chunkInfo.getTotalChunks());
            return R.fail("分片号无效");
        }

        try {
            // 确保目录存在
            File tempDir = new File(chunkInfo.getTempDir());
            if (!tempDir.exists()) {
                tempDir.mkdirs();
                log.info("创建临时目录: {}", tempDir.getAbsolutePath());
            }

            // 创建分片文件
            File chunkFile = new File(tempDir, "chunk_" + chunkNumber);
            file.transferTo(chunkFile);
            log.info("分片 {} 保存成功，大小: {}", chunkNumber, chunkFile.length());

            // 验证哈希（可选）
            // 这里可以添加服务器端计算分片哈希的逻辑并与clientChunkHash比较

            // 更新上传状态
            chunkInfo.getUploadedChunks()[chunkNumber] = true;
            chunkInfo.getChunkHashes()[chunkNumber] = clientChunkHash;
            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());

            // 检查该分片是否真的存在
            if (!chunkFile.exists() || chunkFile.length() == 0) {
                log.error("分片 {} 保存后文件不存在或为空", chunkNumber);
                chunkInfo.getUploadedChunks()[chunkNumber] = false;
                return R.fail("分片保存失败：文件不存在或为空");
            }

            // 返回结果，包括分片上传状态
            int uploadedCount = 0;
            for (boolean uploaded : chunkInfo.getUploadedChunks()) {
                if (uploaded) uploadedCount++;
            }

            log.info("分片 {} 上传成功. 已上传 {}/{}.", chunkNumber, uploadedCount, chunkInfo.getTotalChunks());

            Map<String, Object> result = new HashMap<>();
            result.put("message", "分片 " + chunkNumber + " 上传成功");
            result.put("chunkNumber", chunkNumber);
            result.put("uploadedCount", uploadedCount);
            result.put("totalChunks", chunkInfo.getTotalChunks());
            result.put("isCompleted", uploadedCount == chunkInfo.getTotalChunks());

            return R.ok(result);
        } catch (IOException e) {
            log.error("保存分片 {} 时发生IO错误: {}", chunkNumber, e.getMessage(), e);
            return R.fail("分片上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理分片 {} 时发生错误: {}", chunkNumber, e.getMessage(), e);
            return R.fail("分片上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("完成分片上传（异步）")
    @PostMapping("split/complete")
    public R<Map<String, Object>> completeMultipartUpload(
            @RequestParam("uploadId") String uploadId,
            @RequestParam(defaultValue = "video") String dir,
            @RequestParam(value = "filename", required = false) String clientFilename) {
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        if (chunkInfo == null) {
            return R.fail("无效的上传ID: " + uploadId);
        }

        // 确保MinIO已初始化
        initMinioForSharding();
        if (!minioInitialized) {
            return R.fail("MinIO服务未初始化，无法完成上传");
        }

        String effectiveFilename = StringUtils.isBlank(clientFilename) ?
                IdUtil.fastSimpleUUID() + getFileExtension(chunkInfo.getOriginalFileName()) : clientFilename;
        String finalObjectName = buildObjectName(dir, effectiveFilename, null); // 使用 dir 和 effectiveFilename 构建

        chunkInfo.setFileName(effectiveFilename); // 存储最终文件名（不含路径）
        chunkInfo.setObjectName(finalObjectName); // 存储最终完整对象路径

        log.info("请求完成分片上传. UploadID: {}, 最终对象名: {}", uploadId, finalObjectName);

        // 先更新状态为CHECKING，避免卡在QUEUED状态
        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "CHECKING", 5, null, false));

        // 确保我们有一个有效的执行器
        if (mergeExecutor == null || mergeExecutor.isShutdown()) {
            log.info("重新初始化合并执行器");
            mergeExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        }

        Future<?> mergeTask = mergeExecutor.submit(() -> {
            Thread.currentThread().setName("merge-upload-" + uploadId);
            boolean success = false;
            try {
                // 检查所有分片是否已上传
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "CHECKING", 10, null, false));
                log.info("检查分片完整性. UploadID: {}", uploadId);

                boolean[] uploadedChunks = chunkInfo.getUploadedChunks();
                for (int i = 0; i < uploadedChunks.length; i++) {
                    if (!uploadedChunks[i]) {
                        String errorMsg = "分片 " + i + " 未上传，合并失败";
                        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(i, chunkInfo.getTotalChunks(), "ERROR", 0, errorMsg, false));
                        log.error(errorMsg);
                        return;
                    }
                }

                // 开始合并过程
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "MERGING", 20, null, false));
                log.info("开始合并分片. UploadID: {}", uploadId);

                File mergedFile = new File(chunkInfo.getTempDir(), chunkInfo.getFileName());
                try (FileOutputStream fos = new FileOutputStream(mergedFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;

                    for (int i = 0; i < chunkInfo.getTotalChunks(); i++) {
                        // 更新合并进度
                        int mergePercent = 20 + (i * 50 / chunkInfo.getTotalChunks());
                        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(i, chunkInfo.getTotalChunks(), "MERGING", mergePercent, null, false));

                        File chunkFile = new File(chunkInfo.getTempDir(), "chunk_" + i);
                        try (FileInputStream fis = new FileInputStream(chunkFile)) {
                            while ((bytesRead = fis.read(buffer)) != -1) {
                                fos.write(buffer, 0, bytesRead);
                            }
                        }
                    }
                }

                // 开始上传到MinIO
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "UPLOADING_TO_MINIO", 70, null, false));
                log.info("开始上传合并文件到MinIO. UploadID: {}, 对象名: {}", uploadId, chunkInfo.getObjectName());

                // 确保MinIO客户端已初始化
                if (minioClient == null) {
                    throw new RuntimeException("MinIO客户端未初始化");
                }

                String contentType = Files.probeContentType(mergedFile.toPath());
                if (contentType == null) {
                    contentType = "application/octet-stream";  // 默认内容类型
                }

                try (InputStream is = Files.newInputStream(mergedFile.toPath())) {
                    minioClient.putObject(
                            PutObjectArgs.builder()
                                    .bucket(configManager.getMinioBucket())
                                    .object(chunkInfo.getObjectName())
                                    .contentType(contentType)
                                    .stream(is, mergedFile.length(), -1)
                                    .build()
                    );
                    MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "UPLOADING_TO_MINIO", 90, null, false));
                }

                success = true;
                log.info("文件合并和上传完成. UploadID: {}, 对象名: {}", uploadId, chunkInfo.getObjectName());
            } catch (Exception e) {
                String errorMsg = "合并或上传过程错误: " + e.getMessage();
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "ERROR", 0, errorMsg, false));
                log.error("文件合并或上传失败. UploadID: {}. 错误: {}", uploadId, e.getMessage(), e);
            } finally {
                if (success) {
                    MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "COMPLETED", 100, null, false));
                }

                // 之后可以考虑清理临时文件，但保留一段时间方便调试
                // 清理逻辑可以在定时任务中处理

                ACTIVE_MERGE_TASKS.remove(uploadId);
            }
        });

        ACTIVE_MERGE_TASKS.put(uploadId, mergeTask);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "文件合并与上传任务已启动");
        response.put("uploadId", uploadId);
        response.put("objectName", finalObjectName);
        return R.ok(response);
    }

    @ApiOperation("取消分片上传任务")
    @PostMapping("split/cancel") // 建议使用POST进行有状态更改的操作
    public R<String> cancelMultipartUpload(@RequestParam("uploadId") String uploadId) {
        // ... (逻辑同 File_VideoController.cancelMultipartUpload)
        // 中断 ACTIVE_MERGE_TASKS 中的任务
        // 清理 CHUNK_INFO_MAP, CHUNK_LAST_ACCESS_TIME, MERGE_PROGRESS_MAP (或标记为CANCELLED)
        // 删除临时分片目录
        return R.ok("上传任务 " + uploadId + " 已取消");
    }

    @ApiOperation("获取合并进度")
    @GetMapping("split/progress")
    public R<Map<String, Object>> getMergeProgress(@RequestParam("uploadId") String uploadId) {
        MergeProgress progress = MERGE_PROGRESS_MAP.get(uploadId);
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);

        if (progress == null) {
            // 找不到进度时，检查是否有上传ID的记录
            if (chunkInfo != null) {
                // 有记录但没有进度，可能是尚未开始合并
                progress = new MergeProgress(0, chunkInfo.getTotalChunks(), "WAITING", 0, null, false);
                MERGE_PROGRESS_MAP.put(uploadId, progress);
            } else {
                return R.fail("未找到上传ID的进度: " + uploadId);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("status", progress.getStatus());
        result.put("percent", progress.getPercent());
        result.put("current", progress.getCurrent());
        result.put("total", progress.getTotal());

        // 添加错误消息（如果有）
        if (StringUtils.isNotBlank(progress.getErrorMessage())) {
            result.put("errorMessage", progress.getErrorMessage());
        }

        // 如果已完成，添加对象信息
        if ("COMPLETED".equals(progress.getStatus()) && chunkInfo != null && StringUtils.isNotBlank(chunkInfo.getObjectName())) {
            result.put("objectName", chunkInfo.getObjectName());
            result.put("fileName", chunkInfo.getFileName());
            // 添加文件大小
            if (chunkInfo.getTotalSize() > 0) {
                result.put("fileSize", chunkInfo.getTotalSize());
            }
            // 尝试构建访问URL
            try {
                String accessUrl = "/File/proxy/" + chunkInfo.getObjectName();
                result.put("url", accessUrl);
            } catch (Exception e) {
                log.warn("为完成的文件构建URL失败", e);
            }
        }
        return R.ok(result);
    }

    @ApiOperation("文件断点/分段下载、视频流播放")
    @GetMapping("split/stream/**")
    public void streamFile(HttpServletRequest request, HttpServletResponse response) {
        String objectName = extractPathFromPattern(request, "/stream/**");
        if (!StringUtils.isNotBlank(objectName)) {
            sendError(response, HttpServletResponse.SC_BAD_REQUEST, "请求的 objectName 为空");
            return;
        }
        // 当前分片逻辑主要基于MinIO，所以stream也默认从MinIO获取
        // 如果未来分片支持多种OSS，这里需要判断
        if (!minioInitialized) {
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "MinIO服务未初始化，无法提供流式服务");
            return;
        }
        try {
            // (核心逻辑参考 File_VideoController.streamFromMinio)
            // 使用 minioClient, configManager.getMinioBucket(), objectName
            // 处理HTTP Range请求，设置正确的响应头 (Content-Type, Content-Length, Content-Range, Accept-Ranges)
            streamMinioObject(minioClient, configManager.getMinioBucket(), objectName, request, response); // 复用之前的 streamMinioObject 或更详细的实现
        } catch (Exception e) {
            log.error("Stream error for object {}: {}", objectName, e.getMessage(), e);
            sendError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "流式传输文件失败.");
        }
    }

    @Scheduled(fixedRate = 60 * 60 * 1000) // 每小时清理过期的分片任务
    public void cleanupExpiredChunkUploads() {
        // ... (逻辑同 File_VideoController.cleanupExpiredChunks)
        log.info("Running scheduled cleanup of expired chunk uploads for AllFileController.");
    }

    // =====================================================================================
    // 3️⃣ 文本类
    // =====================================================================================

    @ApiOperation("文本内容上传（保存为纯文本文件）")
    @PostMapping("/uploadByText")
    public R<FileInfo> uploadByText(
            @RequestParam("text") String text,
            @RequestParam("dir") String dir,
            @RequestParam("filename") String filename,
            @RequestParam(value = "module", required = false) String module) { // filename应包含.txt或由服务器添加
        if (!StringUtils.isNotBlank(text) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("文本内容、目录和文件名不能为空");
        }
        try (InputStream inputStream = new ByteArrayInputStream(text.getBytes(StandardCharsets.UTF_8))) {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            // 确保文件名有.txt后缀 (可选，取决于具体需求)
            String effectiveFilename = filename.toLowerCase().endsWith(".txt") ? filename : filename + ".txt";
            String objectName = buildObjectName(dir, effectiveFilename, null);
            long size = text.getBytes(StandardCharsets.UTF_8).length;

            FileInfo fileInfo = service.uploadStream(inputStream, bucketName, objectName, size, "text/plain; charset=utf-8", module);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload text failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("文本上传失败: " + e.getMessage());
        }
    }

    @ApiOperation("字符串转.html文件格式上传")
    @PostMapping("/uploadByHtml")
    public R<FileInfo> uploadByHtml(
            @RequestParam("htmlContent") String htmlContent,
            @RequestParam("dir") String dir,
            @RequestParam("filename") String filename,
            @RequestParam(value = "module", required = false) String module) { // filename应包含.html或由服务器添加
        if (!StringUtils.isNotBlank(htmlContent) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("HTML内容、目录和文件名不能为空");
        }
        try (InputStream inputStream = new ByteArrayInputStream(htmlContent.getBytes(StandardCharsets.UTF_8))) {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();
            String effectiveFilename = filename.toLowerCase().endsWith(".html") ? filename : filename + ".html";
            String objectName = buildObjectName(dir, effectiveFilename, null);
            long size = htmlContent.getBytes(StandardCharsets.UTF_8).length;

            FileInfo fileInfo = service.uploadStream(inputStream, bucketName, objectName, size, "text/html; charset=utf-8", module);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("Upload HTML failed for dir: {}, filename: {}. Error: {}", dir, filename, e.getMessage(), e);
            return R.fail("HTML上传失败: " + e.getMessage());
        }
    }

    // =====================================================================================
    // 4️⃣ 图片类
    // =====================================================================================
    private static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB


// 假设已注入日志、OssService、常量等

    @ApiOperation("同时上传缩略图和原图")
    @PostMapping(value = "/upload2Image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<UploadImageVO> upload2Image(
            @RequestParam("file") MultipartFile file,
            @RequestParam("dir") String dir,
            @RequestParam(value = "filename", required = false) String filename,
            @RequestParam(value = "module", required = false) String module) {

        if (file.isEmpty()) return R.fail("图片文件不能为空");
        if (file.getSize() > MAX_IMAGE_SIZE) return R.fail("图片大小不能超过10MB");

        try {
            OssService service = getCurrentOssService();
            String bucketName = getCurrentBucketName();

            // 1. 上传原图
            String originalFileExtension = getFileExtension(file.getOriginalFilename());
            String baseFilename = StringUtils.isBlank(filename)
                    ? IdUtil.fastSimpleUUID()
                    : (filename.contains(".") ? filename.substring(0, filename.lastIndexOf(".")) : filename);

            String originEffectiveFilename = baseFilename + originalFileExtension;
            String originObjectName = buildObjectName(dir, originEffectiveFilename, null);
            FileInfo originFileInfo = service.upload(file, originObjectName, module);

            UploadImageVO vo = new UploadImageVO();
            vo.setOriginUrl(originFileInfo.getFileurl());

            // 2. 生成并上传缩略图（只处理大于30KB的图片）
            if (file.getSize() > 30 * 1024) {
                // 生成缩略图
                ByteArrayOutputStream thumbOut = new ByteArrayOutputStream();
                Thumbnails.of(file.getInputStream())
                        .size(200, 200)
                        .outputQuality(0.7)
                        .toOutputStream(thumbOut);
                byte[] thumbBytes = thumbOut.toByteArray();

                try (InputStream thumbStream = new ByteArrayInputStream(thumbBytes)) {
                    String thumbEffectiveFilename = baseFilename + "_thumb" + originalFileExtension;
                    String thumbObjectName = buildObjectName(dir, thumbEffectiveFilename, null);
                    FileInfo thumbFileInfo = service.uploadStream(
                            thumbStream,
                            bucketName,
                            thumbObjectName,
                            thumbBytes.length,
                            file.getContentType(),
                            module
                    );
                    vo.setThumbUrl(thumbFileInfo.getFileurl());
                }
            } else {
                vo.setThumbUrl(originFileInfo.getFileurl());
            }
            return R.ok(vo);

        } catch (Exception e) {
            log.error("Upload2Image failed. Error: {}", e.getMessage(), e);
            return R.fail("图片及缩略图上传失败: " + e.getMessage());
        }
    }


    @ApiOperation("Base64图上传 payload传入4参：base64Data, dir, filename, module")
    @PostMapping("/uploadByBase64")
    public R<FileInfo> uploadByBase64(@RequestBody Map<String, String> payload) {
        // payload 示例: {"base64Data": "data:image/png;base64,iVBORw0KG...", "dir": "images/avatars", "filename": "avatar.png"}
        String base64Data = payload.get("base64Data");
        String dir = payload.get("dir");
        String filename = payload.get("filename"); // 客户端应提供带后缀的文件名
        String module = payload.get("module"); // 客户端应提供带后缀的文件名

        if (!StringUtils.isNotBlank(base64Data) || !StringUtils.isNotBlank(dir) || !StringUtils.isNotBlank(filename)) {
            return R.fail("Base64数据、目录和文件名不能为空");
        }

        try {
            // 解码Base64 (注意处理 "data:image/png;base64," 前缀)
            String actualBase64Data = base64Data;
            if (base64Data.contains(",")) {
                actualBase64Data = base64Data.substring(base64Data.indexOf(",") + 1);
            }
            byte[] imageBytes = Base64.getDecoder().decode(actualBase64Data);

            try (InputStream inputStream = new ByteArrayInputStream(imageBytes)) {
                OssService service = getCurrentOssService();
                String bucketName = getCurrentBucketName();
                String objectName = buildObjectName(dir, filename, null); // filename应由客户端提供，包含正确后缀

                // 从文件名或Base64前缀中推断ContentType (简化)
                String contentType = "application/octet-stream"; // 默认
                if (filename.toLowerCase().endsWith(".png")) contentType = "image/png";
                else if (filename.toLowerCase().endsWith(".jpg") || filename.toLowerCase().endsWith(".jpeg"))
                    contentType = "image/jpeg";
                else if (filename.toLowerCase().endsWith(".gif")) contentType = "image/gif";
                //更可靠的方式是从 "data:image/png;base64," 前缀中提取

                FileInfo fileInfo = service.uploadStream(inputStream, bucketName, objectName, imageBytes.length, contentType, module);
                return R.ok(fileInfo);
            }
        } catch (Exception e) {
            log.error("Upload Base64 image failed. Error: {}", e.getMessage(), e);
            return R.fail("Base64图片上传失败: " + e.getMessage());
        }
    }

    // --- 内部类定义 (ChunkInfo, MergeProgress, FileInfo, UploadImageVO) ---
    // 这些类需要从 File_VideoController 和 FileController 迁移或重新定义
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChunkInfo {
        private String uploadId;
        private String originalFileName; // 用户上传的原始文件名
        private String fileName;         // MinIO上存储的文件名 (通常是UUID化的, 不含路径, 在complete时设置)
        private String objectName;       // MinIO上存储的完整对象路径 (dir/fileName, 在complete时设置)
        private long totalSize;
        private int chunkSize;
        private int totalChunks;
        private boolean[] uploadedChunks;
        private String[] chunkHashes;
        private String tempDir;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MergeProgress {
        private int current;
        private int total;
        private String status;
        private int percent;
        private String errorMessage;
        private boolean cancelled;
    }


    // ==============================  公共辅助 ==============================


    // streamMinioObject 和 streamAliyunObject 具体实现需要参考原代码并适配Range
    // 此处仅为示意，详细实现略
    private void streamMinioObject(MinioClient client, String bucket, String objectName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ObjectStat stat = client.statObject(StatObjectArgs.builder().bucket(bucket).object(objectName).build());
        // ... Range 处理逻辑, 设置 response headers, 获取流并 copy ... (参考File_VideoController.handleRangeRequest & streamObjectContent)
        log.info("Streaming MinIO object: {}/{}", bucket, objectName); // 示例日志
        // 简化版：直接下载整个文件
        try (InputStream is = client.getObject(GetObjectArgs.builder().bucket(bucket).object(objectName).build())) {
            response.setContentType(stat.contentType());
            response.setContentLengthLong(stat.length());
            IOUtils.copy(is, response.getOutputStream());
        }
    }

    private void streamAliyunObject(OSS client, String bucket, String objectName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ObjectMetadata meta = client.getObjectMetadata(bucket, objectName);
        // ... Range 处理逻辑, 设置 response headers, 获取流并 copy ...
        log.info("Streaming Aliyun object: {}/{}", bucket, objectName); // 示例日志
        // 简化版：直接下载整个文件
        try (InputStream is = client.getObject(bucket, objectName).getObjectContent()) {
            response.setContentType(meta.getContentType());
            response.setContentLengthLong(meta.getContentLength());
            IOUtils.copy(is, response.getOutputStream());
        }
    }


    /**
     * 目录规则鉴权；失败时可写回 403。
     *
     * @param objectName 对象完整路径
     * @param auth       (可选) 独立授权码
     * @param sec        (可选) 标准请求令牌 (如从 Header 或 Query 中获取)
     * @param resp       (可选) 为 null 时仅返回校验结果，不为 null 时会在失败时写入 403 状态
     * @return boolean 校验是否通过
     */
    private boolean verifyAccess(String objectName, String auth, String sec, HttpServletResponse resp) {
        List<SaDirrulePojo> rules = saRedisService.getCacheObject(MyConstant.DIR_RULE_LIST, new TypeReference<List<SaDirrulePojo>>() {
        });

        // 1. publicmark=1 直接放行
        boolean isPublic = rules != null && rules.stream()
                .anyMatch(r -> Objects.equals(r.getPublicmark(), 1) && objectName.startsWith(r.getDirname()));
        if (isPublic) {
            log.debug("verifyAccess => public dir pass for object: {}", objectName);
            return true;
        }

        // 2. 非公共目录 → 解析用户身份 (兼容 auth 和 sec)
        LoginUser user = null;
        if (StringUtils.isNotBlank(auth)) {
            // 使用 auth 授权码
            user = saRedisService.getLoginUserFromAuthCode(auth, null);
        }
        if (StringUtils.isNotBlank(sec)) {
            // 使用 sec
            user = saRedisService.getLoginUserFromToken(sec);
        }
        if (user == null) {
            // auth、sec都无 从请求头拿
            user = saRedisService.getLoginUser();
        }

        if (user == null) {
            log.warn("verifyAccess => failed to get user for object: {}. Auth or Token may be invalid.", objectName);
            if (resp != null) resp.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return false;
        }
        String userId = user.getUserid();

        SaDirrulePojo matched = rules == null ? null :
                rules.stream().filter(r -> objectName.startsWith(r.getDirname()))
                        .findFirst().orElse(null);

        // 如果没有匹配到任何规则，但目录又非公共，默认放行（或根据业务要求拒绝）
        if (matched == null) {
            log.debug("verifyAccess => no specific rule matched for non-public dir, pass. user={}, object={}", userId, objectName);
            return true;
        }

        // 3. 黑名单校验
        if (StringUtils.isNotBlank(matched.getBlackuserids())) {
            boolean inBlack = Arrays.asList(matched.getBlackuserids().split(",")).contains(userId);
            if (inBlack) {
                log.warn("verifyAccess => user={} in black list, dir={}", userId, matched.getDirname());
                if (resp != null) resp.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return false;
            }
        }

        // 4. 白名单校验
        if (StringUtils.isNotBlank(matched.getWhiteuserids())) {
            boolean inWhite = Arrays.asList(matched.getWhiteuserids().split(",")).contains(userId);
            if (!inWhite) {
                log.warn("verifyAccess => user={} not in white list, dir={}", userId, matched.getDirname());
                if (resp != null) resp.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return false;
            }
        }

        log.debug("verifyAccess => pass. user={} dir={}", userId, matched.getDirname());
        return true;
    }

    /**
     * 打开 OSS / MinIO 对象流（只读）
     */
    private InputStream openObjectStream(String bucket, String objectName) throws Exception {
        String ossType = configManager.getOssType();
        if (OssConstant.OSSTYPE_MINIO.equalsIgnoreCase(ossType)) {
            return minioClient.getObject(
                    GetObjectArgs.builder().bucket(bucket).object(objectName).build());
        } else if (OssConstant.OSSTYPE_ALIYUN.equalsIgnoreCase(ossType)) {
            return aliyunOssClient.getObject(bucket, objectName).getObjectContent();
        }
        throw new IllegalStateException("Unsupported OSS type: " + ossType);
    }


    /**
     * 判断文件是否应自动下载（图片等inline，其他自动attachment）
     */
    private static final Set<String> INLINE_SUFFIX = new HashSet<>(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"
    ));
    private static final Set<String> INLINE_MIME = new HashSet<>(Arrays.asList(
            "image/", "video/", "audio/", "text/plain", "application/pdf"
    ));

    /**
     * 根据文件名推断 content type
     */
    private String inferContentType(String filename) {
        // 简单的根据文件扩展名判断
        if (filename == null) return "application/octet-stream";

        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFilename.endsWith(".png")) {
            return "image/png";
        } else if (lowerFilename.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFilename.endsWith(".mp4")) {
            return "video/mp4";
        } else if (lowerFilename.endsWith(".mp3")) {
            return "audio/mpeg";
        } else if (lowerFilename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFilename.endsWith(".doc") || lowerFilename.endsWith(".docx")) {
            return "application/msword";
        } else if (lowerFilename.endsWith(".xls") || lowerFilename.endsWith(".xlsx")) {
            return "application/vnd.ms-excel";
        }
        // 默认类型
        return "application/octet-stream";
    }
}