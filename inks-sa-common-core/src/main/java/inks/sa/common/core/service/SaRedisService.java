package inks.sa.common.core.service;

import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.sa.common.core.domain.pojo.SaRedisPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MySQL暂替Redis(SaRedis)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-16 15:13:02
 */
public interface SaRedisService {


    SaRedisPojo getEntity(String key);

    PageInfo<SaRedisPojo> getPageList(QueryParam queryParam);

    SaRedisPojo insert(SaRedisPojo saRedisPojo);

    SaRedisPojo update(SaRedisPojo saRedispojo);

    int delete(String key);

    int deleteObject(String key);

    //============================================仿造Redis方法=========================================================
    //  setKeyValue 等同于 setCacheObject:
    <T> void setKeyValue(String redisKey, T redisValue, long timeout);

    <T> void setCacheObject(String redisKey, T redisValue, long timeout);

    <T> void setKeyValue(String redisKey, T redisValue, long timeout, TimeUnit timeUnit);

    <T> void setCacheObject(String redisKey, T redisValue, long timeout, TimeUnit timeUnit);

    LoginUser getLoginUser();

    LoginUser getLoginUser(HttpServletRequest request);

    LoginUser getLoginUserFromToken(String token);

    LoginUser getLoginUserFromAuthCode(String authCode, HttpServletRequest request);

    MyLoginUser getMyLoginUser(HttpServletRequest request);

    int cleanSa_Redis();

    String getValue(String redisKey);

    <T> T getCacheObject(String redisKey, Class<T> type);

    <T> T getCacheObject(String redisKey, TypeReference<T> typeRef);

    ReportsPojo getCacheObject(String redisKey);

    <T> void setCacheMapValue(String redisKey, String hKey, T redisValue);

    <T> T getCacheMapValue(String redisKey, String hKey);

    Boolean setIfAbsent(String redisKey, String value, long timeout, TimeUnit timeUnit);

    Map<String, Object> createToken(LoginUser loginUserNew);
}
