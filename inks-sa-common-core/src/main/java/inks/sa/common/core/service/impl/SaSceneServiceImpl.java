package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaSceneEntity;
import inks.sa.common.core.domain.pojo.SaScenePojo;
import inks.sa.common.core.mapper.SaSceneMapper;
import inks.sa.common.core.service.SaSceneService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 场景管理(Sascene)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
@Service("sasceneService")
public class SaSceneServiceImpl implements SaSceneService {
    @Resource
    private SaSceneMapper sasceneMapper;


    @Override
    public SaScenePojo getEntity(String key, String tid) {
        return this.sasceneMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<SaScenePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaScenePojo> lst = sasceneMapper.getPageList(queryParam);
            PageInfo<SaScenePojo> pageInfo = new PageInfo<SaScenePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param sascenePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScenePojo insert(SaScenePojo sascenePojo) {
        //初始化NULL字段
        if (sascenePojo.getModulecode() == null) sascenePojo.setModulecode("");
        if (sascenePojo.getScenename() == null) sascenePojo.setScenename("");
        if (sascenePojo.getScenedata() == null) sascenePojo.setScenedata("");
        if (sascenePojo.getRownum() == null) sascenePojo.setRownum(0);
        if (sascenePojo.getEnabledmark() == null) sascenePojo.setEnabledmark(0);
        if (sascenePojo.getRemark() == null) sascenePojo.setRemark("");
        if (sascenePojo.getCreateby() == null) sascenePojo.setCreateby("");
        if (sascenePojo.getCreatebyid() == null) sascenePojo.setCreatebyid("");
        if (sascenePojo.getCreatedate() == null) sascenePojo.setCreatedate(new Date());
        if (sascenePojo.getLister() == null) sascenePojo.setLister("");
        if (sascenePojo.getListerid() == null) sascenePojo.setListerid("");
        if (sascenePojo.getModifydate() == null) sascenePojo.setModifydate(new Date());
        if (sascenePojo.getCustom1() == null) sascenePojo.setCustom1("");
        if (sascenePojo.getCustom2() == null) sascenePojo.setCustom2("");
        if (sascenePojo.getCustom3() == null) sascenePojo.setCustom3("");
        if (sascenePojo.getCustom4() == null) sascenePojo.setCustom4("");
        if (sascenePojo.getCustom5() == null) sascenePojo.setCustom5("");
        if (sascenePojo.getTenantid() == null) sascenePojo.setTenantid("");
        if (sascenePojo.getTenantname() == null) sascenePojo.setTenantname("");
        if (sascenePojo.getRevision() == null) sascenePojo.setRevision(0);
        SaSceneEntity sasceneEntity = new SaSceneEntity();
        BeanUtils.copyProperties(sascenePojo, sasceneEntity);

        sasceneEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        sasceneEntity.setRevision(1);  //乐观锁
        this.sasceneMapper.insert(sasceneEntity);
        return this.getEntity(sasceneEntity.getId(), sasceneEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param sascenePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScenePojo update(SaScenePojo sascenePojo) {
        SaSceneEntity sasceneEntity = new SaSceneEntity();
        BeanUtils.copyProperties(sascenePojo, sasceneEntity);
        this.sasceneMapper.update(sasceneEntity);
        return this.getEntity(sasceneEntity.getId(), sasceneEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.sasceneMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<SaScenePojo> getListByCode(String code, String userid, String tid) {

        return this.sasceneMapper.getListByCode(code, userid, tid);
    }


}
