//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package inks.sa.common.core.annotation.aspect;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import java.lang.reflect.Field;
import java.util.Collection;

import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.PatternMatchUtils;

@Component
public class Sa_FieldFilterService {
    private static final Logger logger = LoggerFactory.getLogger(Sa_FieldFilterService.class);

    public Sa_FieldFilterService() {
    }

    public static void filterAmount(Object data, LoginUser loginUser, String permission) {
        try {
            String configValue = InksConfigThreadLocal_Sa.getConfig("system.bill.amountfilter");
            if (!"true".equals(configValue)) {
                return;
            }

            if (hasPermi(loginUser, permission)) {
                return;
            }

            processObject(data);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static void processObject(Object obj) {
        if (obj != null) {
            if (obj instanceof PageInfo) {
                PageInfo<?> pageInfo = (PageInfo)obj;
                processObject(pageInfo.getList());
            } else if (obj instanceof Collection) {
                for(Object item : (Collection)obj) {
                    processObject(item);
                }

            } else {
                filterAmountFields(obj);

                for(Class<?> currentClass = obj.getClass(); currentClass != null && currentClass != Object.class; currentClass = currentClass.getSuperclass()) {
                    for(Field field : currentClass.getDeclaredFields()) {
                        field.setAccessible(true);

                        try {
                            Object fieldValue = field.get(obj);
                            if (fieldValue != null && (fieldValue instanceof Collection || !isJavaType(field.getType()))) {
                                processObject(fieldValue);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

            }
        }
    }

    private static boolean isJavaType(Class<?> clazz) {
        return clazz.getName().startsWith("java.") || clazz.isPrimitive() || clazz.getName().startsWith("javax.") || clazz.getName().startsWith("org.springframework.");
    }

    private static void filterAmountFields(Object obj) {
        if (obj != null) {
            for(Class<?> currentClass = obj.getClass(); currentClass != null && currentClass != Object.class; currentClass = currentClass.getSuperclass()) {
                Field[] fields = currentClass.getDeclaredFields();

                for(Field field : fields) {
                    if (field.isAnnotationPresent(JsonInclude.class)) {
                        field.setAccessible(true);

                        try {
                            field.set(obj, (Object)null);
                            logger.info("敏感字段过滤：" + currentClass.getName() + "." + field.getName());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

        }
    }

    public static boolean hasPermi(LoginUser userInfo, String permission) {
        if (userInfo == null) {
            return false;
        } else if (userInfo.getIsadmin() != null && userInfo.getIsadmin() == 1) {
            return true;
        } else {
            return !CollectionUtils.isEmpty(userInfo.getPermissions()) && hasPermissions(userInfo.getPermissions(), permission);
        }
    }

    private static boolean hasPermissions(Collection<String> authorities, String permission) {
        return authorities.stream().filter((x) -> x != null && !x.trim().isEmpty()).anyMatch((x) -> PatternMatchUtils.simpleMatch(x, permission));
    }
}
