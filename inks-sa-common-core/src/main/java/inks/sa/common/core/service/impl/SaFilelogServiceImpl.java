package inks.sa.common.core.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.domain.SaFilelogEntity;
import inks.sa.common.core.mapper.SaFilelogMapper;
import inks.sa.common.core.service.SaFilelogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 文件上传/下载日志表(SaFilelog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 14:22:57
 */
@Service("saFilelogService")
public class SaFilelogServiceImpl implements SaFilelogService {
    @Resource
    private SaFilelogMapper saFilelogMapper;

    @Override
    public SaFilelogPojo getEntity(String key) {
        return this.saFilelogMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaFilelogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFilelogPojo> lst = saFilelogMapper.getPageList(queryParam);
            PageInfo<SaFilelogPojo> pageInfo = new PageInfo<SaFilelogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaFilelogPojo insert(SaFilelogPojo saFilelogPojo) {
        //初始化NULL字段
        cleanNull(saFilelogPojo);
        SaFilelogEntity saFilelogEntity = new SaFilelogEntity(); 
        BeanUtils.copyProperties(saFilelogPojo,saFilelogEntity);
        //生成雪花id
          saFilelogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFilelogEntity.setRevision(1);  //乐观锁
          this.saFilelogMapper.insert(saFilelogEntity);
        return this.getEntity(saFilelogEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFilelogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFilelogPojo update(SaFilelogPojo saFilelogPojo) {
        SaFilelogEntity saFilelogEntity = new SaFilelogEntity(); 
        BeanUtils.copyProperties(saFilelogPojo,saFilelogEntity);
        this.saFilelogMapper.update(saFilelogEntity);
        return this.getEntity(saFilelogEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saFilelogMapper.delete(key) ;
    }
    

    private static void cleanNull(SaFilelogPojo saFilelogPojo) {
        if(saFilelogPojo.getOptype()==null) saFilelogPojo.setOptype(0);
        if(saFilelogPojo.getUsedmark()==null) saFilelogPojo.setUsedmark(0);
        if(saFilelogPojo.getFileoriname()==null) saFilelogPojo.setFileoriname("");
        if(saFilelogPojo.getBucketname()==null) saFilelogPojo.setBucketname("");
        if(saFilelogPojo.getDirname()==null) saFilelogPojo.setDirname("");
        if(saFilelogPojo.getFilename()==null) saFilelogPojo.setFilename("");
        if(saFilelogPojo.getFileurl()==null) saFilelogPojo.setFileurl("");
        if(saFilelogPojo.getModulecode()==null) saFilelogPojo.setModulecode("");
        if(saFilelogPojo.getModule()==null) saFilelogPojo.setModule("");
        if(saFilelogPojo.getContenttype()==null) saFilelogPojo.setContenttype("");
        if(saFilelogPojo.getFilesuffix()==null) saFilelogPojo.setFilesuffix("");
        if(saFilelogPojo.getStorage()==null) saFilelogPojo.setStorage("");
        if(saFilelogPojo.getRownum()==null) saFilelogPojo.setRownum(0);
        if(saFilelogPojo.getRemark()==null) saFilelogPojo.setRemark("");
        if(saFilelogPojo.getCreateby()==null) saFilelogPojo.setCreateby("");
        if(saFilelogPojo.getCreatebyid()==null) saFilelogPojo.setCreatebyid("");
        if(saFilelogPojo.getCreatedate()==null) saFilelogPojo.setCreatedate(new Date());
        if(saFilelogPojo.getLister()==null) saFilelogPojo.setLister("");
        if(saFilelogPojo.getListerid()==null) saFilelogPojo.setListerid("");
        if(saFilelogPojo.getModifydate()==null) saFilelogPojo.setModifydate(new Date());
        if(saFilelogPojo.getCustom1()==null) saFilelogPojo.setCustom1("");
        if(saFilelogPojo.getCustom2()==null) saFilelogPojo.setCustom2("");
        if(saFilelogPojo.getCustom3()==null) saFilelogPojo.setCustom3("");
        if(saFilelogPojo.getCustom4()==null) saFilelogPojo.setCustom4("");
        if(saFilelogPojo.getCustom5()==null) saFilelogPojo.setCustom5("");
        if(saFilelogPojo.getCustom6()==null) saFilelogPojo.setCustom6("");
        if(saFilelogPojo.getCustom7()==null) saFilelogPojo.setCustom7("");
        if(saFilelogPojo.getCustom8()==null) saFilelogPojo.setCustom8("");
        if(saFilelogPojo.getCustom9()==null) saFilelogPojo.setCustom9("");
        if(saFilelogPojo.getCustom10()==null) saFilelogPojo.setCustom10("");
        if(saFilelogPojo.getTenantid()==null) saFilelogPojo.setTenantid("");
        if(saFilelogPojo.getRevision()==null) saFilelogPojo.setRevision(0);
   }

}
