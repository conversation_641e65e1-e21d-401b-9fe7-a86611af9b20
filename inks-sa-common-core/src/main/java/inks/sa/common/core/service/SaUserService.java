package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaUserPojo;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 用户(SaUser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
public interface SaUserService {


    SaUserPojo getEntity(String key);

    PageInfo<SaUserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    SaUserPojo insert(SaUserPojo saUserPojo) throws Exception;

    /**
     * 修改数据
     *
     * @param saUserpojo 实例对象
     * @return 实例对象
     */
    SaUserPojo update(SaUserPojo saUserpojo) throws Exception;

    int delete(String key, String tid);

    Map<String, Object> token(LoginUser loginUser);

    SaUserPojo getUserInfo(LoginUser loginUser);

    Map<String, Object> loginCheck(String username, String password, HttpServletRequest request, String type);

    Map<String, Object> loginCheckByOpenid(String openid, HttpServletRequest request);


    boolean checkUsername(String username, String id);

    SaUserPojo getEntityByDingUserid(String dingUserid);

    SaUserPojo getEntityByJustAuth(String authtype, String authuuid);

    LoginUser getLoginUserByJust(String wxe, String userid);
}
