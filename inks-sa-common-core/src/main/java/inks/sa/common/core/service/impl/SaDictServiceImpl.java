package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaDictEntity;
import inks.sa.common.core.domain.SaDictitemEntity;
import inks.sa.common.core.domain.pojo.SaDictPojo;
import inks.sa.common.core.domain.pojo.SaDictitemPojo;
import inks.sa.common.core.domain.pojo.SaDictitemdetailPojo;
import inks.sa.common.core.mapper.SaDictMapper;
import inks.sa.common.core.mapper.SaDictitemMapper;
import inks.sa.common.core.service.SaDictService;
import inks.sa.common.core.service.SaDictitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 数据字典(SaDict)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:09
 */
@Service("saDictService")
public class SaDictServiceImpl implements SaDictService {
    @Resource
    private SaDictMapper saDictMapper;

    @Resource
    private SaDictitemMapper saDictitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaDictitemService saDictitemService;


    @Override
    public SaDictPojo getEntity(String key) {
        return this.saDictMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDictitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDictitemdetailPojo> lst = saDictMapper.getPageList(queryParam);
            PageInfo<SaDictitemdetailPojo> pageInfo = new PageInfo<SaDictitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaDictPojo getBillEntity(String key) {
        try {
            //读取主表
            SaDictPojo saDictPojo = this.saDictMapper.getEntity(key);
            //读取子表
            saDictPojo.setItem(saDictitemMapper.getList(saDictPojo.getId()));
            return saDictPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDictPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDictPojo> lst = saDictMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saDictitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaDictPojo> pageInfo = new PageInfo<SaDictPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDictPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDictPojo> lst = saDictMapper.getPageTh(queryParam);
            PageInfo<SaDictPojo> pageInfo = new PageInfo<SaDictPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDictPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaDictPojo insert(SaDictPojo saDictPojo) {
//初始化NULL字段
        if (saDictPojo.getDictgroupid() == null) saDictPojo.setDictgroupid("");
        if (saDictPojo.getDictcode() == null) saDictPojo.setDictcode("");
        if (saDictPojo.getDictname() == null) saDictPojo.setDictname("");
        if (saDictPojo.getModulecode() == null) saDictPojo.setModulecode("");
        if (saDictPojo.getEnabledmark() == null) saDictPojo.setEnabledmark(0);
        if (saDictPojo.getRownum() == null) saDictPojo.setRownum(0);
        if (saDictPojo.getSummary() == null) saDictPojo.setSummary("");
        if (saDictPojo.getCreateby() == null) saDictPojo.setCreateby("");
        if (saDictPojo.getCreatebyid() == null) saDictPojo.setCreatebyid("");
        if (saDictPojo.getCreatedate() == null) saDictPojo.setCreatedate(new Date());
        if (saDictPojo.getLister() == null) saDictPojo.setLister("");
        if (saDictPojo.getListerid() == null) saDictPojo.setListerid("");
        if (saDictPojo.getModifydate() == null) saDictPojo.setModifydate(new Date());
        if (saDictPojo.getCustom1() == null) saDictPojo.setCustom1("");
        if (saDictPojo.getCustom2() == null) saDictPojo.setCustom2("");
        if (saDictPojo.getCustom3() == null) saDictPojo.setCustom3("");
        if (saDictPojo.getCustom4() == null) saDictPojo.setCustom4("");
        if (saDictPojo.getCustom5() == null) saDictPojo.setCustom5("");
        if (saDictPojo.getCustom6() == null) saDictPojo.setCustom6("");
        if (saDictPojo.getCustom7() == null) saDictPojo.setCustom7("");
        if (saDictPojo.getCustom8() == null) saDictPojo.setCustom8("");
        if (saDictPojo.getCustom9() == null) saDictPojo.setCustom9("");
        if (saDictPojo.getCustom10() == null) saDictPojo.setCustom10("");
        if (saDictPojo.getTenantid() == null) saDictPojo.setTenantid("");
        if (saDictPojo.getTenantname() == null) saDictPojo.setTenantname("");
        if (saDictPojo.getRevision() == null) saDictPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaDictEntity saDictEntity = new SaDictEntity();
        BeanUtils.copyProperties(saDictPojo, saDictEntity);
        //设置id和新建日期
        saDictEntity.setId(id);
        saDictEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saDictMapper.insert(saDictEntity);
        //Item子表处理
        List<SaDictitemPojo> lst = saDictPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SaDictitemPojo itemPojo = this.saDictitemService.clearNull(lst.get(i));
                SaDictitemEntity saDictitemEntity = new SaDictitemEntity();
                BeanUtils.copyProperties(itemPojo, saDictitemEntity);
                //设置id和Pid
                saDictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saDictitemEntity.setPid(id);
                saDictitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saDictitemMapper.insert(saDictitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saDictEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDictPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaDictPojo update(SaDictPojo saDictPojo) {
        //主表更改
        SaDictEntity saDictEntity = new SaDictEntity();
        BeanUtils.copyProperties(saDictPojo, saDictEntity);
        this.saDictMapper.update(saDictEntity);
        if (saDictPojo.getItem() != null) {
            //Item子表处理
            List<SaDictitemPojo> lst = saDictPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saDictMapper.getDelItemIds(saDictPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saDictitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaDictitemEntity saDictitemEntity = new SaDictitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaDictitemPojo itemPojo = this.saDictitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saDictitemEntity);
                        //设置id和Pid
                        saDictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saDictitemEntity.setPid(saDictEntity.getId());  // 主表 id
                        saDictitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saDictitemMapper.insert(saDictitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saDictitemEntity);
                        this.saDictitemMapper.update(saDictitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saDictEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaDictPojo saDictPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaDictitemPojo> lst = saDictPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saDictitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saDictMapper.delete(key);
    }

    @Override
    public SaDictPojo getBillEntityByDictCode(String key) {
        try {
            //读取主表
            SaDictPojo cidictPojo = this.saDictMapper.getEntityByDictCode(key);
            //读取子表
            if (cidictPojo == null)  //如果容，读默认的
                cidictPojo = this.saDictMapper.getEntityByDictCode(key);
            if (cidictPojo != null)
                cidictPojo.setItem(saDictitemMapper.getList(cidictPojo.getId()));
            return cidictPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


}
