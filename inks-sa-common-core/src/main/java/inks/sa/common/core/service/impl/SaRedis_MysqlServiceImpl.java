package inks.sa.common.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.PreAuthorizeException;
import inks.common.core.utils.*;
import inks.common.security.sa.service.TokenService;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.controller.A_SaUserController;
import inks.sa.common.core.domain.SaRedisEntity;
import inks.sa.common.core.domain.pojo.SaRedisPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.mapper.SaRedisMapper;
import inks.sa.common.core.service.SaRedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * MySQL暂替Redis(SaRedis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-16 15:13:03
 */
@Service("saRedisService")
@ConditionalOnProperty(name = "inks.redisType", havingValue = "mysql")
public class SaRedis_MysqlServiceImpl implements SaRedisService {
    @Resource
    private SaRedisMapper saRedisMapper;
    @Resource
    private A_SaUserController a_SaUserController;
    @Value("${inks.redisType:mysql}")
    private String redisType;
    @Resource
    private TokenService tokenInSaRedisService;

    @Override
    public LoginUser getLoginUser(HttpServletRequest request) {
        String authCode = SecurityUtils.getAuthCode(request);

        // 判断是否使用 MySQL 存储类型
        if ("mysql".equals(redisType)) {
            // 根据authCode是否为空来处理
            if (isBlank(authCode)) {
                return getLoginUserFromToken(SecurityUtils.getToken(request));
            } else {
                return getLoginUserFromAuthCode(authCode, request);
            }
        } else {
            // 其他 redisType 处理逻辑
            return tokenInSaRedisService.getLoginUser();
        }
    }

    @Override
    public SaRedisPojo getEntity(String key) {
        return this.saRedisMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaRedisPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRedisPojo> lst = saRedisMapper.getPageList(queryParam);
            PageInfo<SaRedisPojo> pageInfo = new PageInfo<SaRedisPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saRedisPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRedisPojo insert(SaRedisPojo saRedisPojo) {
//        // 检查redisKey是否存在   (redis的redisTemplate.opsForValue().set(key, value, timeout, timeUnit);方法会覆盖原有的key,所以不用检查)
//        if (this.saRedisMapper.getEntity(saRedisPojo.getRediskey()) != null) {
//            throw new BaseBusinessException("RedisKey已存在");
//        }
        //初始化NULL字段
        if (saRedisPojo.getRedisvalue() == null) throw new BaseBusinessException("RedisValue不能为空");
        SaRedisEntity saRedisEntity = new SaRedisEntity();
        BeanUtils.copyProperties(saRedisPojo, saRedisEntity);
        saRedisEntity.setCreatetime(System.currentTimeMillis());
        this.saRedisMapper.insert(saRedisEntity);
        return this.getEntity(saRedisEntity.getRediskey());
    }

    /**
     * 修改数据
     *
     * @param saRedisPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRedisPojo update(SaRedisPojo saRedisPojo) {
        SaRedisEntity saRedisEntity = new SaRedisEntity();
        BeanUtils.copyProperties(saRedisPojo, saRedisEntity);
        saRedisEntity.setCreatetime(System.currentTimeMillis());
        this.saRedisMapper.update(saRedisEntity);
        return this.getEntity(saRedisEntity.getRediskey());
    }

    @Override
    public int delete(String key) {
        return this.saRedisMapper.delete(key);
    }

    @Override
    public int deleteObject(String key) {
        return this.saRedisMapper.delete(key);
    }

    @Override
    public void setKeyValue(String redisKey, Object redisValue, long timeout) {
        this.setKeyValue(redisKey, redisValue, timeout, TimeUnit.SECONDS);
    }

    @Override
    public <T> void setCacheObject(String redisKey, T redisValue, long timeout) {
        this.setKeyValue(redisKey, redisValue, timeout, TimeUnit.SECONDS);
    }

    @Override
    public <T> void setKeyValue(String redisKey, T redisValue, long timeout, TimeUnit timeUnit) {
        SaRedisPojo saRedisPojo = new SaRedisPojo();
        saRedisPojo.setRediskey(redisKey);
//        这个条件首先检查是否为 null，然后使用 getClass().isPrimitive() 来判断是否为基本数据类型: Boolean.TYPE, Character.TYPE, Byte.TYPE, Short.TYPE, Integer.TYPE, Long.TYPE, Float.TYPE, Double.TYPE, Void.TYPE
//        接着，它检查是否为 Number 或 String 的实例。如果是这些类型之一，直接将其转换为字符串；否则，使用 FastJSON 将其转换为 JSON 字符串。
//        请注意，这个方式可能不会涵盖所有特殊情况，如自定义类等。如果你的应用中存在其他特殊类型，你可能需要根据实际情况进行调整。
        if (redisValue == null || redisValue instanceof String || redisValue.getClass().isPrimitive() || redisValue instanceof Number) {
            saRedisPojo.setRedisvalue(String.valueOf(redisValue));
        } else {
            saRedisPojo.setRedisvalue(JSON.toJSONString(redisValue));
        }
        if (timeout == -1) {
            saRedisPojo.setExpiretime(-1L);
        } else {
            // 设置过期时间: 时间戳+毫秒数
            long expireTime = System.currentTimeMillis() + timeUnit.toMillis(timeout);
            saRedisPojo.setExpiretime(expireTime);
        }
        // 查询Sa_Redis表中是否存在该键,  有则覆盖, 无则新增
        SaRedisPojo saRedisPojoDB = saRedisMapper.getEntity(redisKey);
        if (saRedisPojoDB == null) {
            this.insert(saRedisPojo);
        } else {
            this.update(saRedisPojo);
        }
    }

    @Override
    public <T> void setCacheObject(String redisKey, T redisValue, long timeout, TimeUnit timeUnit) {
        setKeyValue(redisKey, redisValue, timeout, timeUnit);
    }

    @Override
    public LoginUser getLoginUser() {
        return getLoginUser(ServletUtils.getRequest());
    }


    @Override
    public LoginUser getLoginUserFromToken(String token) {
        String tokenKey = MyConstant.LOGIN_TOKEN_KEY + token;
        String redisValue = saRedisMapper.getValue(tokenKey);
        if (isBlank(redisValue)) {
            throw new PreAuthorizeException("userinfo is null");
        }
        return JSONArray.parseObject(redisValue, LoginUser.class);
    }

    // 从Sa_Redis表中通过 键authCode获取登录用户信息；
    // 获取不到则调用authCode进行登录并将 authCode和token的映射 存入 Sa_Redis 表
    @Override
    public LoginUser getLoginUserFromAuthCode(String authCode, HttpServletRequest request) {
        LoginUser loginUser = getLoginUserByAuthCode(authCode);

        if (loginUser != null) {
            return loginUser;
        } else {
            R<Map<String, Object>> mapR = a_SaUserController.loginByAuthCode(authCode, request, null);
            if (mapR.getCode() != 200) {
                throw new BaseBusinessException(mapR.getMsg());
            }
            Map<String, Object> data = mapR.getData();
            String loginUserJson = JSON.toJSONString(data.get("loginuser"));

            try {
                return JSON.parseObject(loginUserJson, LoginUser.class);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }

        }
    }

    @Override
    public MyLoginUser getMyLoginUser(HttpServletRequest request) {
        if (!"mysql".equals(redisType)) {
            return null;
        }

        String token = "login_tokens:" + SecurityUtils.getToken(request);
        String redisValue = saRedisMapper.getValue(token);

        if (StringUtils.isNotBlank(redisValue)) {
            return JSON.parseObject(redisValue, MyLoginUser.class);
        }

        String authCode = SecurityUtils.getAuthCode(request);
        if (StringUtils.isBlank(authCode)) {
            throw new BaseBusinessException("登录已过期,请重新登录");
        }

        LoginUser loginUserFromAuthCode = getLoginUserFromAuthCode(authCode, request);
        if (loginUserFromAuthCode == null) {
            throw new BaseBusinessException("登录已过期,请重新登录");
        }

        return JSON.parseObject(JSON.toJSONString(loginUserFromAuthCode), MyLoginUser.class);
    }


    // 从Sa_Redis表中通过 键authCode获取登录用户信息
    private LoginUser getLoginUserByAuthCode(String authCode) {
        if (isNotBlank(authCode)) {
            try {
                String authKey = MyConstant.AUTHCODE_KEY + AESUtil.Encrypt(authCode);
                String token = getCacheObject(authKey, String.class);
                if (isNotBlank(token)) {
                    return getLoginUserFromToken(token);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        return null;
    }


    @Override
    public int cleanSa_Redis() {
        return saRedisMapper.cleanSa_Redis();
    }

    @Override
    public String getValue(String redisKey) {
        return saRedisMapper.getValue(redisKey);
    }

    public <T> T getCacheObject(String redisKey, Class<T> type) {
        String value = saRedisMapper.getValue(redisKey);

        if (value == null) {
            return null;
        }

        try {
            // 如果目标类型是 String，直接返回原始值
            if (type == String.class) {
                return type.cast(value);
            }

            // 如果目标类型是 Number，尝试将字符串转换为相应的数字类型
            if (Number.class.isAssignableFrom(type)) {
                return parseNumber(value, type);
            }

            // 如果目标类型是 Boolean，处理为布尔值
            if (type == Boolean.class || type == boolean.class) {
                return type.cast(Boolean.parseBoolean(value));
            }

            // 如果目标类型是 JSON 对象或数组，尝试解析为 JSON 对象或数组
            return JSON.parseObject(value, type);
        } catch (Exception e) {
            // 捕获并处理异常，返回null或者抛出自定义异常
            throw new RuntimeException("Failed to parse value: " + value + " to type: " + type.getName(), e);
        }
    }

    // 新增方法：处理泛型类型
    public <T> T getCacheObject(String redisKey, TypeReference<T> typeRef) {
        Object value = saRedisMapper.getValue(redisKey);
        if (value == null) {
            return null;
        }
        // 直接解析原始对象，避免不必要的JSON转换
        return JSON.parseObject(value.toString(), typeRef);
    }


    @SuppressWarnings("unchecked")
    private <T> T parseNumber(String value, Class<T> type) {
        if (type == Integer.class || type == int.class) {
            return (T) Integer.valueOf(value);
        } else if (type == Long.class || type == long.class) {
            return (T) Long.valueOf(value);
        } else if (type == Double.class || type == double.class) {
            return (T) Double.valueOf(value);
        } else if (type == Float.class || type == float.class) {
            return (T) Float.valueOf(value);
        } else if (type == Short.class || type == short.class) {
            return (T) Short.valueOf(value);
        } else if (type == Byte.class || type == byte.class) {
            return (T) Byte.valueOf(value);
        } else {
            throw new IllegalArgumentException("Unsupported number type: " + type.getName());
        }
    }


    public ReportsPojo getCacheObject(String redisKey) {
        String json = saRedisMapper.getValue(redisKey);
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, ReportsPojo.class);
    }

    public <T> void setCacheMapValue(String redisKey, String hKey, T redisValue, long timeout, TimeUnit timeUnit) {
        SaRedisPojo saRedisPojo = new SaRedisPojo();
        saRedisPojo.setRediskey(redisKey);
        saRedisPojo.setHkey(hKey);
//        这个条件首先检查是否为 null，然后使用 getClass().isPrimitive() 来判断是否为基本数据类型。
//        接着，它检查是否为 Number 或 Boolean 的实例。如果是这些类型之一，直接将其转换为字符串；否则，使用 FastJSON 将其转换为 JSON 字符串。
//        请注意，这个方式可能不会涵盖所有特殊情况，如自定义类等。如果你的应用中存在其他特殊类型，你可能需要根据实际情况进行调整。
        if (redisValue == null || redisValue.getClass().isPrimitive() || redisValue instanceof Number || redisValue instanceof Boolean) {
            saRedisPojo.setRedisvalue(String.valueOf(redisValue));
        } else {
            saRedisPojo.setRedisvalue(JSON.toJSONString(redisValue));
        }
        if (timeout == -1) {
            // 永不过期
            saRedisPojo.setExpiretime(-1L);
        } else {
            // 设置过期时间: 时间戳+毫秒数
            long expireTime = System.currentTimeMillis() + timeUnit.toMillis(timeout);
            saRedisPojo.setExpiretime(expireTime);
        }

        // 查询Sa_Redis表中是否存在该键和哈希内部的键
        SaRedisPojo saRedisPojoDB = saRedisMapper.getEntityByRedisKeyAndHKey(redisKey, hKey);
        if (saRedisPojoDB == null) {
            // 不存在则插入新记录
            this.insert(saRedisPojo);
        } else {
            // 存在则更新记录
            this.update(saRedisPojo);
        }
    }


    @Override
    public <T> void setCacheMapValue(String redisKey, String hKey, T redisValue) {
        this.setCacheMapValue(redisKey, hKey, redisValue, -1L, TimeUnit.SECONDS);
    }

    public <T> T getCacheMapValue(String redisKey, String hKey) {
        // 查询Sa_Redis表中是否存在该键和哈希内部的键
        SaRedisPojo saRedisPojo = saRedisMapper.getEntityByRedisKeyAndHKey(redisKey, hKey);
        if (saRedisPojo != null) {
            // 如果找到记录，返回对应的值
            String redisValue = saRedisPojo.getRedisvalue();
            // 使用FastJSON将字符串转换为泛型T的类型
            return JSON.parseObject(redisValue, new TypeReference<T>() {
            });
        } else {
            // 如果记录不存在，返回null或者其他默认值，取决于业务需求
            return null;
        }
    }

    // 已有值则返回false, 否则设置值并返回true
    @Override
    public Boolean setIfAbsent(String redisKey, String value, long timeout, TimeUnit timeUnit) {
        String valueDB = getValue(redisKey);
        if (isNotBlank(valueDB)) {
            return false;
        }
        setKeyValue(redisKey, value, timeout, timeUnit);
        return true;
    }

    @Override
    public Map<String, Object> createToken(LoginUser loginUser) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(loginUser.getUserid());
        loginUser.setUsername(loginUser.getUsername());
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        loginUser.setLogintime(System.currentTimeMillis());
        loginUser.setExpiretime(loginUser.getLogintime() + 43200000L);
        Map<String, Object> map = new HashMap();
        map.put("access_token", token);
        map.put("loginuser", loginUser);
        map.put("expires_in", 43200L);
        this.setCacheObject("login_tokens:" + token, loginUser, 43200L, TimeUnit.SECONDS);
        return map;
    }
}
