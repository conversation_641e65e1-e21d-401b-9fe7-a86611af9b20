package inks.sa.common.core.utils;

/**
 * PrintColor类用于在控制台打印彩色文本。
 * 该类支持以下颜色：
 * 1. 红色（"red" 或 "hong"）
 * 2. 绿色（"green" 或 "lv"）
 * 3. 黄色（"yellow" 或 "huang"）
 * 4. 蓝色（"blue" 或 "lan"）
 * 5. 紫色（"purple" 或 "zi"）
 * 6. 青色（"cyan" 或 "qing"）
 * 7. 白色（"white" 或 "bai"）
 * 如果输入的颜色名称不在上述列表中，则默认使用黑色。
 * <p>
 * 注意：这些颜色是基于ANSI颜色代码的，所以在大多数的终端和控制台环境中都能正常显示。
 * 但是不是所有的环境都支持ANSI颜色代码。如果在某个特定环境中无法看到颜色效果，
 * 那可能是因为该环境不支持ANSI颜色代码。
 *
 * <AUTHOR>
 * @since 2023/6/9 17:22
 */
public class PrintColor {
    public static void color(String color, String str) {
        switch (color) {
            case "red":
            case "hong":
                System.out.println("\033[31;4m" + str + "\033[0m");
                break;
            case "green":
            case "lv":
                System.out.println("\033[32;4m" + str + "\033[0m");
                break;
            case "yellow":
            case "huang":
                System.out.println("\033[33;4m" + str + "\033[0m");
                break;
            case "blue":
            case "lan":
                System.out.println("\033[34;4m" + str + "\033[0m");
                break;
            case "purple":
            case "zi":
                System.out.println("\033[35;4m" + str + "\033[0m");
                break;
            case "cyan":
            case "qing":
                System.out.println("\033[36;4m" + str + "\033[0m");
                break;
            case "white":
            case "bai":
                System.out.println("\033[37;4m" + str + "\033[0m");
                break;
            default:
                System.out.println("\033[30;4m" + str + "\033[0m");
                break;
        }
    }

    //不传颜色默认红色
    public static void red(String str) {
        color("red", str);
    }

    public static void color(String str) {
        color("red", str);
    }

    public static void printcolor(String str) {
        color("red", str);
    }

    public static void zi(String str) {
        color("zi", str);
    }

    public static void hong(String str) {
        color("hong", str);
    }

    public static void lv(String str) {
        color("lv", str);
    }

    public static void huang(String str) {
        color("huang", str);
    }

    public static void lan(String str) {
        color("lan", str);
    }

    public static void qing(String str) {
        color("qing", str);
    }

    public static void bai(String str) {
        color("bai", str);
    }

    public static void green(String str) {
        color("green", str);
    }

    public static void yellow(String str) {
        color("yellow", str);
    }

    public static void blue(String str) {
        color("blue", str);
    }

    public static void purple(String str) {
        color("purple", str);
    }

    public static void cyan(String str) {
        color("cyan", str);
    }

    public static void white(String str) {
        color("white", str);
    }
}