package inks.sa.common.core.feign;

import inks.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class TestUtsFeignContriller {
    @Resource
    private SaUtsFeignClient saUtsFeignClient;

    @GetMapping("/testUts")
    public String testUts() {
        R r = saUtsFeignClient.dingapprovel("11", "tid");
        return r.toString();
    }
}
