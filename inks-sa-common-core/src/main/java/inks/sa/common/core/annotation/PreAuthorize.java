package inks.sa.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PreAuthorize {
    String hasPermi() default "";

    String lacksPermi() default "";

    String[] hasAnyPermi() default {};

    String hasRole() default "";

    String lacksRole() default "";

    String[] hasAnyRoles() default {};
}
