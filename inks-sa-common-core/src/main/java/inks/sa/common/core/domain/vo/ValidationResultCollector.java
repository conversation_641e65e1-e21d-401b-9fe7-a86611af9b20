package inks.sa.common.core.domain.vo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 验证结果收集器
 */

public class ValidationResultCollector {
    private int totalCount;
    private int trueCount;
    private final List<Map<String, Object>> messages;
    private final Integer requiredMark;

    public ValidationResultCollector(Integer requiredMark) {
        this.requiredMark = requiredMark;
        this.messages = new ArrayList<>();
        this.trueCount = 0;
    }

    public void incrementTrueCount() {
        this.trueCount++;
    }

    public void addMessage(String message) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("msg", message);
        messageMap.put("requ", requiredMark);
        messages.add(messageMap);
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getTrueCount() {
        return trueCount;
    }

    public void setTrueCount(int trueCount) {
        this.trueCount = trueCount;
    }

    public List<Map<String, Object>> getMessages() {
        return messages;
    }

    public Integer getRequiredMark() {
        return requiredMark;
    }
}