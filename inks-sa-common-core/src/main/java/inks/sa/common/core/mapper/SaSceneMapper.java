package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaSceneEntity;
import inks.sa.common.core.domain.pojo.SaScenePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景管理(Sascene)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
@Mapper
public interface SaSceneMapper {


    SaScenePojo getEntity(@Param("key") String key, @Param("tid") String tid);


    List<SaScenePojo> getPageList(QueryParam queryParam);

    int insert(SaSceneEntity sasceneEntity);

    int update(SaSceneEntity sasceneEntity);


    int delete(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<SaScenePojo> getListByCode(@Param("code") String code, @Param("userid") String userid, @Param("tid") String tid);

}

