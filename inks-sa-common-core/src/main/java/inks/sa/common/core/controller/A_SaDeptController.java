package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.FieldFilter_Sa;
import inks.sa.common.core.domain.pojo.SaDeptPojo;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.mapper.SaDeptuserMapper;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.service.SaDeptService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户组织架构(Sa_Dept)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-05 12:36:29
 */
@RestController
@RequestMapping("SaDept")
@Api(tags = "通用:部门(组织架构）")
public class A_SaDeptController {
    @Resource
    private SaDeptService saDeptService;
    @Resource
    private SaDeptuserMapper saDeptuserMapper;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaUserMapper saUserMapper;

    private final static Logger logger = LoggerFactory.getLogger(A_SaDeptController.class);

    @ApiOperation(value = " 获取组织用户表详细信息 key是userid", notes = "获取组织用户表详细信息", produces = "application/json")
    @RequestMapping(value = "/getDeptinfolistByUser", method = RequestMethod.GET)
    public R<List<DeptinfoPojo>> getDeptinfolistByUser(String key) {
        try {

            return R.ok(this.saDeptService.getDeptinfoList(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @Description 注意 getDeptinfolistByUser方法 即使当前用户不是部门管理员,也会返回当前部门和所有下级部门信息,只不过部门信息的isdeptadmin字段都=0,
     * 且如果当前用户未关联部门,则返回null
     */
    @ApiOperation(value = "当前用户下所有部门", notes = "", produces = "application/json")
    @RequestMapping(value = "/getDeptinfolistByUserTB", method = RequestMethod.GET)
    @FieldFilter_Sa
    public R<List<DeptinfoPojo>> getDeptinfolistByUserTB() {
        try {
            // 获得用户数据
            String userid = saRedisService.getLoginUser().getUserid();
            return R.ok(this.saDeptService.getDeptinfolistByUser(userid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "当前用户下所有部门的员工信息", notes = "", produces = "application/json")
    @RequestMapping(value = "/getDeptUserinfolistByUser", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Dept.List")
    public R<List<SaUserPojo>> getDeptUserinfolistByUser() {
        try {
            // 获得用户数据
            String userid = saRedisService.getLoginUser().getUserid();
            List<DeptinfoPojo> deptinfolistByUser = this.saDeptService.getDeptinfolistByUser(userid);
            if (deptinfolistByUser == null) {
                throw new BaseBusinessException("当前用户未关联部门");
            }
            List<String> deptIds = deptinfolistByUser.stream().map(DeptinfoPojo::getDeptid).collect(Collectors.toList());
            List<String> useridList = saDeptuserMapper.getUseridListInDeptids(deptIds);// 部门id集合对应的所有用户id集合
            List<SaUserPojo> userPojoList = saUserMapper.getUserInfosByUserids(useridList);
            return R.ok(userPojoList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取用户组织架构详细信息", notes = "获取用户组织架构详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Dept.List")
    public R<SaDeptPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saDeptService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Dept.List")
    public R<PageInfo<SaDeptPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Dept.CreateDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDeptService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增用户组织架构", notes = "新增用户组织架构", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Dept.Add")
    public R<SaDeptPojo> create(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaDeptPojo saDeptPojo = JSONArray.parseObject(json, SaDeptPojo.class);
            saDeptPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDeptPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDeptPojo.setCreatedate(new Date());   // 创建时间
            saDeptPojo.setLister(loginUser.getRealname());   // 制表
            saDeptPojo.setListerid(loginUser.getUserid());    // 制表id
            saDeptPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDeptService.insert(saDeptPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改用户组织架构", notes = "修改用户组织架构", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Dept.Edit")
    public R<SaDeptPojo> update(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaDeptPojo saDeptPojo = JSONArray.parseObject(json, SaDeptPojo.class);
            saDeptPojo.setLister(loginUser.getRealname());   // 制表
            saDeptPojo.setListerid(loginUser.getUserid());    // 制表id
            saDeptPojo.setModifydate(new Date());   //修改时间
            //            saDeptPojo.setAssessor(""); // 审核员
            //            saDeptPojo.setAssessorid(""); // 审核员id
            //            saDeptPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDeptService.update(saDeptPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除用户组织架构", notes = "删除用户组织架构", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Dept.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saDeptService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Dept.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaDeptPojo saDeptPojo = this.saDeptService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDeptPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

