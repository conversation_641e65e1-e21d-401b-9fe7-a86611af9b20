package inks.sa.common.core.config.oss;

import java.io.InputStream;

/**
 * minio 存储
 *
 * <AUTHOR> lhm
 * @version : 1.0
 * @since : 2022-10-10
 */
public interface Storage {
    /**
     * 存对象
     *
     * @param bucketName  bucket名称
     * @param dirName  目录名称
     * @param objectName  对象名称
     * @param inputStream 输入流
     * @param contentType 内容类型
     * <AUTHOR> Yvon / 2020-11-04
     */
    void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType,String oriFileName) throws Exception;
    void putObject(String bucketName,  String objectName, InputStream inputStream, String contentType,String oriFileName) throws Exception;
    InputStream getObject(String bucketName, String objectName) throws Exception;

    /**
     * 删除对象
     *
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * <AUTHOR> Yvon / 2020-11-03
     */
    void removeObject(String bucketName, String objectName) throws Exception;
}
