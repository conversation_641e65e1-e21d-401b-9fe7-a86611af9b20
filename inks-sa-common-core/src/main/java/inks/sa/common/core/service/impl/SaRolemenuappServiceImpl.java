package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaRolemenuappEntity;
import inks.sa.common.core.domain.pojo.SaRolemenuappPojo;
import inks.sa.common.core.mapper.SaRolemenuappMapper;
import inks.sa.common.core.service.SaRolemenuappService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 角色菜单App(SaRolemenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Service("saRolemenuappService")
public class SaRolemenuappServiceImpl implements SaRolemenuappService {
    @Resource
    private SaRolemenuappMapper saRolemenuappMapper;

    @Override
    public SaRolemenuappPojo getEntity(String key) {
        return this.saRolemenuappMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaRolemenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRolemenuappPojo> lst = saRolemenuappMapper.getPageList(queryParam);
            PageInfo<SaRolemenuappPojo> pageInfo = new PageInfo<SaRolemenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaRolemenuappPojo insert(SaRolemenuappPojo saRolemenuappPojo) {
        //初始化NULL字段
        cleanNull(saRolemenuappPojo);
        SaRolemenuappEntity saRolemenuappEntity = new SaRolemenuappEntity(); 
        BeanUtils.copyProperties(saRolemenuappPojo,saRolemenuappEntity);
        //生成雪花id
          saRolemenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saRolemenuappEntity.setRevision(1);  //乐观锁
          this.saRolemenuappMapper.insert(saRolemenuappEntity);
        return this.getEntity(saRolemenuappEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saRolemenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRolemenuappPojo update(SaRolemenuappPojo saRolemenuappPojo) {
        SaRolemenuappEntity saRolemenuappEntity = new SaRolemenuappEntity(); 
        BeanUtils.copyProperties(saRolemenuappPojo,saRolemenuappEntity);
        this.saRolemenuappMapper.update(saRolemenuappEntity);
        return this.getEntity(saRolemenuappEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saRolemenuappMapper.delete(key) ;
    }

    @Override
    public Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String realname = loginUser.getRealname();
        String userid = loginUser.getUserid();
        int affectedRows = 0;
        if (CollectionUtils.isNotEmpty(deleteNavids)) {
            Integer i = this.saRolemenuappMapper.batchDelete(roleid, deleteNavids);
            affectedRows += i==null?0:i;
        }

        if (CollectionUtils.isNotEmpty(createNavids)) {
            //insert into PiRoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
            List<SaRolemenuappPojo> rolemenuappPojoList = new ArrayList<>();
            for (String navid : createNavids) {
                SaRolemenuappPojo saRolemenuappPojo = new SaRolemenuappPojo();
                saRolemenuappPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                saRolemenuappPojo.setRoleid(roleid);
                saRolemenuappPojo.setNavid(navid);
                // pirolemenuappPojo.setRownum(createNavids.indexOf(navid) + 1);
                saRolemenuappPojo.setCreateby(realname); // 示例：设置创建者
                saRolemenuappPojo.setCreatebyid(userid); // 示例：设置创建者ID
                saRolemenuappPojo.setLister(realname); // 示例：设置列出者
                saRolemenuappPojo.setListerid(userid); // 示例：设置列出者ID
//                pirolemenuappPojo.setTenantid(tid); // 示例：设置租户ID
                cleanNull(saRolemenuappPojo);
                rolemenuappPojoList.add(saRolemenuappPojo);
            }

            Integer i = this.saRolemenuappMapper.batchInsert(rolemenuappPojoList);
            affectedRows += i==null?0:i;
        }
        return affectedRows;
    }

    private static void cleanNull(SaRolemenuappPojo saRolemenuappPojo) {
        if(saRolemenuappPojo.getRoleid()==null) saRolemenuappPojo.setRoleid("");
        if(saRolemenuappPojo.getNavid()==null) saRolemenuappPojo.setNavid("");
        if(saRolemenuappPojo.getRownum()==null) saRolemenuappPojo.setRownum(0);
        if(saRolemenuappPojo.getCreateby()==null) saRolemenuappPojo.setCreateby("");
        if(saRolemenuappPojo.getCreatebyid()==null) saRolemenuappPojo.setCreatebyid("");
        if(saRolemenuappPojo.getCreatedate()==null) saRolemenuappPojo.setCreatedate(new Date());
        if(saRolemenuappPojo.getLister()==null) saRolemenuappPojo.setLister("");
        if(saRolemenuappPojo.getListerid()==null) saRolemenuappPojo.setListerid("");
        if(saRolemenuappPojo.getModifydate()==null) saRolemenuappPojo.setModifydate(new Date());
        if(saRolemenuappPojo.getTenantid()==null) saRolemenuappPojo.setTenantid("");
        if(saRolemenuappPojo.getTenantname()==null) saRolemenuappPojo.setTenantname("");
        if(saRolemenuappPojo.getRevision()==null) saRolemenuappPojo.setRevision(0);
   }

}
