package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaBillgroupPojo;

import java.util.List;

/**
 * 通用分组(SaBillgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-02 14:47:57
 */
public interface SaBillgroupService {


    SaBillgroupPojo getEntity(String key);

    PageInfo<SaBillgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saBillgroupPojo 实例对象
     * @return 实例对象
     */
    SaBillgroupPojo insert(SaBillgroupPojo saBillgroupPojo);

    /**
     * 修改数据
     *
     * @param saBillgrouppojo 实例对象
     * @return 实例对象
     */
    SaBillgroupPojo update(SaBillgroupPojo saBillgrouppojo);

    int delete(String key);

    /**
     * 通过功能号获得分组
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<SaBillgroupPojo> getListByModuleCode(String moduleCode);
}
