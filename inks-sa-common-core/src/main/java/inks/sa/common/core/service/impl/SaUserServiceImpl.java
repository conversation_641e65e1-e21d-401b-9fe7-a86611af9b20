package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import eu.bitwalker.useragentutils.UserAgent;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.controller.A_SaUserController;
import inks.sa.common.core.domain.SaLoginlogEntity;
import inks.sa.common.core.domain.SaUserEntity;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.mapper.SaLoginlogMapper;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.service.*;
import inks.sa.common.core.utils.LicenseUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Base64;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 用户(SaUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
@Service("saUserService")
public class SaUserServiceImpl implements SaUserService {
    @Resource
    private SaJustauthService saJustauthService;
    @Resource
    private SaUserMapper saUserMapper;
    @Resource
    private SaDeptService saDeptService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private A_SaUserController a_saUserController;
    @Resource
    private SaCompanyService saCompanyService;
    @Value("${inks.tid}")
    private String yml_tid;
    @Value("${inks.user.wxscan-create:false}")
    private boolean wxScanCreate;
    @Resource
    private SaUserroleService saUserroleService;
    @Resource
    private SaLoginlogMapper saLoginlogMapper;

    //    在JDK 8中使用的sun.misc.BASE64Encoder已经被标记为内部API，并且在JDK 9及更高版本中已删除。
//    相反，你应该使用java.util.Base64类来执行Base64编码。
//    以下是将return (new BASE64Encoder()).encode(encrypted);修改为java.util.Base64的示例：
    public static String Encrypt(String sSrc) throws Exception {
        byte[] raw = "12345678901234567890123456789012".getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(1, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public static String Decrypt(String sSrc) throws Exception {
        if (sSrc == null) {
            System.out.print("Key为空null");
            return null;
        }
        byte[] raw = "12345678901234567890123456789012".getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(2, skeySpec);
        byte[] encrypted1 = Base64.getDecoder().decode(sSrc);
        byte[] original = cipher.doFinal(encrypted1);
        return new String(original, StandardCharsets.UTF_8);
    }

    @Override
    public SaUserPojo getEntity(String key) {
        return this.saUserMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaUserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUserPojo> lst = saUserMapper.getPageList(queryParam);
            PageInfo<SaUserPojo> pageInfo = new PageInfo<SaUserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserPojo insert(SaUserPojo saUserPojo) throws Exception {
        //初始化NULL字段
        if (saUserPojo.getUsername() == null) saUserPojo.setUsername("");
        if (saUserPojo.getRealname() == null) saUserPojo.setRealname("");
        if (saUserPojo.getPassword() == null) saUserPojo.setPassword("");
        if (saUserPojo.getPhone() == null) saUserPojo.setPhone("");
        if (saUserPojo.getEmail() == null) saUserPojo.setEmail("");
        if (saUserPojo.getEmailauthcode() == null) saUserPojo.setEmailauthcode("");
        if (saUserPojo.getSex() == null) saUserPojo.setSex(0);
        if (saUserPojo.getAvatar() == null) saUserPojo.setAvatar("");
        if (saUserPojo.getUserstate() == null) saUserPojo.setUserstate(0);
        if (saUserPojo.getRemark() == null) saUserPojo.setRemark("");
        if (saUserPojo.getCreateby() == null) saUserPojo.setCreateby("");
        if (saUserPojo.getCreatedate() == null) saUserPojo.setCreatedate(new Date());
        if (saUserPojo.getLister() == null) saUserPojo.setLister("");
        if (saUserPojo.getModifydate() == null) saUserPojo.setModifydate(new Date());
        if (saUserPojo.getRevision() == null) saUserPojo.setRevision(0);
        if (saUserPojo.getDirname() == null) saUserPojo.setDirname("");
        if (saUserPojo.getFilename() == null) saUserPojo.setFilename("");
        if (saUserPojo.getAdminmark() == null) saUserPojo.setAdminmark(0);
        if (saUserPojo.getRoletype() == null) saUserPojo.setRoletype(0);
        //// 检查openid是否已存在
        //if (isNotBlank(saUserPojo.getWxopenid())) {
        //    SaUserPojo saUser = saUserMapper.getEntityByOpenid(saUserPojo.getWxopenid());
        //    if (saUser != null) {
        //        throw new BaseBusinessException("该微信openid已绑定账号:" + saUser.getUsername());
        //    }
        //}
        // 加密密码存入数据库Sa_User
        saUserPojo.setPassword(AESUtil.Encrypt(saUserPojo.getPassword()));
        SaUserEntity saUserEntity = new SaUserEntity();
        org.springframework.beans.BeanUtils.copyProperties(saUserPojo, saUserEntity);

        saUserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saUserEntity.setRevision(1);  //乐观锁
        this.saUserMapper.insert(saUserEntity);
        return this.getEntity(saUserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserPojo update(SaUserPojo saUserPojo) throws Exception {
        String password = saUserPojo.getPassword();
        if (isNotBlank(password)) {
            saUserPojo.setPassword(AESUtil.Encrypt(password));
        }
        SaUserEntity saUserEntity = new SaUserEntity();
        BeanUtils.copyProperties(saUserPojo, saUserEntity);
        this.saUserMapper.update(saUserEntity);
        return this.getEntity(saUserEntity.getId());
    }

    @Override
    public int delete(String key, String tid) {
        return this.saUserMapper.delete(key, tid);
    }

    @Override
    public Map<String, Object> token(LoginUser loginUser) {
////        SaTenantPojo saTenantPojo = saTenantMapper.getEntityUseridTid(loginUser.getUserid(), loginUser.getTenantid());
//        loginUser.setIsadmin(saTenantPojo.getIsadmin());
//        TenantInfo tenantInfo = new TenantInfo();
//        tenantInfo.setTenantid(saTenantPojo.getTenantid());
//        tenantInfo.setTenantcode(saTenantPojo.getTenantcode());
//        tenantInfo.setTenantname(saTenantPojo.getTenantname());
//        tenantInfo.setCompany(saTenantPojo.getCompany());
//        tenantInfo.setCompanytel(saTenantPojo.getCompanytel());
//        tenantInfo.setCompanyadd(saTenantPojo.getCompanyaddr());
//        loginUser.setTenantinfo(tenantInfo);
//        Map<String, Object> token = saRedisService.createToken(loginUser);
        return null;
    }

    @Override
    public SaUserPojo getUserInfo(LoginUser loginUser) {
        return this.saUserMapper.getUserInfo(loginUser.getUserid());
    }

    @Override
    public Map<String, Object> loginCheck(String username, String password, HttpServletRequest request, String type) {
        try {
            //构建登陆日志对象
            SaLoginlogEntity loginLogEntity = new SaLoginlogEntity();
            loginLogEntity.setLogintime(BillCodeUtil.newDate());
            loginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            //获取请求浏览器操作系统
            UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
            String os = userAgent.getOperatingSystem().getName();
            String browser = userAgent.getBrowser().getName();
            String ip = UserAgentUtil.getIpAddr(request);
            String address = UserAgentUtil.getRealAddressByIP(ip);
            loginLogEntity.setBrowsername(browser);
            loginLogEntity.setHostsystem(os);
            loginLogEntity.setIpaddr(ip);
            loginLogEntity.setLoginlocation(address);

            // 改为支持JDK17
            String encryptPass = Encrypt(password);
            SaUserPojo saUserPojo = this.saUserMapper.getEntityByUNameAndPass(username, encryptPass);
            // 构建登录日志
            if (saUserPojo != null) {
                // 登录成功日志构建:
                loginLogEntity.setUserid(saUserPojo.getId());
                loginLogEntity.setUsername(username);
                loginLogEntity.setRealname(saUserPojo.getRealname());
                loginLogEntity.setDirection("登录");
                loginLogEntity.setLoginstatus(0);
                // 登录成功日志:
                loginLogEntity.setLoginmsg("用户：" + username + "登录成功,登录时间："
                        + BillCodeUtil.forMatDate(loginLogEntity.getLogintime()) + ",登录系统："
                        + loginLogEntity.getHostsystem() + ",操作浏览器："
                        + loginLogEntity.getBrowsername() + ",登录地址："
                        + loginLogEntity.getLoginlocation());
                saLoginlogMapper.insert(loginLogEntity);
            } else {
                loginLogEntity.setUserid("");
                loginLogEntity.setUsername(username);
                loginLogEntity.setRealname("");
                loginLogEntity.setDirection("登录");
                loginLogEntity.setLoginstatus(1);
                loginLogEntity.setLoginmsg("用户：" + username + "登录失败,登录时间："
                        + BillCodeUtil.forMatDate(loginLogEntity.getLogintime()) + ",原因：用户名或密码错误" + ",登录系统："
                        + loginLogEntity.getHostsystem() + ",操作浏览器："
                        + loginLogEntity.getBrowsername() + ",登录地址："
                        + loginLogEntity.getLoginlocation());
                saLoginlogMapper.insert(loginLogEntity);
                throw new BaseBusinessException("用户名或密码错误");
            }

            // 创建登录loginuser并存入Redis/DB
            return myCreateToken(saUserPojo, request);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    public Map<String, Object> myCreateToken(SaUserPojo saUserPojo, HttpServletRequest request) throws Exception {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserid(saUserPojo.getId());
        loginUser.setUsername(saUserPojo.getUsername());
        loginUser.setAvatar(saUserPojo.getAvatar());
        loginUser.setIpaddr(IpUtils.getIpAddr(request));
        loginUser.setRealname(saUserPojo.getRealname());
        //查DB 当前用户的AdminMark
        loginUser.setIsadmin(saUserPojo.getAdminmark());
        loginUser.setTenantid(yml_tid);

        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(loginUser.getUserid());
        loginUser.setUsername(loginUser.getUsername());
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
//        this.refreshToken(loginUser);
        loginUser.setLogintime(System.currentTimeMillis());
        loginUser.setExpiretime(loginUser.getLogintime() + 43200000L);//12小时 毫秒
        loginUser.setIsregister(0);
        Map<String, Object> map = new HashMap();
        map.put("access_token", token);
        map.put("expires_in", 43200L);
        map.put("sn", LicenseUtils.getYmlSN());

        // 检查SN成功才返回解密后的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":**********},存储在Sa_Config中CfgKey=system.registrkey
        String decrypt = "";
        R<String> stringR = a_saUserController.checkHard(); //检查SN成功才返回解密后的密文
        if (stringR.getCode() == 200) {
            decrypt = stringR.getData();
            loginUser.setIsregister(1);//已注册且硬件匹配
        } else if (stringR.getCode() == 402) {
            loginUser.setIsregister(2);//已注册但硬件不匹配
        }
        //20240524 loginUser中加入权限编码
        saUserroleService.updateTokenPerm(loginUser);

        //公司信息
        SaCompanyPojo companyInfo = saCompanyService.getCompanyInfo();
        if (companyInfo == null) {
            throw new BaseBusinessException("公司信息不存在");
        }
        TenantInfo tenantInfo = new TenantInfo(yml_tid, "tid-code", "tid-name",
                companyInfo.getName(), companyInfo.getAddress(), companyInfo.getTel(), companyInfo.getContactperson(), new Date());
        //20250505 加入部门信息（组织框架）
        List<DeptinfoPojo> lstdept = saDeptService.getDeptinfoList(loginUser.getUserid());
        // 系统管理员直接标记为部门管理员
        boolean isAdmin = loginUser.getIsadmin() == 1 || loginUser.getIsadmin() == 2;
        tenantInfo.setIsdeptadmin(isAdmin ? 1 : 0);
        if (CollectionUtils.isNotEmpty(lstdept)) {
            tenantInfo.setLstdept(lstdept);
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);
        map.put("registrkey", decrypt);
        map.put("loginuser", loginUser);
        map.put("openid", saUserPojo.getWxopenid());

        this.saRedisService.setKeyValue(MyConstant.LOGIN_TOKEN_KEY + token, loginUser, 43200L);
        return map;
    }

    @Override
    public Map<String, Object> loginCheckByOpenid(String openid, HttpServletRequest request) {
        try {
            // 通过openid查询用户信息
            SaUserPojo saUserPojoDB = saUserMapper.getEntityByOpenid(openid);

            // 如果用户不存在并且yml未开启自动创建用户，则抛出异常
            if (saUserPojoDB == null) {
                if (!wxScanCreate) {
                    throw new BaseBusinessException("微信未绑定账号");
                }
                // 自动创建用户
                SaUserPojo saUserPojo = new SaUserPojo();
                // openid最后6位作为用户名
                String openidLast6 = openid.substring(openid.length() - 6);
                saUserPojo.setUsername("wx-" + openidLast6);
                saUserPojo.setRealname("wx-" + openidLast6);
                saUserPojo.setPassword("123456");
                saUserPojo.setAdminmark(1);
                saUserPojo.setRemark("微信扫码自动注册");
                // 保存新用户
                SaUserPojo insertUser = insert(saUserPojo);
                // 更新查询结果
                saUserPojoDB = saUserPojo;
                // 创建openid绑定关系到Sa_JustAuth表
                SaJustauthPojo saJustauthPojo = new SaJustauthPojo();
                saJustauthPojo.setUserid(insertUser.getId());
                saJustauthPojo.setUsername(saUserPojo.getUsername());
                saJustauthPojo.setRealname(saUserPojo.getRealname());
                saJustauthPojo.setAuthtype("openid");
                saJustauthPojo.setAuthuuid(openid);
                saJustauthService.insert(saJustauthPojo);
            }

            // 返回登录用户额外信息
            return myCreateToken(saUserPojoDB, request);
        } catch (Exception e) {
            throw new BaseBusinessException("登录检查失败: " + e.getMessage());
        }
    }


//    public void refreshToken(LoginUser loginUser) {
//        loginUser.setLogintime(System.currentTimeMillis());
//        loginUser.setExpiretime(loginUser.getLogintime() + 43200000L);
//        String userKey = this.getTokenKey(loginUser.getToken());
//        this.saRedisService.setKeyValue(userKey, loginUser, 43200L, TimeUnit.SECONDS);
//    }
//
//    private String getTokenKey(String token) {
//        return MyConstant.LOGIN_TOKEN_KEY + token;
//    }


    @Override
    public boolean checkUsername(String username, String id) {
        int i = this.saUserMapper.checkUsername(username, id);
        return i > 0;
    }

    @Override
    public SaUserPojo getEntityByDingUserid(String dingUserid) {
        return this.saUserMapper.getEntityByDingUserid(dingUserid);
    }

    @Override
    public SaUserPojo getEntityByJustAuth(String authtype, String authuuid) {
        return this.saUserMapper.getEntityByJustAuth(authtype, authuuid);
    }

    @Override
    public LoginUser getLoginUserByJust(String authtype, String authuuid) {
        LoginUser loginUserByJustAuth = this.saUserMapper.getLoginUserByJustAuth(authtype, authuuid);
        SaCompanyPojo companyInfo = saCompanyService.getCompanyInfo();
        if (companyInfo == null) {
            throw new BaseBusinessException("公司信息不存在");
        }
        loginUserByJustAuth.setTenantinfo(new TenantInfo(yml_tid, "tid-code", "tid-name",
                companyInfo.getName(), companyInfo.getAddress(), companyInfo.getTel(), companyInfo.getContactperson(), new Date()));
        return loginUserByJustAuth;
    }
}
