package inks.sa.common.core.config.oa.aes; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param
 */

public class DetailsPojo {
    private Integer spStatus;
    private String speech;
    private ResultUserPojo approver;
    private String spTime;


    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }

    public String getSpeech() {
        return speech;
    }

    public void setSpeech(String speech) {
        this.speech = speech;
    }

    public ResultUserPojo getApprover() {
        return approver;
    }

    public void setApprover(ResultUserPojo approver) {
        this.approver = approver;
    }

    public String getSpTime() {
        return spTime;
    }

    public void setSpTime(String spTime) {
        this.spTime = spTime;
    }

    @Override
    public String toString() {
        String sb = "{" + "\"spStatus\":" +
                spStatus +
                ",\"speech\":\"" +
                speech + '\"' +
                ",\"approver\":" +
                approver +
                ",\"spTime\":\"" +
                spTime + '\"' +
                '}';
        return sb;
    }
}
