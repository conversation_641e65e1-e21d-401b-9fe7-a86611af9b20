package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaRolemenuappPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaRolemenuappService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 角色菜单App(Sa_RoleMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */

@RestController
@RequestMapping("SaRoleMenuApp")
@Api(tags = "通用:角色菜单App")
public class A_SaRolemenuappController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaRolemenuappController.class);
    @Resource
    private SaRolemenuappService saRolemenuappService;
    @Resource
    private SaRedisService saRedisService;

    // json示例 "    {\" +
    //                "    \"roleid\": \"12345\",\n" +
    //                "    \"delete\": [\"navid1\", \"navid2\", \"navid3\"],\n" +
    //                "    \"create\": [\"navi4\", \"navi5\"]\n" +
    //                "}"
    @ApiOperation(value = "批量删除角色菜单Web  通过一个Roleid和多个Navids", notes = "", produces = "application/json")
    @RequestMapping(value = "/batchCreateDelete", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.Delete")
    public R<Integer> batchCreateDelete(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 解析JSON字符串
            JSONObject jsonObject = JSON.parseObject(json);
            String roleid = jsonObject.getString("roleid");
            JSONArray deleteArray = jsonObject.getJSONArray("delete");
            List<String> deleteNavids = deleteArray != null ? deleteArray.toJavaList(String.class) : null;
            JSONArray createArray = jsonObject.getJSONArray("create");
            List<String> createNavids = createArray != null ? createArray.toJavaList(String.class) : null;
            return R.ok(this.saRolemenuappService.batchCreateDelete(roleid, deleteNavids, createNavids, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取角色菜单App详细信息", notes = "获取角色菜单App详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.List")
    public R<SaRolemenuappPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRolemenuappService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.List")
    public R<PageInfo<SaRolemenuappPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_RoleMenuApp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saRolemenuappService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增角色菜单App", notes = "新增角色菜单App", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.Add")
    public R<SaRolemenuappPojo> create(@RequestBody String json) {
        try {
            SaRolemenuappPojo saRolemenuappPojo = JSONArray.parseObject(json, SaRolemenuappPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saRolemenuappPojo.setCreateby(loginUser.getRealName());   // 创建者
            saRolemenuappPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saRolemenuappPojo.setCreatedate(new Date());   // 创建时间
            saRolemenuappPojo.setLister(loginUser.getRealname());   // 制表
            saRolemenuappPojo.setListerid(loginUser.getUserid());    // 制表id
            saRolemenuappPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saRolemenuappService.insert(saRolemenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改角色菜单App", notes = "修改角色菜单App", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.Edit")
    public R<SaRolemenuappPojo> update(@RequestBody String json) {
        try {
            SaRolemenuappPojo saRolemenuappPojo = JSONArray.parseObject(json, SaRolemenuappPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saRolemenuappPojo.setLister(loginUser.getRealname());   // 制表
            saRolemenuappPojo.setListerid(loginUser.getUserid());    // 制表id
            saRolemenuappPojo.setModifydate(new Date());   //修改时间
            //            saRolemenuappPojo.setAssessor(""); // 审核员
            //            saRolemenuappPojo.setAssessorid(""); // 审核员id
            //            saRolemenuappPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saRolemenuappService.update(saRolemenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除角色菜单App", notes = "删除角色菜单App", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRolemenuappService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_RoleMenuApp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaRolemenuappPojo saRolemenuappPojo = this.saRolemenuappService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saRolemenuappPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

