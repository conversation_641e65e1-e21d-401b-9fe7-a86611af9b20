package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaOperlogEntity;
import inks.sa.common.core.domain.pojo.SaOperlogPojo;
import inks.sa.common.core.mapper.SaOperlogMapper;
import inks.sa.common.core.service.SaOperlogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 操作日志(SaOperlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19 14:41:51
 */
@Service("saOperlogService")
public class SaOperlogServiceImpl implements SaOperlogService {
    @Resource
    private SaOperlogMapper saOperlogMapper;

    private static void cleanNull(SaOperlogPojo saOperlogPojo) {
        if (saOperlogPojo.getOpertitle() == null) saOperlogPojo.setOpertitle("");
        if (saOperlogPojo.getBusinesstype() == null) saOperlogPojo.setBusinesstype(0);
        if (saOperlogPojo.getMethod() == null) saOperlogPojo.setMethod("");
        if (saOperlogPojo.getRequestmethod() == null) saOperlogPojo.setRequestmethod("");
        if (saOperlogPojo.getOperatortype() == null) saOperlogPojo.setOperatortype(0);
        if (saOperlogPojo.getOperuserid() == null) saOperlogPojo.setOperuserid("");
        if (saOperlogPojo.getOpername() == null) saOperlogPojo.setOpername("");
        if (saOperlogPojo.getDeptname() == null) saOperlogPojo.setDeptname("");
        if (saOperlogPojo.getOperurl() == null) saOperlogPojo.setOperurl("");
        if (saOperlogPojo.getOperip() == null) saOperlogPojo.setOperip("");
        if (saOperlogPojo.getOperlocation() == null) saOperlogPojo.setOperlocation("");
        if (saOperlogPojo.getOperparam() == null) saOperlogPojo.setOperparam("");
        if (saOperlogPojo.getJsonresult() == null) saOperlogPojo.setJsonresult("");
        if (saOperlogPojo.getStatus() == null) saOperlogPojo.setStatus(0);
        if (saOperlogPojo.getErrormsg() == null) saOperlogPojo.setErrormsg("");
        if (saOperlogPojo.getOpertime() == null) saOperlogPojo.setOpertime(new Date());
        if (saOperlogPojo.getTenantid() == null) saOperlogPojo.setTenantid("");
    }

    @Override
    public SaOperlogPojo getEntity(String key) {
        return this.saOperlogMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaOperlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaOperlogPojo> lst = saOperlogMapper.getPageList(queryParam);
            PageInfo<SaOperlogPojo> pageInfo = new PageInfo<SaOperlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaOperlogPojo insert(SaOperlogPojo saOperlogPojo) {
        //初始化NULL字段
        cleanNull(saOperlogPojo);
        SaOperlogEntity saOperlogEntity = new SaOperlogEntity();
        BeanUtils.copyProperties(saOperlogPojo, saOperlogEntity);
        //生成雪花id
        saOperlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saOperlogMapper.insert(saOperlogEntity);
        return this.getEntity(saOperlogEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saOperlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaOperlogPojo update(SaOperlogPojo saOperlogPojo) {
        SaOperlogEntity saOperlogEntity = new SaOperlogEntity();
        BeanUtils.copyProperties(saOperlogPojo, saOperlogEntity);
        this.saOperlogMapper.update(saOperlogEntity);
        return this.getEntity(saOperlogEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saOperlogMapper.delete(key);
    }

    @Override
    public int deleteByTime(QueryParam queryParam) {
        return this.saOperlogMapper.deleteByTime(queryParam);
    }
}
