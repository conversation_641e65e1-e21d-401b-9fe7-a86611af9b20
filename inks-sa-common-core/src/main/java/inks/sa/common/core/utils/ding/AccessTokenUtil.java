package inks.sa.common.core.utils.ding;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.taobao.api.ApiException;

/**
 * 官方文档：https://open.dingtalk.com/document/orgapp/obtain-orgapp-token
 * 在使用access_token时，请注意：
 * access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
 * 开发者需要缓存access_token，用于后续接口的调用。因为每个应用的access_token是彼此独立的，所以进行缓存时需要区分应用来进行存储。
 * 不能频繁调用gettoken接口，否则会受到频率拦截。
 */
public class AccessTokenUtil {
    public static String AppKey = "替换为你应用的AppKey";
    public static String AppSecret = "替换为你应用的AppSecret";

    public static String getToken() throws RuntimeException {
        try {
            DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest request = new OapiGettokenRequest();

            request.setAppkey(AppKey);
            request.setAppsecret(AppSecret);
            request.setHttpMethod("GET");
            OapiGettokenResponse response = client.execute(request);
            String accessToken = response.getAccessToken();
            return accessToken;
        } catch (ApiException e) {
            throw new RuntimeException();
        }

    }

    public static void main(String[] args) throws ApiException {
        String accessToken = AccessTokenUtil.getToken();
        System.out.println(accessToken);
    }
}
