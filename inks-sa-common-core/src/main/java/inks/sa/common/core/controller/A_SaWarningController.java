package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaWarningPojo;
import inks.sa.common.core.domain.pojo.SaWarninguserPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaWarningService;
import inks.sa.common.core.service.SaWarninguserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预警(Sa_Warning)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@RestController
@RequestMapping("SaWarning")
@Api(tags = "通用:预警")
public class A_SaWarningController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaWarningController.class);
    @Resource
    private SaWarningService saWarningService;
    @Resource
    private SaWarninguserService saWarninguserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "读取当前用户预警列表", notes = "读取当前用户预警列表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Warning.List")
    public R<List<SaWarninguserPojo>> getListByUser() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarninguserService.getListByUser(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "读取当前用户预警数据", notes = "读取当前用户预警数据", produces = "application/json")
    @RequestMapping(value = "/getWarnListByUser", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Warning.List")
    public R<List<SaWarninguserPojo>> getWarnListByUser() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarninguserService.getWarnListByUser(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取预警详细信息", notes = "获取预警详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Warning.List")
    public R<SaWarningPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarningService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Warning.List")
    public R<PageInfo<SaWarningPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Warning.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saWarningService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增预警", notes = "新增预警", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Warning.Add")
    public R<SaWarningPojo> create(@RequestBody String json) {
        try {
            SaWarningPojo saWarningPojo = JSONArray.parseObject(json, SaWarningPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWarningPojo.setCreateby(loginUser.getRealName());   // 创建者
            saWarningPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saWarningPojo.setCreatedate(new Date());   // 创建时间
            saWarningPojo.setLister(loginUser.getRealname());   // 制表
            saWarningPojo.setListerid(loginUser.getUserid());    // 制表id
            saWarningPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saWarningService.insert(saWarningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改预警", notes = "修改预警", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Warning.Edit")
    public R<SaWarningPojo> update(@RequestBody String json) {
        try {
            SaWarningPojo saWarningPojo = JSONArray.parseObject(json, SaWarningPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWarningPojo.setLister(loginUser.getRealname());   // 制表
            saWarningPojo.setListerid(loginUser.getUserid());    // 制表id
            saWarningPojo.setModifydate(new Date());   //修改时间
            //            saWarningPojo.setAssessor(""); // 审核员
            //            saWarningPojo.setAssessorid(""); // 审核员id
            //            saWarningPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saWarningService.update(saWarningPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除预警", notes = "删除预警", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Warning.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWarningService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Warning.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaWarningPojo saWarningPojo = this.saWarningService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saWarningPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

