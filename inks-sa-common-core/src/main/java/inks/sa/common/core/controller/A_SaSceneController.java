package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.sa.common.core.domain.pojo.SaScenePojo;
import inks.sa.common.core.domain.pojo.SaScenefieldPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaSceneService;
import inks.sa.common.core.service.SaScenefieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 场景管理(Sa_Scene)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:51
 */

@RestController
@RequestMapping("SaScene")
@Api(tags = "通用:场景管理")
public class A_SaSceneController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaSceneController.class);

    @Resource
    private SaSceneService sasceneService;

    @Resource
    private SaScenefieldService sascenefieldService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取场景字段详细信息", notes = "获取场景字段详细信息", produces = "application/json")
    @RequestMapping(value = "/getFieldEntity", method = RequestMethod.GET)
    public R<SaScenefieldPojo> getFieldEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sascenefieldService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getFieldPageList", method = RequestMethod.POST)
    public R<PageInfo<SaScenefieldPojo>> getFieldPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SceneField.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.sascenefieldService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增场景字段", notes = "新增场景字段", produces = "application/json")
    @RequestMapping(value = "/createField", method = RequestMethod.POST)
    public R<SaScenefieldPojo> createField(@RequestBody String json) {
        try {
            SaScenefieldPojo sascenefieldPojo = JSONArray.parseObject(json, SaScenefieldPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sascenefieldPojo.setCreateby(loginUser.getRealName());   // 创建者
            sascenefieldPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            sascenefieldPojo.setCreatedate(new Date());   // 创建时间
            sascenefieldPojo.setLister(loginUser.getRealname());   // 制表
            sascenefieldPojo.setListerid(loginUser.getUserid());    // 制表id
            sascenefieldPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.sascenefieldService.insert(sascenefieldPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改场景字段", notes = "修改场景字段", produces = "application/json")
    @RequestMapping(value = "/updateField", method = RequestMethod.POST)
    public R<SaScenefieldPojo> updateField(@RequestBody String json) {
        try {
            SaScenefieldPojo sascenefieldPojo = JSONArray.parseObject(json, SaScenefieldPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sascenefieldPojo.setLister(loginUser.getRealname());   // 制表
            sascenefieldPojo.setListerid(loginUser.getUserid());    // 制表id
            sascenefieldPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.sascenefieldService.update(sascenefieldPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除场景字段", notes = "删除场景字段", produces = "application/json")
    @RequestMapping(value = "/deleteField", method = RequestMethod.GET)
    public R<Integer> deleteField(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sascenefieldService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按code查询场景字段", notes = "按code查询场景字段", produces = "application/json")
    @RequestMapping(value = "/getFieldListByCode", method = RequestMethod.GET)
    public R<List<SaScenefieldPojo>> getFieldListByCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sascenefieldService.getListByCode(code));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按code查询用户场景列表", notes = "按code查询用户场景列表", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    public R<List<SaScenePojo>> getListByCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sasceneService.getListByCode(code, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取场景管理详细信息", notes = "获取场景管理详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaScenePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sasceneService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaScenePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("SaScene.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.sasceneService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增场景管理", notes = "新增场景管理", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaScenePojo> create(@RequestBody String json) {
        try {
            SaScenePojo sascenePojo = JSONArray.parseObject(json, SaScenePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sascenePojo.setCreateby(loginUser.getRealName());   // 创建者
            sascenePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            sascenePojo.setCreatedate(new Date());   // 创建时间
            sascenePojo.setLister(loginUser.getRealname());   // 制表
            sascenePojo.setListerid(loginUser.getUserid());    // 制表id  
            sascenePojo.setModifydate(new Date());   //修改时间
            sascenePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.sasceneService.insert(sascenePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改场景管理", notes = "修改场景管理", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaScenePojo> update(@RequestBody String json) {
        try {
            SaScenePojo sascenePojo = JSONArray.parseObject(json, SaScenePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sascenePojo.setLister(loginUser.getRealname());   // 制表
            sascenePojo.setListerid(loginUser.getUserid());    // 制表id  
            sascenePojo.setTenantid(loginUser.getTenantid());   //租户id
            sascenePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.sasceneService.update(sascenePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除场景管理", notes = "删除场景管理", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sasceneService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

