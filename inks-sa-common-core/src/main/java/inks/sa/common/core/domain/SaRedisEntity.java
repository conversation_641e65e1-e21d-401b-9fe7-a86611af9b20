package inks.sa.common.core.domain;

import java.io.Serializable;

/**
 * MySQL暂替Redis(SaRedis)实体类
 *
 * <AUTHOR>
 * @since 2023-11-27 15:26:35
 */
public class SaRedisEntity implements Serializable {
    private static final long serialVersionUID = 657083579858076984L;
    // key是MySQL关键字
    private String rediskey;
    // value
    private String redisvalue;
    // 过期时间戳(-1永不过期),每天23点执行SQL清除过期key:DELETE FROM Sa_Redis WHERE UNIX_TIMESTAMP(NOW()) * 1000 > expiretime AND expiretime != -1
    private Long expiretime;
    // 创建时间戳
    private Long createtime;
    // 哈希内部的键
    private String hkey;

// key是MySQL关键字

    public String getRediskey() {
        return rediskey;
    }

    public void setRediskey(String rediskey) {
        this.rediskey = rediskey;
    }
// value

    public String getRedisvalue() {
        return redisvalue;
    }

    public void setRedisvalue(String redisvalue) {
        this.redisvalue = redisvalue;
    }
// 过期时间戳(-1永不过期),每天23点执行SQL清除过期key:DELETE FROM Sa_Redis WHERE UNIX_TIMESTAMP(NOW()) * 1000 > expiretime AND expiretime != -1

    public Long getExpiretime() {
        return expiretime;
    }

    public void setExpiretime(Long expiretime) {
        this.expiretime = expiretime;
    }
// 创建时间戳

    public Long getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Long createtime) {
        this.createtime = createtime;
    }
// 哈希内部的键

    public String getHkey() {
        return hkey;
    }

    public void setHkey(String hkey) {
        this.hkey = hkey;
    }

}

