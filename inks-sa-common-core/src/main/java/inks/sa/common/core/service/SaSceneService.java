package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaScenePojo;

import java.util.List;

/**
 * 场景管理(Sascene)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
public interface SaSceneService {


    SaScenePojo getEntity(String key, String tid);

    PageInfo<SaScenePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param sascenePojo 实例对象
     * @return 实例对象
     */
    SaScenePojo insert(SaScenePojo sascenePojo);

    /**
     * 修改数据
     *
     * @param sascenepojo 实例对象
     * @return 实例对象
     */
    SaScenePojo update(SaScenePojo sascenepojo);

    int delete(String key, String tid);

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    List<SaScenePojo> getListByCode(String code, String userid, String tid);
}
