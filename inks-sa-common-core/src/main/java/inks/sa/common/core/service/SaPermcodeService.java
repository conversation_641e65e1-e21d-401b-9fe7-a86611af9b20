package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;

/**
 * 权限编码(SaPermcode)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:36
 */
public interface SaPermcodeService {


    SaPermcodePojo getEntity(String key);

    PageInfo<SaPermcodePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saPermcodePojo 实例对象
     * @return 实例对象
     */
    SaPermcodePojo insert(SaPermcodePojo saPermcodePojo);

    /**
     * 修改数据
     *
     * @param saPermcodepojo 实例对象
     * @return 实例对象
     */
    SaPermcodePojo update(SaPermcodePojo saPermcodepojo);

    int delete(String key);
}
