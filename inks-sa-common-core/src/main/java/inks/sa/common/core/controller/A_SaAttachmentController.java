package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaAttachmentPojo;
import inks.sa.common.core.service.SaAttachmentService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 附件中心(Sa_Attachment)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-29 13:26:54
 */
@RestController
@RequestMapping("SaAttachment")
@Api(tags = "通用:附件中心")
public class A_SaAttachmentController {
    @Resource
    private SaAttachmentService saAttachmentService;

    @Resource
    private SaRedisService saRedisService;



    @ApiOperation(value = " 获取附件中心详细信息", notes = "获取附件中心详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Attachment.List")
    public R<SaAttachmentPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAttachmentService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Attachment.List")
    public R<PageInfo<SaAttachmentPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Attachment.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saAttachmentService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增附件中心", notes = "新增附件中心", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Attachment.Add")
    public R<SaAttachmentPojo> create(@RequestBody String json) {
        try {
            SaAttachmentPojo saAttachmentPojo = JSONArray.parseObject(json, SaAttachmentPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saAttachmentPojo.setCreateby(loginUser.getRealName());   // 创建者
            saAttachmentPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saAttachmentPojo.setCreatedate(new Date());   // 创建时间
            saAttachmentPojo.setLister(loginUser.getRealname());   // 制表
            saAttachmentPojo.setListerid(loginUser.getUserid());    // 制表id
            saAttachmentPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saAttachmentService.insert(saAttachmentPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改附件中心", notes = "修改附件中心", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Attachment.Edit")
    public R<SaAttachmentPojo> update(@RequestBody String json) {
        try {
            SaAttachmentPojo saAttachmentPojo = JSONArray.parseObject(json, SaAttachmentPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saAttachmentPojo.setLister(loginUser.getRealname());   // 制表
            saAttachmentPojo.setListerid(loginUser.getUserid());    // 制表id
            saAttachmentPojo.setModifydate(new Date());   //修改时间
            //            saAttachmentPojo.setAssessor(""); // 审核员
            //            saAttachmentPojo.setAssessorid(""); // 审核员id
            //            saAttachmentPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saAttachmentService.update(saAttachmentPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除附件中心", notes = "删除附件中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Attachment.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAttachmentService.delete(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Attachment.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaAttachmentPojo saAttachmentPojo = this.saAttachmentService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saAttachmentPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

