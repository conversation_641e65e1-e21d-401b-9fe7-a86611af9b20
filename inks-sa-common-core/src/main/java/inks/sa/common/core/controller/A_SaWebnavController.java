package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.pojo.SaWebnavPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaWebnavService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Pc导航(Sa_WebNav)表控制层
 *
 * <AUTHOR>
 * @since 2024-03-02 13:42:49
 */

@RestController
@RequestMapping("SaWebNav")
@Api(tags = "通用:PC导航")
public class A_SaWebnavController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaWebnavController.class);

    @Resource
    private SaWebnavService saWebnavService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getAllList", method = RequestMethod.POST)
    public R<List<SaWebnavPojo>> getAllList() {
        try {
            QueryParam queryParam = new QueryParam();
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_WebNav.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setPageNum(1);
            queryParam.setPageSize(1000);
            return R.ok(this.saWebnavService.getPageList(queryParam).getList());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取Pc导航详细信息", notes = "获取Pc导航详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_WebNav.List")
    public R<SaWebnavPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWebnavService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WebNav.List")
    public R<PageInfo<SaWebnavPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_WebNav.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWebnavService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增Pc导航", notes = "新增Pc导航", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WebNav.Add")
    public R<SaWebnavPojo> create(@RequestBody String json) {
        try {
            SaWebnavPojo saWebnavPojo = JSONArray.parseObject(json, SaWebnavPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWebnavPojo.setCreateby(loginUser.getRealName());   // 创建者
            saWebnavPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saWebnavPojo.setCreatedate(new Date());   // 创建时间
            saWebnavPojo.setLister(loginUser.getRealname());   // 制表
            saWebnavPojo.setListerid(loginUser.getUserid());    // 制表id  
            saWebnavPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saWebnavService.insert(saWebnavPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改Pc导航", notes = "修改Pc导航", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_WebNav.Edit")
    public R<SaWebnavPojo> update(@RequestBody String json) {
        try {
            SaWebnavPojo saWebnavPojo = JSONArray.parseObject(json, SaWebnavPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saWebnavPojo.setLister(loginUser.getRealname());   // 制表
            saWebnavPojo.setListerid(loginUser.getUserid());    // 制表id  
            saWebnavPojo.setModifydate(new Date());   //修改时间
//            saWebnavPojo.setAssessor(""); // 审核员
//            saWebnavPojo.setAssessorid(""); // 审核员id
//            saWebnavPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saWebnavService.update(saWebnavPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除Pc导航", notes = "删除Pc导航", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_WebNav.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saWebnavService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

