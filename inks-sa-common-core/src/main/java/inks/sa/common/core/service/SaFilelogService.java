package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.domain.SaFilelogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 文件上传/下载日志表(SaFilelog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 14:22:57
 */
public interface SaFilelogService {


    SaFilelogPojo getEntity(String key);

    PageInfo<SaFilelogPojo> getPageList(QueryParam queryParam);

    SaFilelogPojo insert(SaFilelogPojo saFilelogPojo);

    SaFilelogPojo update(SaFilelogPojo saFilelogpojo);

    int delete(String key);
}
