package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 用户组织架构(SaDept)实体类
 *
 * <AUTHOR>
 * @since 2025-05-05 12:36:30
 */
@Data
public class SaDeptPojo implements Serializable {
    private static final long serialVersionUID = 455669162823704013L;
     // id
    @Excel(name = "id") 
    private String id;
     // Parentid
    @Excel(name = "Parentid") 
    private String parentid;
     // 祖组列表
    @Excel(name = "祖组列表") 
    private String ancestors;
     // 编码
    @Excel(name = "编码") 
    private String deptcode;
     // 名称
    @Excel(name = "名称") 
    private String deptname;
     // 有效性
    @Excel(name = "有效性") 
    private Integer enabledmark;
     // 负责人
    @Excel(name = "负责人") 
    private String leader;
     // 电话
    @Excel(name = "电话") 
    private String phone;
     // 邮箱
    @Excel(name = "邮箱") 
    private String email;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

