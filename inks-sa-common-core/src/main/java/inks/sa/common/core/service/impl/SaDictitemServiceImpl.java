package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaDictitemEntity;
import inks.sa.common.core.domain.pojo.SaDictitemPojo;
import inks.sa.common.core.mapper.SaDictitemMapper;
import inks.sa.common.core.service.SaDictitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 字典子表(SaDictitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:35
 */
@Service("saDictitemService")
public class SaDictitemServiceImpl implements SaDictitemService {
    @Resource
    private SaDictitemMapper saDictitemMapper;


    @Override
    public SaDictitemPojo getEntity(String key) {
        return this.saDictitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDictitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDictitemPojo> lst = saDictitemMapper.getPageList(queryParam);
            PageInfo<SaDictitemPojo> pageInfo = new PageInfo<SaDictitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public List<SaDictitemPojo> getList(String Pid) {
        try {
            List<SaDictitemPojo> lst = saDictitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saDictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDictitemPojo insert(SaDictitemPojo saDictitemPojo) {
        //初始化item的NULL
        SaDictitemPojo itempojo = this.clearNull(saDictitemPojo);
        SaDictitemEntity saDictitemEntity = new SaDictitemEntity();
        BeanUtils.copyProperties(itempojo, saDictitemEntity);

        saDictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDictitemEntity.setRevision(1);  //乐观锁
        this.saDictitemMapper.insert(saDictitemEntity);
        return this.getEntity(saDictitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDictitemPojo update(SaDictitemPojo saDictitemPojo) {
        SaDictitemEntity saDictitemEntity = new SaDictitemEntity();
        BeanUtils.copyProperties(saDictitemPojo, saDictitemEntity);
        this.saDictitemMapper.update(saDictitemEntity);
        return this.getEntity(saDictitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saDictitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saDictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDictitemPojo clearNull(SaDictitemPojo saDictitemPojo) {
        //初始化NULL字段
        if (saDictitemPojo.getPid() == null) saDictitemPojo.setPid("");
        if (saDictitemPojo.getDictcode() == null) saDictitemPojo.setDictcode("");
        if (saDictitemPojo.getDictvalue() == null) saDictitemPojo.setDictvalue("");
        if (saDictitemPojo.getEssential() == null) saDictitemPojo.setEssential(0);
        if (saDictitemPojo.getCssclass() == null) saDictitemPojo.setCssclass("");
        if (saDictitemPojo.getEnabledmark() == null) saDictitemPojo.setEnabledmark(0);
        if (saDictitemPojo.getRownum() == null) saDictitemPojo.setRownum(0);
        if (saDictitemPojo.getRemark() == null) saDictitemPojo.setRemark("");
        if (saDictitemPojo.getCreateby() == null) saDictitemPojo.setCreateby("");
        if (saDictitemPojo.getCreatebyid() == null) saDictitemPojo.setCreatebyid("");
        if (saDictitemPojo.getCreatedate() == null) saDictitemPojo.setCreatedate(new Date());
        if (saDictitemPojo.getLister() == null) saDictitemPojo.setLister("");
        if (saDictitemPojo.getListerid() == null) saDictitemPojo.setListerid("");
        if (saDictitemPojo.getModifydate() == null) saDictitemPojo.setModifydate(new Date());
        if (saDictitemPojo.getCustom1() == null) saDictitemPojo.setCustom1("");
        if (saDictitemPojo.getCustom2() == null) saDictitemPojo.setCustom2("");
        if (saDictitemPojo.getCustom3() == null) saDictitemPojo.setCustom3("");
        if (saDictitemPojo.getCustom4() == null) saDictitemPojo.setCustom4("");
        if (saDictitemPojo.getCustom5() == null) saDictitemPojo.setCustom5("");
        if (saDictitemPojo.getCustom6() == null) saDictitemPojo.setCustom6("");
        if (saDictitemPojo.getCustom7() == null) saDictitemPojo.setCustom7("");
        if (saDictitemPojo.getCustom8() == null) saDictitemPojo.setCustom8("");
        if (saDictitemPojo.getCustom9() == null) saDictitemPojo.setCustom9("");
        if (saDictitemPojo.getCustom10() == null) saDictitemPojo.setCustom10("");
        if (saDictitemPojo.getTenantid() == null) saDictitemPojo.setTenantid("");
        if (saDictitemPojo.getTenantname() == null) saDictitemPojo.setTenantname("");
        if (saDictitemPojo.getRevision() == null) saDictitemPojo.setRevision(0);
        return saDictitemPojo;
    }
}
