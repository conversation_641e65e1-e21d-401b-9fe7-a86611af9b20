package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaOperlogPojo;

/**
 * 操作日志(SaOperlog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-19 14:41:50
 */
public interface SaOperlogService {


    SaOperlogPojo getEntity(String key);

    PageInfo<SaOperlogPojo> getPageList(QueryParam queryParam);

    SaOperlogPojo insert(SaOperlogPojo saOperlogPojo);

    SaOperlogPojo update(SaOperlogPojo saOperlogpojo);

    int delete(String key);

    int deleteByTime(QueryParam queryParam);
}
