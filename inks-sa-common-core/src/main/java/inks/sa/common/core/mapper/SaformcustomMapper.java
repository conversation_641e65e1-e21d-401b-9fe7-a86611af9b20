package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaformcustomEntity;
import inks.sa.common.core.domain.pojo.SaformcustomPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自定义界面(Saformcustom)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-09 10:48:38
 */
@Mapper
public interface SaformcustomMapper {


    SaformcustomPojo getEntity(@Param("key") String key);


    List<SaformcustomPojo> getPageList(QueryParam queryParam);


    int insert(SaformcustomEntity saformcustomEntity);


    int update(SaformcustomEntity saformcustomEntity);


    int delete(@Param("key") String key);

    SaformcustomPojo getEntityByCode(String code);
}

