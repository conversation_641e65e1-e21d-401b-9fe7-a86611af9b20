package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 目录规则配置表(SaDirrule)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 14:21:06
 */
@Data
public class SaDirruleEntity implements Serializable {
    private static final long serialVersionUID = -96123955162585849L;
     // id
    private String id;
     // 目录名
    private String dirname;
     // 描述
    private String description;
     // 是否公共目录标识
    private Integer publicmark;
     // 黑名单用户ID
    private String blackuserids;
     // 黑名单用户名
    private String blackusernames;
     // 白名单用户ID
    private String whiteuserids;
     // 白名单用户名
    private String whiteusernames;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 功能编码
    private String modulecode;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门ID
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

