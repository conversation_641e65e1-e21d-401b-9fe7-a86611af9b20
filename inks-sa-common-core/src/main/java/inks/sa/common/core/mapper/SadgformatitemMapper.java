package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SadgformatitemEntity;
import inks.sa.common.core.domain.pojo.SadgformatitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 列表项目(Sadgformatitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:13
 */
@Mapper
public interface SadgformatitemMapper {


    SadgformatitemPojo getEntity(@Param("key") String key, @Param("tid") String tid);


    List<SadgformatitemPojo> getPageList(QueryParam queryParam);

    List<SadgformatitemPojo> getList(@Param("Pid") String Pid, @Param("tid") String tid);

    int insert(SadgformatitemEntity sadgformatitemEntity);

    int update(SadgformatitemEntity sadgformatitemEntity);


    int delete(@Param("key") String key, @Param("tid") String tid);

}

