package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaLogEntity;
import inks.sa.common.core.domain.pojo.SaLogPojo;
import inks.sa.common.core.mapper.SaLogMapper;
import inks.sa.common.core.service.SaLogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 通用日志(SaLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-17 16:22:15
 */
@Service("saLogService")
public class SaLogServiceImpl implements SaLogService {
    @Resource
    private SaLogMapper saLogMapper;

    @Override
    public SaLogPojo getEntity(String key) {
        return this.saLogMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaLogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLogPojo> lst = saLogMapper.getPageList(queryParam);
            PageInfo<SaLogPojo> pageInfo = new PageInfo<SaLogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaLogPojo insert(SaLogPojo saLogPojo) {
        //初始化NULL字段
        cleanNull(saLogPojo);
        SaLogEntity saLogEntity = new SaLogEntity(); 
        BeanUtils.copyProperties(saLogPojo,saLogEntity);
        //生成雪花id
          saLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saLogEntity.setRevision(1);  //乐观锁
          this.saLogMapper.insert(saLogEntity);
        return this.getEntity(saLogEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saLogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLogPojo update(SaLogPojo saLogPojo) {
        SaLogEntity saLogEntity = new SaLogEntity(); 
        BeanUtils.copyProperties(saLogPojo,saLogEntity);
        this.saLogMapper.update(saLogEntity);
        return this.getEntity(saLogEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saLogMapper.delete(key) ;
    }
    

    private static void cleanNull(SaLogPojo saLogPojo) {
        if(saLogPojo.getLogtype()==null) saLogPojo.setLogtype("");
        if(saLogPojo.getLoglevel()==null) saLogPojo.setLoglevel("");
        if(saLogPojo.getMessage()==null) saLogPojo.setMessage("");
        if(saLogPojo.getRequesturl()==null) saLogPojo.setRequesturl("");
        if(saLogPojo.getIpaddress()==null) saLogPojo.setIpaddress("");
        if(saLogPojo.getModule()==null) saLogPojo.setModule("");
        if(saLogPojo.getOperationtype()==null) saLogPojo.setOperationtype("");
        if(saLogPojo.getCreatedate()==null) saLogPojo.setCreatedate(new Date());
        if(saLogPojo.getUserid()==null) saLogPojo.setUserid("");
        if(saLogPojo.getRealname()==null) saLogPojo.setRealname("");
        if(saLogPojo.getUseragent()==null) saLogPojo.setUseragent("");
        if(saLogPojo.getHttpmethod()==null) saLogPojo.setHttpmethod("");
        if(saLogPojo.getResourcetype()==null) saLogPojo.setResourcetype("");
        if(saLogPojo.getResourceid()==null) saLogPojo.setResourceid("");
        if(saLogPojo.getResult()==null) saLogPojo.setResult("");
        if(saLogPojo.getErrormessage()==null) saLogPojo.setErrormessage("");
        if(saLogPojo.getCustomdata()==null) saLogPojo.setCustomdata("");
        if(saLogPojo.getTenantid()==null) saLogPojo.setTenantid("");
        if(saLogPojo.getRevision()==null) saLogPojo.setRevision(0);
   }

}
