package inks.sa.common.core.controller;

import inks.common.core.domain.FileInfo;
import inks.common.core.domain.R;
import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import inks.sa.common.core.config.oss.service.FileController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 文件中心（无表）
 */
@RestController
@RequestMapping("/File")
@Slf4j
@Api(tags = "通用:文件中心")
public class A_FileController extends FileController {
    @ApiOperation("通用上传文件")
    @GetMapping("aaaaa")
    public R<FileInfo> upload() {
        // 使用新的InksConfigThreadLocal，无需注解
        Map<String, String> config = InksConfigThreadLocal_Sa.getConfig();
        //打印config
        for (String key : config.keySet()) {
            System.out.println(key + ":" + config.get(key));
        }
        return R.ok();
    }

}