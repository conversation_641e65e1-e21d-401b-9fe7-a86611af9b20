package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.domain.pojo.SaReportsPojo;
import inks.sa.common.core.feign.GrfFeignClient;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaReportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 报表中心(Sa_Reports)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:48
 */
@RestController
@RequestMapping("SaReports")
@Api(tags = "通用:报表中心")
public class A_SaReportsController {

    @Resource
    private SaReportsService saReportsService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private GrfFeignClient grfFeignClient;

    //报表中心加入打印工具设计专用接口
    //类型：get
    //名称：getGrfDesign
    //参数:   key=报表id&redis=1
    //返回：ptid报表id, temp=grfdata/ report_codes:报表id, code = "design",token=token,msg=报表名称；
    @ApiOperation(value = "打印工具设计专用接口", notes = "获取报表设计数据", produces = "application/json")
    @GetMapping("/getGrfDesign")
    public R<Map<String, Object>> getGrfDesign(String key, Integer redis) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            // 从Redis中获取报表数据
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + key);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            // 组装返回数据
            Map<String, Object> mapPrint = new HashMap<>();
            mapPrint.put("ptid", key);
            mapPrint.put("code", "design");
            mapPrint.put("token", loginUser.getToken());
            if (Objects.equals(redis, 1)) {
                mapPrint.put("temp", "report_codes:" + key);   // Text模板
            } else {
                mapPrint.put("temp", reportsPojo.getGrfdata());
            }
            return R.ok(mapPrint);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Grf转发打印 通过Feign", notes = "", produces = "application/json")
    @PostMapping("/getGrfReport")  //temp和data可能传入的可能是JSON全数据；也可能是Redis键(需要转为JSON)
    public ResponseEntity<byte[]> getGrfReport(@RequestBody String json) {//{"msg":"送货单据BM-2024-02-937","temp":"report_codes:1759765386380742656","code":"print","data":"report_data:1765192700841201664","paperwidth":27.9,"sn":"local","paperlength":21.0,"token":"2d7d9ebd-8dcc-4c04-8a47-286040fea75e"}
        try {
            // 替换原始JSON字符串中的"data"和"temp"值为实际值:Sa_Redis中的值
            JSONObject jsonObject = JSONObject.parseObject(json);
            String data = jsonObject.getString("data");
            String temp = jsonObject.getString("temp");
            // 判断 "data" 和 "temp" 是否为 JSON 格式  不是则从 Sa_Redis 中获取全数据
            if (!isValidJson(data)) {
                data = saRedisService.getValue(data);
            }
            if (!isValidJson(temp)) {
                temp = saRedisService.getValue(temp);
            }
            jsonObject.put("data", data);
            jsonObject.put("temp", temp);
            String jsonUpdated = jsonObject.toJSONString();

            // 调用Feign客户端方法，获取PDF文件流
            ResponseEntity<byte[]> responseEntity = grfFeignClient.getGrfReport(jsonUpdated);
            // 获取Feign客户端的响应头 这里会得到一个Content-Type:application/octet-stream(因为远程的getGrfReport方法返回的就是application/octet-stream)
            // 二进制流的意思 以文本查看的话会得到和显式设置PDF一样的内容
            HttpHeaders headers = responseEntity.getHeaders();
            // 返回PDF文件流和响应头
            return new ResponseEntity<>(responseEntity.getBody(), headers, HttpStatus.OK);
        } catch (Exception e) {
            // 如果异常发生，将异常信息转换为字节数组并返回给前端
            String eMessage = e.getMessage();
            int startIndex = eMessage.indexOf("[{");
            // 添加对 startIndex 的检查，如果为负数，则设置为 0
            if (startIndex < 0) {
                startIndex = 0;
            }
            byte[] errorBytes = eMessage.substring(startIndex).getBytes(StandardCharsets.UTF_8);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            return new ResponseEntity<>(errorBytes, headers, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 判断字符串是否为有效的JSON格式
     */
    private boolean isValidJson(String str) {
        try {
            JSONObject.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @ApiOperation(value = " 获取报表中心详细信息", notes = "获取报表中心详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.List")
    public R<SaReportsPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saReportsService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.List")
    public R<PageInfo<SaReportsPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Reports.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saReportsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增报表中心", notes = "新增报表中心", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.Add")
    public R<SaReportsPojo> create(@RequestBody String json) {
        try {
            SaReportsPojo saReportsPojo = JSONArray.parseObject(json, SaReportsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saReportsPojo.setCreateby(loginUser.getRealName());   // 创建者
            saReportsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saReportsPojo.setCreatedate(new Date());   // 创建时间
            saReportsPojo.setLister(loginUser.getRealname());   // 制表
            saReportsPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportsPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saReportsService.insert(saReportsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改报表中心", notes = "修改报表中心", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.Edit")
    public R<SaReportsPojo> update(@RequestBody String json) {
        try {
            SaReportsPojo saReportsPojo = JSONArray.parseObject(json, SaReportsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saReportsPojo.setLister(loginUser.getRealname());   // 制表
            saReportsPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportsPojo.setModifydate(new Date());   //修改时间
//            saReportsPojo.setAssessor(""); // 审核员
//            saReportsPojo.setAssessorid(""); // 审核员id
//            saReportsPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saReportsService.update(saReportsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除报表中心", notes = "删除报表中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saReportsService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Reports.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaReportsPojo saReportsPojo = this.saReportsService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saReportsPojo);
        // 加入公司信息
        if (!Objects.isNull(loginUser.getTenantinfo())) {
            if (loginUser.getTenantinfo() != null)
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        }
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<SaReportsPojo>> getListByModuleCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            List<SaReportsPojo> list = this.saReportsService.getListByModuleCode(code);
            if (list != null) {
                for (int i = 0; i < list.size(); i++) {
                    String verifyKey = CacheConstants.REPORT_CODES_KEY + list.get(i).getId();
                    ReportsPojo reportsPojo = new ReportsPojo();
                    reportsPojo.setRptdata(list.get(i).getRptdata());
                    reportsPojo.setPagerow(list.get(i).getPagerow());
                    reportsPojo.setTempurl(list.get(i).getTempurl());
                    reportsPojo.setPrintersn(list.get(i).getPrintersn());
                    reportsPojo.setPaperlength(list.get(i).getPaperlength());
                    reportsPojo.setPaperwidth(list.get(i).getPaperwidth());
                    reportsPojo.setGrfdata(list.get(i).getGrfdata());
                    saRedisService.setKeyValue(verifyKey, reportsPojo, 60 * 12, TimeUnit.MINUTES);
                    if (list.get(i).getGrfdata() != null && !"".equals(list.get(i).getGrfdata())) {
                        list.get(i).setGrfdata("true");
                    } else {
                        list.get(i).setGrfdata(null);
                    }
                    if (list.get(i).getRptdata() != null && !"".equals(list.get(i).getRptdata())) {
                        list.get(i).setRptdata("true");
                    } else {
                        list.get(i).setRptdata(null);
                    }

                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "拉取默认报表,重复code、name跳过", notes = "拉取默认报表 code可选", produces = "application/json")
    @RequestMapping(value = "/pullDefault", method = RequestMethod.GET)
    public R<List<SaReportsPojo>> pullDefault(String code) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            List<SaReportsPojo> fmAccountPojos = this.saReportsService.pullDefault(code, loginUser);
            return R.ok(fmAccountPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListAll", method = RequestMethod.POST)
    public R<PageInfo<SaReportsPojo>> getPageListAll(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Reports.ModuleCode");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saReportsService.getPageListAll(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "传入一主表多子表Map,自定义云打印", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printFreeWebBill", method = RequestMethod.POST)
    public R<String> printFreeWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());


            //获取传入的自定义 一主表多子表Map
            Map<String, Object> billMap = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
            // 创建一个新的 Map 来存储除了 "item" 之外的所有键值对
            Map<String, Object> map = new HashMap<>(billMap);
            map.remove("item");
            // 创建一个新的 List<Map> 来只存储 "item" 键值对
            List<Map<String, Object>> lstitem = new ArrayList<>();
            if (billMap.get("item") instanceof List) {
                lstitem = (List<Map<String, Object>>) billMap.get("item");
            }

            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lstitem);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:主子表");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "一页两联打印:传入一主表多子表Map,自定义云打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printFreeWebBillMulti", method = RequestMethod.POST)
    public R<String> printFreeWebBillMulti(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //获取传入的自定义 一主表多子表Map
            Map<String, Object> billMap = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
            // 创建一个新的 Map 来存储除了 "item" 之外的所有键值对
            Map<String, Object> map = new HashMap<>(billMap);
            map.remove("item");
            // 加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            //=========获取单据Item信息========
            // 创建一个新的 List<Map> 来只存储 "item" 键值对
            List<Map<String, Object>> lstitem = new ArrayList<>();
            if (billMap.get("item") instanceof List) {
                lstitem = (List<Map<String, Object>>) billMap.get("item");
            }
            // 一式两份
            List<Map<String, Object>> lst = new ArrayList<>();
            List<Map<String, Object>> lstCopy = new ArrayList<>();
            for (Map<String, Object> item : lstitem) {
                Map<String, Object> hashMap = new HashMap<>(item);
                hashMap.put("ToWho", 1);
                hashMap.put("PageNo", 1);
                lst.add(hashMap);

                Map<String, Object> hashMapCopy = new HashMap<>(hashMap);
                hashMapCopy.put("ToWho", 2);
                lstCopy.add(hashMapCopy);
            }
            lst.addAll(lstCopy);
//            List<Map<String, Object>> lst = lstitem.stream()
//                    .map(item -> {
//                        Map<String, Object> hashMap = new HashMap<>(item);
//                        hashMap.put("ToWho", 1);
//                        hashMap.put("PageNo", 1);
//                        return hashMap;
//                    })
//                    .collect(Collectors.toList());
//            List<Map<String, Object>> lstCopy = lstitem.stream()
//                    .map(item -> {
//                        Map<String, Object> hashMap = new HashMap<>(item);
//                        hashMap.put("ToWho", 2);
//                        hashMap.put("PageNo", 1);
//                        return hashMap;
//                    })
//                    .collect(Collectors.toList());
//            lst.addAll(lstCopy);
            //            PrintColor.red("lst:" + lst);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:一页两联");
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (StringUtils.isNotBlank(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入子表List 自定义云打印", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printFreeWebList", method = RequestMethod.POST)
    public R<String> printFreeWebList(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据 子表List
            List<Map<String, Object>> lstitem = JSON.parseObject(json, new TypeReference<List<Map<String, Object>>>() {
            });
            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (lstitem != null && !lstitem.isEmpty() && lstitem.get(0) != null) {
                map.put("groupname", StringUtils.defaultIfBlank(lstitem.get(0).get("groupname") == null ? "" : lstitem.get(0).get("groupname").toString(), ""));
                map.put("abbreviate", StringUtils.defaultIfBlank(lstitem.get(0).get("abbreviate") == null ? "" : lstitem.get(0).get("abbreviate").toString(), ""));
                map.put("groupuid", StringUtils.defaultIfBlank(lstitem.get(0).get("groupuid") == null ? "" : lstitem.get(0).get("groupuid").toString(), ""));
            }


            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lstitem);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:子表List");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

