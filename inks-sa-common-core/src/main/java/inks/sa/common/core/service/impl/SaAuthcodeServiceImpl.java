package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaAuthcodeEntity;
import inks.sa.common.core.domain.pojo.SaAuthcodePojo;
import inks.sa.common.core.mapper.SaAuthcodeMapper;
import inks.sa.common.core.service.SaAuthcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 授权码(SaAuthcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-15 09:38:43
 */
@Service("saAuthcodeService")
public class SaAuthcodeServiceImpl implements SaAuthcodeService {
    @Resource
    private SaAuthcodeMapper saAuthcodeMapper;

    private static void cleanNull(SaAuthcodePojo saAuthcodePojo) {
        if (saAuthcodePojo.getAuthcode() == null) saAuthcodePojo.setAuthcode("");
        if (saAuthcodePojo.getAuthdesc() == null) saAuthcodePojo.setAuthdesc("");
        if (saAuthcodePojo.getUsername() == null) saAuthcodePojo.setUsername("");
        if (saAuthcodePojo.getUserpassword() == null) saAuthcodePojo.setUserpassword("");
        if (saAuthcodePojo.getRownum() == null) saAuthcodePojo.setRownum(0);
        if (saAuthcodePojo.getEnabledmark() == null) saAuthcodePojo.setEnabledmark(0);
        if (saAuthcodePojo.getRemark() == null) saAuthcodePojo.setRemark("");
        if (saAuthcodePojo.getCreateby() == null) saAuthcodePojo.setCreateby("");
        if (saAuthcodePojo.getCreatebyid() == null) saAuthcodePojo.setCreatebyid("");
        if (saAuthcodePojo.getCreatedate() == null) saAuthcodePojo.setCreatedate(new Date());
        if (saAuthcodePojo.getLister() == null) saAuthcodePojo.setLister("");
        if (saAuthcodePojo.getListerid() == null) saAuthcodePojo.setListerid("");
        if (saAuthcodePojo.getModifydate() == null) saAuthcodePojo.setModifydate(new Date());
        if (saAuthcodePojo.getCustom1() == null) saAuthcodePojo.setCustom1("");
        if (saAuthcodePojo.getCustom2() == null) saAuthcodePojo.setCustom2("");
        if (saAuthcodePojo.getCustom3() == null) saAuthcodePojo.setCustom3("");
        if (saAuthcodePojo.getCustom4() == null) saAuthcodePojo.setCustom4("");
        if (saAuthcodePojo.getCustom5() == null) saAuthcodePojo.setCustom5("");
        if (saAuthcodePojo.getTenantid() == null) saAuthcodePojo.setTenantid("");
        if (saAuthcodePojo.getTenantname() == null) saAuthcodePojo.setTenantname("");
        if (saAuthcodePojo.getRevision() == null) saAuthcodePojo.setRevision(0);
    }

    @Override
    public SaAuthcodePojo getEntity(String key) {
        return this.saAuthcodeMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaAuthcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaAuthcodePojo> lst = saAuthcodeMapper.getPageList(queryParam);
            PageInfo<SaAuthcodePojo> pageInfo = new PageInfo<SaAuthcodePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaAuthcodePojo insert(SaAuthcodePojo saAuthcodePojo) {
        //初始化NULL字段
        cleanNull(saAuthcodePojo);
        SaAuthcodeEntity saAuthcodeEntity = new SaAuthcodeEntity();
        BeanUtils.copyProperties(saAuthcodePojo, saAuthcodeEntity);
        //生成雪花id
        saAuthcodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saAuthcodeEntity.setRevision(1);  //乐观锁
        this.saAuthcodeMapper.insert(saAuthcodeEntity);
        return this.getEntity(saAuthcodeEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saAuthcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaAuthcodePojo update(SaAuthcodePojo saAuthcodePojo) {
        SaAuthcodeEntity saAuthcodeEntity = new SaAuthcodeEntity();
        BeanUtils.copyProperties(saAuthcodePojo, saAuthcodeEntity);
        this.saAuthcodeMapper.update(saAuthcodeEntity);
        return this.getEntity(saAuthcodeEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saAuthcodeMapper.delete(key);
    }

}
