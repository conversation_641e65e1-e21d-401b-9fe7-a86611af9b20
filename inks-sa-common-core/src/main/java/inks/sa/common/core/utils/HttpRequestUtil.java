package inks.sa.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Component
public class HttpRequestUtil implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(HttpRequestUtil.class);

    private static RestTemplate restTemplate;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) {
        if (HttpRequestUtil.restTemplate == null) {
            HttpRequestUtil.restTemplate = applicationContext.getBean(RestTemplate.class);
            log.info("RestTemplate 已注入到 HttpUtils");
        }
    }

    /**
     * 发送 GET 请求 (无 headers)
     *
     * @param url 请求的 URL
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * JSONObject result = HttpRequestUtil.sendGet("https://api.example.com/users");
     * }
     * </pre>
     */
    public static JSONObject sendGet(String url) {
        return sendGet(url, null);
    }

    /**
     * 发送 GET 请求
     *
     * @param url     请求的 URL
     * @param headers 请求头 可以为 null
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * Map<String, String> headers = new HashMap<>();
     * headers.put("Authorization", "Bearer your-token");
     * headers.put("User-Agent", "MyApp/1.0");
     * JSONObject result = HttpRequestUtil.sendGet("https://api.example.com/users", headers);
     * }
     * </pre>
     */
    public static JSONObject sendGet(String url, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpHeaders::add);
            }
            log.info("发送 GET 请求 -> URL: {}, Headers: {}", url, httpHeaders);

            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            String body = response.getBody();
            log.info("GET 响应 -> 状态: {}, Body: {}", response.getStatusCode(), body);

            return JSON.parseObject(body);
        } catch (RestClientException e) {
            log.error("发送 GET 请求失败, URL: {}", url, e);
            return null;
        }
    }

    /**
     * 发送 POST 请求 (JSONObject body, 无 headers)
     *
     * @param url  请求的 URL
     * @param body 请求体 可以为 null
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * JSONObject postData = new JSONObject();
     * postData.put("name", "John");
     * postData.put("age", 30);
     * JSONObject result = HttpRequestUtil.sendPost("https://api.example.com/users", postData);
     * }
     * </pre>
     */
    public static JSONObject sendPost(String url, JSONObject body) {
        return sendPost(url, body, null);
    }

    /**
     * 发送 POST 请求 (String 参数, 无 headers)
     *
     * @param url  请求的 URL
     * @param parm 请求体字符串 (JSON格式)
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * String jsonParam = "{\"name\":\"John\",\"age\":30}";
     * JSONObject result = HttpRequestUtil.sendPost("https://api.example.com/users", jsonParam);
     * }
     * </pre>
     */
    public static JSONObject sendPost(String url, String parm) {
        return sendPost(url, parm, null);
    }

    /**
     * 发送 POST 请求 (String 参数, 带 headers)
     *
     * @param url     请求的 URL
     * @param parm    请求体字符串 (JSON格式)
     * @param headers 请求头 可以为 null
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * String jsonParam = "{\"name\":\"John\",\"age\":30}";
     * Map<String, String> headers = new HashMap<>();
     * headers.put("Authorization", "Bearer your-token");
     * headers.put("Content-Type", "application/json");
     * JSONObject result = HttpRequestUtil.sendPost("https://api.example.com/users", jsonParam, headers);
     * }
     * </pre>
     */
    public static JSONObject sendPost(String url, String parm, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpHeaders::add);
            }

            log.info("发送 POST 请求 -> URL: {}, Headers: {}, Body: {}", url, httpHeaders, parm);

            HttpEntity<String> entity = new HttpEntity<>(parm, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            String bodyStr = response.getBody();
            log.info("POST 响应 -> 状态: {}, Body: {}", response.getStatusCode(), bodyStr);

            return JSON.parseObject(bodyStr);
        } catch (RestClientException e) {
            log.error("发送 POST 请求失败, URL: {}, Body: {}", url, parm, e);
            return null;
        }
    }

    /**
     * 发送 POST 请求
     *
     * @param url     请求的 URL
     * @param body    请求体 可以为 null
     * @param headers 请求头 可以为 null
     * @return 响应的 JSON 对象
     * @example <pre>
     * {@code
     * JSONObject postData = new JSONObject();
     * postData.put("name", "John");
     * postData.put("age", 30);
     * Map<String, String> headers = new HashMap<>();
     * headers.put("Authorization", "Bearer your-token");
     * JSONObject result = HttpRequestUtil.sendPost("https://api.example.com/users", postData, headers);
     * }
     * </pre>
     */
    public static JSONObject sendPost(String url, JSONObject body, Map<String, String> headers) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(httpHeaders::add);
            }
            String requestBody = (body == null) ? "{}" : body.toJSONString();

            log.info("发送 POST 请求 -> URL: {}, Headers: {}, Body: {}", url, httpHeaders, requestBody);

            HttpEntity<String> entity = new HttpEntity<>(requestBody, httpHeaders);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            String bodyStr = response.getBody();
            log.info("POST 响应 -> 状态: {}, Body: {}", response.getStatusCode(), bodyStr);

            return JSON.parseObject(bodyStr);
        } catch (RestClientException e) {
            log.error("发送 POST 请求失败, URL: {}, Body: {}", url, body, e);
            return null;
        }
    }
}