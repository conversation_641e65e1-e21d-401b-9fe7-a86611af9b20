package inks.sa.common.core.config.flyway;

import inks.sa.common.core.utils.PrintColor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.awt.Desktop;
import java.net.URI;

/**
 * Flyway进度管理器
 * 负责在应用启动完成后打开进度页面
 */
@Component
public class FlywayProgressManager {
    
    private static boolean needOpenProgressPage = false;
    private static String databaseName = "";
    
    /**
     * 标记需要打开进度页面
     */
    public static void markNeedOpenProgressPage(String dbName) {
        needOpenProgressPage = true;
        databaseName = dbName;
    }
    
    /**
     * 应用启动完成后的事件监听器
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        if (needOpenProgressPage) {
            // 应用已完全启动，现在可以安全地打开浏览器
            openProgressPage();
            needOpenProgressPage = false; // 重置标记
        }
    }
    
    /**
     * 打开进度页面
     */
    private void openProgressPage() {
        new Thread(() -> {
            try {
                // 稍微等待一下确保所有服务都已就绪
                Thread.sleep(1000);
                
                String url = "http://localhost:8081/flyway/progress";
                boolean opened = BrowserLauncher.openUrl(url);
                if (opened) {
                    String msg = "数据库 [" + databaseName + "] 初始化进度页面已打开: " + url;
                    PrintColor.red(msg);
                    FlywayProgressWebSocketHandler.sendLog(msg);
                } else {
                    String msg = "无法自动打开浏览器，请手动访问: " + url;
                    PrintColor.red(msg);
                    FlywayProgressWebSocketHandler.sendLog(msg);
                }
            } catch (Exception e) {
                String errorMsg = "打开进度页面失败: " + e.getMessage() + "，请手动访问: http://localhost:8081/flyway/progress";
                PrintColor.red(errorMsg);
                FlywayProgressWebSocketHandler.sendLog(errorMsg);
            }
        }).start();
    }
}
