package inks.sa.common.core.config.oa;

import com.alibaba.fastjson.JSON;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.OAUtil;
import inks.sa.common.core.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用审批处理器
 * 使用反射机制处理不同业务实体的审批操作
 */
@Component
public class OAProcessor {

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaJustauthService saJustauthService;

    @Resource
    private OAHandler oaHandler;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Value("${inks.tid}")
    private String tid;

    /**
     * 发出第三方审批的通用方法
     */
    public R<ApprrecPojo> sendApproval(String key, String apprid, String type, String remark,
                                       Object service, String tableName, String approvalName) {
        try {
            if (StringUtils.isBlank(type)) type = "wxe"; // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;

            // 获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 从redis中获取模板对象
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();

            // 通过反射获取业务实体数据（兼容1参、2参）
            //Object billEntity = invokeMethod(service, "getBillEntity", key);
// 优先尝试 getBillEntity(key, tid)，找不到再尝试 getBillEntity(key)，找不到再尝试 getEntity(key, tid），找不到再尝试 getEntity(key)
            Object billEntity = null;
            try {
                billEntity = invokeMethod(service, "getBillEntity", key, loginUser.getTenantid());
            } catch (NoSuchMethodException e1) {
                try {
                    billEntity = invokeMethod(service, "getBillEntity", key);
                } catch (NoSuchMethodException e2) {
                    try {
                        billEntity = invokeMethod(service, "getEntity", key, loginUser.getTenantid());
                    } catch (NoSuchMethodException e3) {
                        billEntity = invokeMethod(service, "getEntity", key);
                    }
                }
            }

            Map<String, Object> billMap = OAUtil.billToOAMap(billEntity);
            approvePojo.setObject(billMap);

            // 发起oms审批,先判断是否正在审批
            Integer oaflowmark = (Integer) invokeMethod(billEntity, "getOaflowmark");
            if (oaflowmark != null && oaflowmark == 1) {
                return R.fail("该单据已发起OA审批");
            }

            if ("oms".equals(type)) {
                // 创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();

                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();

                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法

                // 写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            } else {
                // 创建VM数据对象
                VelocityContext context = new VelocityContext();

                // 获得第三方账号
                SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
                JustauthPojo justauthPojo = new JustauthPojo();
                if (justAuth == null) {
                    PrintColor.red("获得第三方账号出错");
                    return R.fail("获得第三方账号出错");
                }
                org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);

                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();

                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();

                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法

                // 写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }

            // 新建审批记录
            Date date = new Date();
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            if (StringUtils.isBlank(approvalName)) {
                approvalName = tableName + "审批";
            }
            apprrecPojo.setApprname(approvalName);
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key); // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(date);
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(date);
            apprrecPojo.setTenantid(loginUser.getTenantid());
            apprrecPojo.setRemark(remark == null ? "" : remark);

            // 将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setKeyValue(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);

            if ("wxe".equals(type) || "ding".equals(type)) {
                R r = this.oaHandler.sendApproval(apprrecPojo.getId(), type);
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            }

            // 发起审批成功,需设置OaFlowMark=1 并更新单据
            setOaFlowMark(tableName, key, 1);

            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审批回调修改状态的通用方法
     */
    public R justApproval(String key, String type, String approved, String tableName) {
        try {
            PrintColor.red("/justapprovel 审批回调修改状态 approved:" + approved);

            // 1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            PrintColor.red("审批回调apprrecPojo" + JSON.toJSONString(apprrecPojo));
            // 单据id
            String billid = apprrecPojo.getBillid();
            // 2.1 获得单据数据
            //Object billPojo = invokeMethod(businessService, "getEntity", apprrecPojo.getBillid());
// 优先尝试 getBillEntity(key, tid)，找不到再尝试 getBillEntity(key)
//            Object billPojo;
//            try {
//                billPojo = invokeMethod(service, "getEntity", apprrecPojo.getBillid(), tid); // 推荐都传
//            } catch (NoSuchMethodException e) {
//                billPojo = invokeMethod(service, "getEntity", apprrecPojo.getBillid());
//            }
            // 3. 写入审核批
            if (type == null) type = "wxe";

            // oms审批即将完成,需设置OaFlowMark=0
            setOaFlowMark(tableName, billid, 0);


            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }

            if ("oms".equals(type)) {
                setAssessorFields(tableName, billid, apprrecPojo.getUserid(), apprrecPojo.getRealname(), new Date());
            } else {
                SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, null);
                if (justauthByUuid == null) {
                    PrintColor.red("审批回调失败,未找到第三方账号");
                    return R.fail("审批回调失败,未找到第三方账号");
                }
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(justauthByUuid, justauthPojo);
                // 审批图标根据Assessor判断的，所以realname必须有值！！！
                String realname = StringUtils.isBlank(justauthByUuid.getRealname()) ? "未配置JustAuth.RealName" : justauthByUuid.getRealname();
                setAssessorFields(tableName, billid, justauthPojo.getUserid(), realname, new Date());
            }
            return R.ok(true);
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

    public void setOaFlowMark(String table, String key, int val) {
        String safeTable = table.replaceAll("[^a-zA-Z0-9_]", "");
        String sql = "UPDATE " + safeTable + " SET OaFlowMark = ? WHERE id = ?";
        String realSql = "UPDATE " + safeTable + " SET OaFlowMark = " + val + " WHERE id = '" + key + "'";
        PrintColor.red("setOaFlowMark sql: " + sql);
        PrintColor.red("setOaFlowMark 实际SQL: " + realSql);

        jdbcTemplate.update(sql, val, key);
    }

    public void setAssessorFields(String table, String key, String assessorid, String assessor, Date assessdate) {
        String safeTable = table.replaceAll("[^a-zA-Z0-9_]", "");
        String sql = "UPDATE " + safeTable + " SET Assessorid=?, Assessor=?, Assessdate=? WHERE id=?";
        String realSql = "UPDATE " + safeTable + " SET Assessorid='" + assessorid
                + "', Assessor='" + assessor
                + "', Assessdate='" + assessdate
                + "' WHERE id='" + key + "'";
        PrintColor.red("setAssessorFields sql: " + sql);
        PrintColor.red("setAssessorFields 实际SQL: " + realSql);

        jdbcTemplate.update(sql, assessorid, assessor, assessdate, key);
    }


    /**
     * 反射调用方法的工具方法，支持多参数自动适配
     */
    private Object invokeMethod(Object target, String methodName, Object... args) throws Exception {
        Class<?> clazz = target.getClass();
        Method bestMethod = null;
        // 递归所有父类，查找合适方法
        while (clazz != null && clazz != Object.class) {
            Method[] methods = clazz.getDeclaredMethods();
            outer:
            for (Method method : methods) {
                if (!method.getName().equals(methodName)) continue;
                Class<?>[] paramTypes = method.getParameterTypes();
                if (paramTypes.length != args.length) continue;
                for (int i = 0; i < paramTypes.length; i++) {
                    if (args[i] == null) continue; // null 兼容
                    if (paramTypes[i].isPrimitive()) {
                        if (!isPrimitiveAssignable(paramTypes[i], args[i].getClass())) continue outer;
                    } else if (!paramTypes[i].isAssignableFrom(args[i].getClass())) continue outer;
                }
                bestMethod = method;
                break;
            }
            if (bestMethod != null) break;
            clazz = clazz.getSuperclass(); // 关键：递归到父类
        }
        if (bestMethod == null) {
            throw new NoSuchMethodException("No suitable method found: " + methodName + " for " + target.getClass().getName());
        }
        bestMethod.setAccessible(true);
        return bestMethod.invoke(target, args);
    }

    private boolean isPrimitiveAssignable(Class<?> primitiveType, Class<?> wrapperType) {
        if (primitiveType == int.class && wrapperType == Integer.class) return true;
        if (primitiveType == long.class && wrapperType == Long.class) return true;
        if (primitiveType == double.class && wrapperType == Double.class) return true;
        if (primitiveType == float.class && wrapperType == Float.class) return true;
        if (primitiveType == boolean.class && wrapperType == Boolean.class) return true;
        if (primitiveType == char.class && wrapperType == Character.class) return true;
        if (primitiveType == byte.class && wrapperType == Byte.class) return true;
        if (primitiveType == short.class && wrapperType == Short.class) return true;
        return false;
    }

}
