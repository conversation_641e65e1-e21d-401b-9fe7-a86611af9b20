package inks.sa.common.core.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaAuthcodePojo;
import inks.sa.common.core.service.SaAuthcodeService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 授权码(Sa_AuthCode)表控制层
 *
 * <AUTHOR>
 * @since 2024-08-15 09:38:43
 */

@RestController
@RequestMapping("SaAuthcode")
@Api(tags = "通用:授权码管理")
public class A_SaAuthcodeController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaAuthcodeController.class);
    @Resource
    private SaAuthcodeService saAuthcodeService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取授权码详细信息", notes = "获取授权码详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.List")
    public R<SaAuthcodePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAuthcodeService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.List")
    public R<PageInfo<SaAuthcodePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_AuthCode.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saAuthcodeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增授权码", notes = "新增授权码", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.Add")
    public R<SaAuthcodePojo> create(@RequestBody String json) {
        try {
            SaAuthcodePojo saAuthcodePojo = JSONArray.parseObject(json, SaAuthcodePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saAuthcodePojo.setCreateby(loginUser.getRealName());   // 创建者
            saAuthcodePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saAuthcodePojo.setCreatedate(new Date());   // 创建时间
            saAuthcodePojo.setLister(loginUser.getRealname());   // 制表
            saAuthcodePojo.setListerid(loginUser.getUserid());    // 制表id
            saAuthcodePojo.setModifydate(new Date());   //修改时间
            saAuthcodePojo.setAuthcode(IdUtil.simpleUUID().substring(1, 21));  // code

            return R.ok(this.saAuthcodeService.insert(saAuthcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改授权码", notes = "修改授权码", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.Edit")
    public R<SaAuthcodePojo> update(@RequestBody String json) {
        try {
            SaAuthcodePojo saAuthcodePojo = JSONArray.parseObject(json, SaAuthcodePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saAuthcodePojo.setLister(loginUser.getRealname());   // 制表
            saAuthcodePojo.setListerid(loginUser.getUserid());    // 制表id
            saAuthcodePojo.setModifydate(new Date());   //修改时间
            //            saAuthcodePojo.setAssessor(""); // 审核员
            //            saAuthcodePojo.setAssessorid(""); // 审核员id
            //            saAuthcodePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saAuthcodeService.update(saAuthcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除授权码", notes = "删除授权码", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAuthcodeService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_AuthCode.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaAuthcodePojo saAuthcodePojo = this.saAuthcodeService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saAuthcodePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

