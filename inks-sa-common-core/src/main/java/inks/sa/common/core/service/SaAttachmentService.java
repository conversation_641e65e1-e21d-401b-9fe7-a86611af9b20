package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaAttachmentPojo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 附件中心(SaAttachment)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-29 13:26:54
 */
public interface SaAttachmentService {


    SaAttachmentPojo getEntity(String key);

    PageInfo<SaAttachmentPojo> getPageList(QueryParam queryParam);

    SaAttachmentPojo insert(SaAttachmentPojo saAttachmentPojo);

    SaAttachmentPojo update(SaAttachmentPojo saAttachmentpojo);

    int delete(String key, LoginUser loginUser);


}
