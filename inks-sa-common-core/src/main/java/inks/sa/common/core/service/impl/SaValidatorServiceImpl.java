package inks.sa.common.core.service.impl;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaValidatorPojo;
import inks.sa.common.core.domain.SaValidatorEntity;
import inks.sa.common.core.domain.vo.ValidationResponse;
import inks.sa.common.core.domain.vo.ValidationResultCollector;
import inks.sa.common.core.mapper.SaValidatorMapper;
import inks.sa.common.core.service.SaValidatorService;
import inks.sa.common.core.utils.ExpressionUtils;
import inks.sa.common.core.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;

import java.io.StringWriter;
import java.util.*;

/**
 * 数据验证(SaValidator)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-18 14:23:32
 */
@Service("saValidatorService")
public class SaValidatorServiceImpl implements SaValidatorService {
    @Resource
    private SaValidatorMapper saValidatorMapper;

    @Override
    public SaValidatorPojo getEntity(String key) {
        return this.saValidatorMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaValidatorPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaValidatorPojo> lst = saValidatorMapper.getPageList(queryParam);
            PageInfo<SaValidatorPojo> pageInfo = new PageInfo<SaValidatorPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaValidatorPojo insert(SaValidatorPojo saValidatorPojo) {
        //初始化NULL字段
        cleanNull(saValidatorPojo);
        SaValidatorEntity saValidatorEntity = new SaValidatorEntity(); 
        BeanUtils.copyProperties(saValidatorPojo,saValidatorEntity);
        //生成雪花id
          saValidatorEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saValidatorEntity.setRevision(1);  //乐观锁
          this.saValidatorMapper.insert(saValidatorEntity);
        return this.getEntity(saValidatorEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saValidatorPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaValidatorPojo update(SaValidatorPojo saValidatorPojo) {
        SaValidatorEntity saValidatorEntity = new SaValidatorEntity(); 
        BeanUtils.copyProperties(saValidatorPojo,saValidatorEntity);
        this.saValidatorMapper.update(saValidatorEntity);
        return this.getEntity(saValidatorEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saValidatorMapper.delete(key) ;
    }
    

    private static void cleanNull(SaValidatorPojo saValidatorPojo) {
        if(saValidatorPojo.getValicode()==null) saValidatorPojo.setValicode("");
        if(saValidatorPojo.getValititle()==null) saValidatorPojo.setValititle("");
        if(saValidatorPojo.getSqlmark()==null) saValidatorPojo.setSqlmark(0);
        if(saValidatorPojo.getSqlstr()==null) saValidatorPojo.setSqlstr("");
        if(saValidatorPojo.getExpression()==null) saValidatorPojo.setExpression("");
        if(saValidatorPojo.getTipmsg()==null) saValidatorPojo.setTipmsg("");
        if(saValidatorPojo.getTipmsgen()==null) saValidatorPojo.setTipmsgen("");
        if(saValidatorPojo.getRequiredmark()==null) saValidatorPojo.setRequiredmark(0);
        if(saValidatorPojo.getItemloopmark()==null) saValidatorPojo.setItemloopmark(0);
        if(saValidatorPojo.getEnabledmark()==null) saValidatorPojo.setEnabledmark(0);
        if(saValidatorPojo.getRownum()==null) saValidatorPojo.setRownum(0);
        if(saValidatorPojo.getRemark()==null) saValidatorPojo.setRemark("");
        if(saValidatorPojo.getCreateby()==null) saValidatorPojo.setCreateby("");
        if(saValidatorPojo.getCreatebyid()==null) saValidatorPojo.setCreatebyid("");
        if(saValidatorPojo.getCreatedate()==null) saValidatorPojo.setCreatedate(new Date());
        if(saValidatorPojo.getLister()==null) saValidatorPojo.setLister("");
        if(saValidatorPojo.getListerid()==null) saValidatorPojo.setListerid("");
        if(saValidatorPojo.getModifydate()==null) saValidatorPojo.setModifydate(new Date());
        if(saValidatorPojo.getCustom1()==null) saValidatorPojo.setCustom1("");
        if(saValidatorPojo.getCustom2()==null) saValidatorPojo.setCustom2("");
        if(saValidatorPojo.getCustom3()==null) saValidatorPojo.setCustom3("");
        if(saValidatorPojo.getCustom4()==null) saValidatorPojo.setCustom4("");
        if(saValidatorPojo.getCustom5()==null) saValidatorPojo.setCustom5("");
        if(saValidatorPojo.getTenantid()==null) saValidatorPojo.setTenantid("");
        if(saValidatorPojo.getTenantname()==null) saValidatorPojo.setTenantname("");
        if(saValidatorPojo.getRevision()==null) saValidatorPojo.setRevision(0);
   }


    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 执行数据验证
     *
     * @param valicode  验证编码
     * @param dataObj   待验证数据对象
     * @param loginUser 登录用户信息
     * @return ValidationResponse 验证结果
     */
    public ValidationResponse validate(String valicode, Map<String, Object> dataObj, LoginUser loginUser) {
        try {
            //String tid = loginUser.getTenantid();
            // 1. 获取所有启用的验证规则
            List<SaValidatorPojo> validators = saValidatorMapper.getListByValicodeEnabled(valicode, "");
            if (validators == null || validators.isEmpty()) {
                return null; //没有设置规则也返回200，null
            }

            // 2. 创建一个总的结果对象
            int totalCount = 0;
            int totalRequCount = 0;
            List<Map<String, Object>> allMessages = new ArrayList<>();

            // 3. 遍历每个验证规则并执行验证
            for (SaValidatorPojo validator : validators) {
                ValidationResponse singleResponse = validateSingle(validator, dataObj);
                if (singleResponse != null) {
                    totalCount += singleResponse.getTotal();
                    totalRequCount += singleResponse.getRequcount();
                    if (singleResponse.getList() != null) {
                        allMessages.addAll(singleResponse.getList());
                    }
                }
            }

            // 4. 构建合并后的返回结果
            return new ValidationResponse.Builder()
                    .total(totalCount)
                    .requcount(totalRequCount)
                    .list(allMessages)
                    .build();

        } catch (Exception e) {
            throw new BaseBusinessException("验证过程发生错误: " + e.getMessage());
        }
    }

    /**
     * 执行单个验证规则
     */
    private ValidationResponse validateSingle(SaValidatorPojo validator, Map<String, Object> dataObj) {
        // 初始化结果收集器
        ValidationResultCollector collector = new ValidationResultCollector(validator.getRequiredmark());

        // 根据规则类型执行验证: 是否进行item循环验证
        if (validator.getItemloopmark() == 1) {
            processItemLoop(validator, dataObj, collector);
        } else {//普通单次验证
            processSingle(validator, dataObj, collector);
        }

        // 返回单个规则的验证结果
        return new ValidationResponse.Builder()
                .total(collector.getTotalCount())
                .requcount(collector.getTrueCount())
                .list(collector.getMessages())
                .build();
    }

    /**
     * 处理循环验证 必须有item:[]属性
     */
    private void processItemLoop(SaValidatorPojo validator, Map<String, Object> dataObj,
                                 ValidationResultCollector collector) {
        if (dataObj.get("item") == null) {
            throw new BaseBusinessException("循环验证必须有item:[]属性");
        }
        List<Map<String, Object>> items = (List<Map<String, Object>>) dataObj.get("item");
        collector.setTotalCount(items.size());

        for (Map<String, Object> item : items) {
            Map<String, Object> fullData = new HashMap<>(dataObj);
            fullData.putAll(item);

            validateSingleItem(validator, fullData, collector);
        }
    }

    /**
     * 处理单项验证
     */
    private void processSingle(SaValidatorPojo validator, Map<String, Object> dataObj,
                               ValidationResultCollector collector) {
        collector.setTotalCount(1);
        validateSingleItem(validator, dataObj, collector);
    }

    /**
     * 验证单个数据项
     */
    private void validateSingleItem(SaValidatorPojo validator, Map<String, Object> data,
                                    ValidationResultCollector collector) {
        // 执行SQL查询（如果需要）查询结果放到data的sqlobj属性中
        if (validator.getSqlmark() == 1) {
            String sql = processTemplate(validator.getSqlstr(), data);
            Map<String, Object> queryResult = jdbcTemplate.queryForMap(sql);
            data.put("sqlobj", queryResult);
        }

        // 计算表达式
        String resolvedExpression = processTemplate(validator.getExpression(), data);
        boolean result = ExpressionUtils.evaluateExpression(resolvedExpression);

        if (result) {
            collector.incrementTrueCount();
            // 生成提示消息
            String messageTemplate = StringUtils.isNotEmpty(validator.getTipmsg()) ?
                    validator.getTipmsg() : validator.getTipmsgen();
            String message = processTemplate(messageTemplate, data);
            collector.addMessage(message);
        }
    }

    /**
     * 使用Velocity处理模板
     */
    private String processTemplate(String template, Map<String, Object> data) {
        VelocityContext context = new VelocityContext();
        context.put("math", Math.class);  // 添加 Math 工具
        // 将数据放入上下文中 最终context格式为： {id:1,name:"张三",sqlobj:{price:12,quantity:10,amount:120}}
        data.forEach(context::put);
        StringWriter writer = new StringWriter();
        try {
            PrintColor.lan("Velocity引擎替换前内容:" + template);
            Velocity.evaluate(context, writer, "log", template);
            PrintColor.red("Velocity引擎替换后内容:" + writer);
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("模板处理失败", e);
        }
    }
   
}
