package inks.sa.common.core.feign;

import inks.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


@Service
@FeignClient(name = "sautsClient", url = "${inks.svcFeign:http://localhost:10684}")
//@FeignClient(name = "grfClient", url = "http://*************:18801")
public interface SaUtsFeignClient {

    @RequestMapping(
            value = {"/S34M06B1/sendapprovel"},
            method = {RequestMethod.GET}
    )
    R wxeapprovel(@RequestParam("key") String var1, @RequestParam("tid") String var2);

    @RequestMapping(
            value = {"/S34M06B2/sendapprovel"},
            method = {RequestMethod.GET}
    )
    R dingapprovel(@RequestParam("key") String var1, @RequestParam("tid") String var2);
}