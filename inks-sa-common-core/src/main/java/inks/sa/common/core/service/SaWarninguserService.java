package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaWarninguserPojo;
import inks.sa.common.core.domain.SaWarninguserEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 预警用户(SaWarninguser)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
public interface SaWarninguserService {


    SaWarninguserPojo getEntity(String key);

    PageInfo<SaWarninguserPojo> getPageList(QueryParam queryParam);

    SaWarninguserPojo insert(SaWarninguserPojo saWarninguserPojo);

    SaWarninguserPojo update(SaWarninguserPojo saWarninguserpojo);

    int delete(String key);

    List<SaWarninguserPojo> getListByUser(String userid, String tenantid);

    List<SaWarninguserPojo> getWarnListByUser(String userid, String tenantid);
}
