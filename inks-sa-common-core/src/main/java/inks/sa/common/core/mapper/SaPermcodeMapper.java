package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaPermcodeEntity;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限编码(SaPermcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:35
 */
@Mapper
public interface SaPermcodeMapper {


    SaPermcodePojo getEntity(@Param("key") String key);


    List<SaPermcodePojo> getPageList(QueryParam queryParam);

    int insert(SaPermcodeEntity saPermcodeEntity);

    int update(SaPermcodeEntity saPermcodeEntity);


    int delete(@Param("key") String key);

}

