package inks.sa.common.core.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaUserService;
import inks.sa.common.core.service.impl.SaUserServiceImpl;
import inks.sa.common.core.utils.PrintColor;
import inks.sa.common.core.utils.ding.AccessTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


@RestController
@RequestMapping("SaUser/dinglogin")
@Api(tags = "通用:用户服务-钉钉登录")
public class A_SaUser_DingLoginController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaUser_DingLoginController.class);
    @Resource
    private SaUserService saUserService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaConfigService saConfigService;


    // 根据索引获取跳转地址 http://dev.inksyun.com:31202/#/?key={key};http://dev.inksyun.com:31203/#/?key={key};
    public static String getJumpAddressByNo(String addresses, Integer no) {
        String[] addressArray = addresses.trim().split(";");
        if (no == null || no < 0 || no >= addressArray.length) {
            no = 0;
        }
        return addressArray[no];
    }

//    public static void main(String[] args) {
//        String addresses = "http://dev.inksyun.com:31202/#/?key={key}";
//        String jumpAddress = getJumpAddressByNo(addresses, 1);
//        System.out.println(jumpAddress);
//    }

    /**
     * 获取授权用户的个人信息 openapi@dingtalk
     * code 临时授权码
     * state 租户ID
     * no 跳转地址的索引, 跳转地址格式为:http://dev.inksyun.com:31202/#/?key={key};http://dev.inksyun.com:31203/#/?key={key}
     * 以分号分隔多个跳转地址,no相当于索引,确定是第几个跳转地址 (no为0或不传时默认第一个跳转地址, 若只有一个跳转地址,则忽略no)
     *
     * @return
     * @throws Exception ServiceResult<Map<String,Object>> 2020-11-4
     */
    @RequestMapping(value = "/redirectApp", method = RequestMethod.GET)
    public void redirectApp(
            @RequestParam(required = false, defaultValue = "0") Integer no,
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        PrintColor.red("/redirectApp开始钉钉免登 code：" + code + " state：" + state + " no:" + no);
        logger.info("开始App免登 code：" + code);
        logger.info("开始App免登 state：" + state);
        logger.info("开始App免登 no：" + no);

        String loginurl = "";
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);//"system.ding.appkey"
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);//"system.ding.appsecret"
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        loginurl = mapcfg.get(ConfigConstant.DING_APPLOGINURL);//"system.ding.apploginurl"
        logger.info("钉钉免登 完整AppLoginUrl:" + loginurl);
        loginurl = getJumpAddressByNo(loginurl, no);
        logger.info("钉钉免登 通过no实际跳转AppLoginUrl:" + loginurl);


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 通过临时授权码获取授权用户的个人信息
        DefaultDingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest reqBycodeRequest = new OapiSnsGetuserinfoBycodeRequest();

        reqBycodeRequest.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse bycodeResponse = client2.execute(reqBycodeRequest, AccessTokenUtil.AppKey, AccessTokenUtil.AppSecret);

        // 根据unionid获取钉钉的userId
        String unionid = bycodeResponse.getUserInfo().getUnionid();
        DingTalkClient clientDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest reqGetbyunionidRequest = new OapiUserGetbyunionidRequest();
        reqGetbyunionidRequest.setUnionid(unionid);
        OapiUserGetbyunionidResponse oapiUserGetbyunionidResponse = clientDingTalkClient.execute(reqGetbyunionidRequest, access_token);

        // 根据获取用户钉钉的userId,即PiJustAuth.AuthUuid
        String dingUserid = oapiUserGetbyunionidResponse.getResult().getUserid();

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        SaUserPojo userPojo = saUserService.getEntityByDingUserid(dingUserid);
        if (userPojo == null) {
            throw new BaseBusinessException("当前钉钉未绑定账号,请联系管理员");
        }
        // 登录并获取token
        Map<String, Object> tokenMap = saUserService.loginCheck(userPojo.getUsername(), userPojo.getPassword(), request, null);
        String accessToken = tokenMap.get("access_token").toString();
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", accessToken);
        PrintColor.red("跳转免登 AppLoginUrl:" + loginurl);
        response.sendRedirect(loginurl);
    }


    /**
     * 获取授权用户的个人信息 openapi@dingtalk
     *
     * @return
     * @throws Exception ServiceResult<Map<String,Object>> 2020-11-4
     */
    @RequestMapping(value = "/redirectWeb", method = RequestMethod.GET)
    public void redirectWeb(
            @RequestParam(required = false, defaultValue = "0") Integer no,
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {

        logger.info("开始Web免登 code：" + code);
        logger.info("开始Web免登 state：" + state);

        logger.info("------开始Web钉钉免登-------");

        String loginurl = "";
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        loginurl = mapcfg.get(ConfigConstant.DING_WEBLOGINURL);
        logger.info("钉钉免登 完整WebLoginUrl:" + loginurl);
        loginurl = getJumpAddressByNo(loginurl, no);
        logger.info("钉钉免登 通过no实际跳转WebLoginUrl:" + loginurl);


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 通过临时授权码获取授权用户的个人信息
        DefaultDingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest reqBycodeRequest = new OapiSnsGetuserinfoBycodeRequest();

        reqBycodeRequest.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse bycodeResponse = client2.execute(reqBycodeRequest, AccessTokenUtil.AppKey, AccessTokenUtil.AppSecret);

        // 根据unionid获取钉钉的userId
        String unionid = bycodeResponse.getUserInfo().getUnionid();
        DingTalkClient clientDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest reqGetbyunionidRequest = new OapiUserGetbyunionidRequest();
        reqGetbyunionidRequest.setUnionid(unionid);
        OapiUserGetbyunionidResponse oapiUserGetbyunionidResponse = clientDingTalkClient.execute(reqGetbyunionidRequest, access_token);

        // 根据获取用户钉钉的userId,即PiJustAuth.AuthUuid
        String dingUserid = oapiUserGetbyunionidResponse.getResult().getUserid();

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        SaUserPojo userPojo = saUserService.getEntityByDingUserid(dingUserid);
        if (userPojo == null) {
            throw new BaseBusinessException("当前钉钉未绑定账号,请联系管理员");
        }

        // 登录并获取token
        Map<String, Object> tokenMap = saUserService.loginCheck(userPojo.getUsername(), userPojo.getPassword(), request, null);
        String accessToken = tokenMap.get("access_token").toString();

        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", accessToken);
        PrintColor.red("跳转免登 WebLoginUrl:" + loginurl);
        response.sendRedirect(loginurl);
    }

    @ApiOperation(value = "读取LoginUser用户信息 key为token", notes = "读取LoginUser解析用户信息", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key, HttpServletRequest request) { //key为 token
        try {
            //解析用户信息
            LoginUser loginUser = this.saRedisService.getLoginUserFromToken(key);
            if (loginUser == null) {
                logger.info("key（token）已过期失效：" + key);
                return R.fail("key已过期失效");
            }
            logger.info("Loginuser：" + loginUser);
            //删除缓存用户信息
            saRedisService.deleteObject("login_tokens:" + key);
            //解密
            String decryptPassword = SaUserServiceImpl.Decrypt(loginUser.getPassword());
            Map<String, Object> tokenMap = saUserService.loginCheck(loginUser.getUsername(), decryptPassword, request, null);
            logger.info("免登最终token：" + tokenMap.get("access_token").toString());
            //重新存入redis
            Map<String, Object> map = tokenMap;   //tokenService.createToken(loginUser);
            return R.ok(map);
        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }


}
