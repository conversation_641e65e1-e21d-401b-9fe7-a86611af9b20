package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 预警用户(SaWarninguser)实体类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Data
public class SaWarninguserEntity implements Serializable {
    private static final long serialVersionUID = -93433936919333003L;
     // id
    private String id;
     // 预警id
    private String warnid;
     // 时间差
    private Integer diffnum;
     // 行号
    private Integer rownum;
     // 摘要
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 用户id
    private String userid;
     // 姓名
    private String realname;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

