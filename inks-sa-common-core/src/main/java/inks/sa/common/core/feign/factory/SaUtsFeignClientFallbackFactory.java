package inks.sa.common.core.feign.factory;

import inks.common.core.domain.R;
import inks.sa.common.core.feign.SaUtsFeignClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

// FallbackFactory 实现
@Component
class SaUtsFeignClientFallbackFactory implements FallbackFactory<SaUtsFeignClient> {

    @Override
    public SaUtsFeignClient create(Throwable cause) {
        return new SaUtsFeignClient() {

            @Override
            public R wxeapprovel(String key, String tid) {
                // 返回降级的结果，比如可以返回一个默认的失败响应
                return R.fail("sa-uts微信审批服务不可用: " + cause.getMessage());
            }

            @Override
            public R dingapprovel(String key, String tid) {
                // 返回降级的结果
                return R.fail("sa-uts钉钉审批服务不可用: " + cause.getMessage());
            }
        };
    }
}