package inks.sa.common.core.config.flyway;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * Flyway进度WebSocket处理器
 */
@Component
public class FlywayProgressWebSocketHandler extends TextWebSocketHandler {

    private static final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        System.out.println("WebSocket连接建立: " + session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        sessions.remove(session);
        System.out.println("WebSocket连接关闭: " + session.getId());
    }

    /**
     * 发送进度消息到所有连接的客户端
     */
    public static void sendProgress(String type, String message, int progress) {
        Map<String, Object> data = new HashMap<>();
        data.put("type", type);
        data.put("message", message);
        data.put("progress", progress);
        data.put("timestamp", System.currentTimeMillis());

        try {
            String json = objectMapper.writeValueAsString(data);
            TextMessage textMessage = new TextMessage(json);

            for (WebSocketSession session : sessions) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(textMessage);
                    } catch (IOException e) {
                        System.err.println("发送WebSocket消息失败: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("序列化进度消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送日志消息
     */
    public static void sendLog(String message) {
        sendProgress("log", message, -1);
    }

    /**
     * 发送进度更新
     */
    public static void sendProgressUpdate(String message, int progress) {
        sendProgress("progress", message, progress);
    }

    /**
     * 发送完成消息
     */
    public static void sendComplete(String message) {
        sendProgress("complete", message, 100);
    }

    /**
     * 发送错误消息
     */
    public static void sendError(String message) {
        sendProgress("error", message, -1);
    }
}
