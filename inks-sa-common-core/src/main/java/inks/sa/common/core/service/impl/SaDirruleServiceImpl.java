package inks.sa.common.core.service.impl;

import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.domain.pojo.SaDirrulePojo;
import inks.sa.common.core.domain.SaDirruleEntity;
import inks.sa.common.core.mapper.SaDirruleMapper;
import inks.sa.common.core.service.SaDirruleService;
import inks.sa.common.core.service.SaRedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 目录规则配置表(SaDirrule)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 14:21:06
 */
@Service("saDirruleService")
public class SaDirruleServiceImpl implements SaDirruleService {
    @Resource
    private SaDirruleMapper saDirruleMapper;

    @Resource
    private SaRedisService saRedisService;

    @Override
    public SaDirrulePojo getEntity(String key) {
        return this.saDirruleMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDirrulePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDirrulePojo> lst = saDirruleMapper.getPageList(queryParam);
            PageInfo<SaDirrulePojo> pageInfo = new PageInfo<SaDirrulePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaDirrulePojo insert(SaDirrulePojo saDirrulePojo) {
        //初始化NULL字段
        cleanNull(saDirrulePojo);
        SaDirruleEntity saDirruleEntity = new SaDirruleEntity(); 
        BeanUtils.copyProperties(saDirrulePojo,saDirruleEntity);
        //生成雪花id
          saDirruleEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDirruleEntity.setRevision(1);  //乐观锁
          this.saDirruleMapper.insert(saDirruleEntity);
        // 同步刷新redis缓存的所有目录规则列表DirRuleList (系统管理员才能设置：默认租户)
        saRedisService.setCacheObject(MyConstant.DIR_RULE_LIST, saDirruleMapper.getList(),300, TimeUnit.DAYS);
        return this.getEntity(saDirruleEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saDirrulePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDirrulePojo update(SaDirrulePojo saDirrulePojo) {
        SaDirruleEntity saDirruleEntity = new SaDirruleEntity(); 
        BeanUtils.copyProperties(saDirrulePojo,saDirruleEntity);
        this.saDirruleMapper.update(saDirruleEntity);
        // 同步刷新redis缓存的所有目录规则列表DirRuleList (系统管理员才能设置：默认租户)
        saRedisService.setCacheObject(MyConstant.DIR_RULE_LIST, saDirruleMapper.getList(),300, TimeUnit.DAYS);
        return this.getEntity(saDirruleEntity.getId());
    }


    @Override
    public int delete(String key) {
        int delete = this.saDirruleMapper.delete(key);
        // 同步刷新redis缓存的所有目录规则列表DirRuleList (系统管理员才能设置：默认租户)
        saRedisService.setCacheObject(MyConstant.DIR_RULE_LIST, saDirruleMapper.getList(),300, TimeUnit.DAYS);
        return delete;
    }
    

    private static void cleanNull(SaDirrulePojo saDirrulePojo) {
        if(saDirrulePojo.getDirname()==null) saDirrulePojo.setDirname("");
        if(saDirrulePojo.getDescription()==null) saDirrulePojo.setDescription("");
        if(saDirrulePojo.getPublicmark()==null) saDirrulePojo.setPublicmark(0);
        if(saDirrulePojo.getBlackuserids()==null) saDirrulePojo.setBlackuserids("");
        if(saDirrulePojo.getBlackusernames()==null) saDirrulePojo.setBlackusernames("");
        if(saDirrulePojo.getWhiteuserids()==null) saDirrulePojo.setWhiteuserids("");
        if(saDirrulePojo.getWhiteusernames()==null) saDirrulePojo.setWhiteusernames("");
        if(saDirrulePojo.getRownum()==null) saDirrulePojo.setRownum(0);
        if(saDirrulePojo.getRemark()==null) saDirrulePojo.setRemark("");
        if(saDirrulePojo.getCreateby()==null) saDirrulePojo.setCreateby("");
        if(saDirrulePojo.getCreatebyid()==null) saDirrulePojo.setCreatebyid("");
        if(saDirrulePojo.getCreatedate()==null) saDirrulePojo.setCreatedate(new Date());
        if(saDirrulePojo.getLister()==null) saDirrulePojo.setLister("");
        if(saDirrulePojo.getListerid()==null) saDirrulePojo.setListerid("");
        if(saDirrulePojo.getModifydate()==null) saDirrulePojo.setModifydate(new Date());
        if(saDirrulePojo.getModulecode()==null) saDirrulePojo.setModulecode("");
        if(saDirrulePojo.getCustom1()==null) saDirrulePojo.setCustom1("");
        if(saDirrulePojo.getCustom2()==null) saDirrulePojo.setCustom2("");
        if(saDirrulePojo.getCustom3()==null) saDirrulePojo.setCustom3("");
        if(saDirrulePojo.getCustom4()==null) saDirrulePojo.setCustom4("");
        if(saDirrulePojo.getCustom5()==null) saDirrulePojo.setCustom5("");
        if(saDirrulePojo.getCustom6()==null) saDirrulePojo.setCustom6("");
        if(saDirrulePojo.getCustom7()==null) saDirrulePojo.setCustom7("");
        if(saDirrulePojo.getCustom8()==null) saDirrulePojo.setCustom8("");
        if(saDirrulePojo.getCustom9()==null) saDirrulePojo.setCustom9("");
        if(saDirrulePojo.getCustom10()==null) saDirrulePojo.setCustom10("");
        if(saDirrulePojo.getDeptid()==null) saDirrulePojo.setDeptid("");
        if(saDirrulePojo.getTenantid()==null) saDirrulePojo.setTenantid("");
        if(saDirrulePojo.getTenantname()==null) saDirrulePojo.setTenantname("");
        if(saDirrulePojo.getRevision()==null) saDirrulePojo.setRevision(0);
   }

}
