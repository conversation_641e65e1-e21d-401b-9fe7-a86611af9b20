package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.service.SaFilelogService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 文件上传/下载日志表(Sa_FileLog)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-17 14:22:54
 */

@RestController
@RequestMapping("SaFileLog")
@Api(tags = "通用:文件上传/下载日志")
public class A_SaFilelogController {
    @Resource
    private SaFilelogService saFilelogService;

    @Resource
    private SaRedisService saRedisService;

    private final static Logger logger = LoggerFactory.getLogger(A_SaFilelogController.class);


    @ApiOperation(value = "按条件分页查询 未使用过的僵尸文件Uts_FileLog.UsedMark=0", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FileLog.List")
    public R<PageInfo<SaFilelogPojo>> getOnlinePageList(@RequestBody String json, String module) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_FileLog.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Uts_FileLog.UsedMark=0";
            if (module != null) {
                qpfilter += " and Uts_FileLog.Module='" + module + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFilelogService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取文件上传/下载日志表详细信息", notes = "获取文件上传/下载日志表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FileLog.List")
    public R<SaFilelogPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFilelogService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FileLog.List")
    public R<PageInfo<SaFilelogPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_FileLog.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFilelogService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增文件上传/下载日志表", notes = "新增文件上传/下载日志表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FileLog.Add")
    public R<SaFilelogPojo> create(@RequestBody String json) {
        try {
            SaFilelogPojo saFilelogPojo = JSONArray.parseObject(json, SaFilelogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFilelogPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFilelogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFilelogPojo.setCreatedate(new Date());   // 创建时间
            saFilelogPojo.setLister(loginUser.getRealname());   // 制表
            saFilelogPojo.setListerid(loginUser.getUserid());    // 制表id
            saFilelogPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFilelogService.insert(saFilelogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改文件上传/下载日志表", notes = "修改文件上传/下载日志表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FileLog.Edit")
    public R<SaFilelogPojo> update(@RequestBody String json) {
        try {
            SaFilelogPojo saFilelogPojo = JSONArray.parseObject(json, SaFilelogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFilelogPojo.setLister(loginUser.getRealname());   // 制表
            saFilelogPojo.setListerid(loginUser.getUserid());    // 制表id
            saFilelogPojo.setModifydate(new Date());   //修改时间
            //            saFilelogPojo.setAssessor(""); // 审核员
            //            saFilelogPojo.setAssessorid(""); // 审核员id
            //            saFilelogPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFilelogService.update(saFilelogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除文件上传/下载日志表", notes = "删除文件上传/下载日志表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FileLog.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFilelogService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FileLog.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFilelogPojo saFilelogPojo = this.saFilelogService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFilelogPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

