package inks.sa.common.core.domain.vo;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * 审批发起的请求DTO
 */
// 用于替代 String json，使代码类型安全、易于维护和校验。
public class ApprovalRequestDto {
    // 单据id
    private String key;
    private String apprid;
    private String type;
    private String remark;
    private JsonNode process; // 改为JsonNode类型以支持对象和字符串

    // --- Getters and Setters ---
    public String getKey() { return key; }
    public void setKey(String key) { this.key = key; }
    public String getApprid() { return apprid; }
    public void setApprid(String apprid) { this.apprid = apprid; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    public JsonNode getProcess() { return process; }
    public void setProcess(JsonNode process) { this.process = process; }
}