package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaRolemenuappEntity;
import inks.sa.common.core.domain.pojo.SaRolemenuappPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单App(SaRolemenuapp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Mapper
public interface SaRolemenuappMapper {


    SaRolemenuappPojo getEntity(@Param("key") String key);

    List<SaRolemenuappPojo> getPageList(QueryParam queryParam);

    int insert(SaRolemenuappEntity saRolemenuappEntity);

    int update(SaRolemenuappEntity saRolemenuappEntity);

    int delete(@Param("key") String key);

    List<String> getNavidsByUserid(String userid);

    List<String> getNavidsByRoleid(String roleid);

    Integer batchDelete(@Param("roleid") String roleid, @Param("deleteNavids") List<String> deleteNavids);

    Integer batchInsert(@Param("rolemenuappPojoList") List<SaRolemenuappPojo> rolemenuappPojoList);
}

