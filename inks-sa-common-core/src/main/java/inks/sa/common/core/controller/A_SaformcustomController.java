package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.pojo.SaformcustomPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaformcustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 自定义界面(Sa_FormCustom)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-09 10:48:38
 */
@RestController
@RequestMapping("SaFormCustom")
@Api(tags = "通用:自定义界面")
public class A_SaformcustomController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaformcustomController.class);

    @Resource
    private SaformcustomService saformcustomService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取自定义界面详细信息 ByCode", notes = "获取自定义界面详细信息 ByCode", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    public R<SaformcustomPojo> getEntityByCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saformcustomService.getEntityByCode(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取自定义界面详细信息", notes = "获取自定义界面详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FormCustom.List")
    public R<SaformcustomPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saformcustomService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FormCustom.List")
    public R<PageInfo<SaformcustomPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_FormCustom.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saformcustomService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增自定义界面", notes = "新增自定义界面", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FormCustom.Add")
    public R<SaformcustomPojo> create(@RequestBody String json) {
        try {
            SaformcustomPojo saformcustomPojo = JSONArray.parseObject(json, SaformcustomPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saformcustomPojo.setCreateby(loginUser.getRealName());   // 创建者
            saformcustomPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saformcustomPojo.setCreatedate(new Date());   // 创建时间
            saformcustomPojo.setLister(loginUser.getRealname());   // 制表
            saformcustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            saformcustomPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saformcustomService.insert(saformcustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改自定义界面", notes = "修改自定义界面", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FormCustom.Edit")
    public R<SaformcustomPojo> update(@RequestBody String json) {
        try {
            SaformcustomPojo saformcustomPojo = JSONArray.parseObject(json, SaformcustomPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saformcustomPojo.setLister(loginUser.getRealname());   // 制表
            saformcustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            saformcustomPojo.setModifydate(new Date());   //修改时间
//            saformcustomPojo.setAssessor(""); // 审核员
//            saformcustomPojo.setAssessorid(""); // 审核员id
//            saformcustomPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saformcustomService.update(saformcustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除自定义界面", notes = "删除自定义界面", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FormCustom.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saformcustomService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
// //@PreAuthorize(hasPermi = "Sa_FormCustom.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//        // 获得用户数据
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        SaformcustomPojo saformcustomPojo = this.saformcustomService.getEntity(key);
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(saformcustomPojo);
//                // 加入公司信息
//        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
//      //从redis中获取Reprot内容
//     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//     String content ;
//     if (reportsPojo != null ) {
//         content = reportsPojo.getRptdata();
//     } else {
//         throw new BaseBusinessException("未找到报表");
//     }
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response= ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
}

