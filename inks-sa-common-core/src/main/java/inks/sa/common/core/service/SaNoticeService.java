package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaNoticePojo;

/**
 * (SaNotice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
public interface SaNoticeService {


    SaNoticePojo getEntity(String key);

    PageInfo<SaNoticePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saNoticePojo 实例对象
     * @return 实例对象
     */
    SaNoticePojo insert(SaNoticePojo saNoticePojo);

    /**
     * 修改数据
     *
     * @param saNoticepojo 实例对象
     * @return 实例对象
     */
    SaNoticePojo update(SaNoticePojo saNoticepojo);

    int delete(String key);
}
