package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaDeptEntity;
import inks.sa.common.core.domain.pojo.SaDeptPojo;
import inks.sa.common.core.domain.pojo.SaDeptuserPojo;
import inks.sa.common.core.mapper.SaDeptMapper;
import inks.sa.common.core.mapper.SaDeptuserMapper;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.service.SaDeptService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用户组织架构(SaDept)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-05 12:36:30
 */
@Service("saDeptService")
public class SaDeptServiceImpl implements SaDeptService {
    @Resource
    private SaDeptMapper saDeptMapper;

    @Resource
    private SaDeptuserMapper saDeptuserMapper;
    @Resource
    private SaUserMapper saUserMapper;
    @Override
    public SaDeptPojo getEntity(String key) {
        return this.saDeptMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDeptPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDeptPojo> lst = saDeptMapper.getPageList(queryParam);
            PageInfo<SaDeptPojo> pageInfo = new PageInfo<SaDeptPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaDeptPojo insert(SaDeptPojo saDeptPojo) {
        //初始化NULL字段
        cleanNull(saDeptPojo);
        SaDeptEntity saDeptEntity = new SaDeptEntity();
        BeanUtils.copyProperties(saDeptPojo, saDeptEntity);
        //生成雪花id
        saDeptEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDeptEntity.setRevision(1);  //乐观锁
        this.saDeptMapper.insert(saDeptEntity);
        return this.getEntity(saDeptEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDeptPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDeptPojo update(SaDeptPojo saDeptPojo) {
        SaDeptEntity saDeptEntity = new SaDeptEntity();
        BeanUtils.copyProperties(saDeptPojo, saDeptEntity);
        this.saDeptMapper.update(saDeptEntity);
        return this.getEntity(saDeptEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDeptMapper.delete(key);
    }


    private static void cleanNull(SaDeptPojo saDeptPojo) {
        if (saDeptPojo.getParentid() == null) saDeptPojo.setParentid("");
        if (saDeptPojo.getAncestors() == null) saDeptPojo.setAncestors("");
        if (saDeptPojo.getDeptcode() == null) saDeptPojo.setDeptcode("");
        if (saDeptPojo.getDeptname() == null) saDeptPojo.setDeptname("");
        if (saDeptPojo.getEnabledmark() == null) saDeptPojo.setEnabledmark(0);
        if (saDeptPojo.getLeader() == null) saDeptPojo.setLeader("");
        if (saDeptPojo.getPhone() == null) saDeptPojo.setPhone("");
        if (saDeptPojo.getEmail() == null) saDeptPojo.setEmail("");
        if (saDeptPojo.getRownum() == null) saDeptPojo.setRownum(0);
        if (saDeptPojo.getRemark() == null) saDeptPojo.setRemark("");
        if (saDeptPojo.getCreateby() == null) saDeptPojo.setCreateby("");
        if (saDeptPojo.getCreatebyid() == null) saDeptPojo.setCreatebyid("");
        if (saDeptPojo.getCreatedate() == null) saDeptPojo.setCreatedate(new Date());
        if (saDeptPojo.getLister() == null) saDeptPojo.setLister("");
        if (saDeptPojo.getListerid() == null) saDeptPojo.setListerid("");
        if (saDeptPojo.getModifydate() == null) saDeptPojo.setModifydate(new Date());
        if (saDeptPojo.getCustom1() == null) saDeptPojo.setCustom1("");
        if (saDeptPojo.getCustom2() == null) saDeptPojo.setCustom2("");
        if (saDeptPojo.getCustom3() == null) saDeptPojo.setCustom3("");
        if (saDeptPojo.getCustom4() == null) saDeptPojo.setCustom4("");
        if (saDeptPojo.getCustom5() == null) saDeptPojo.setCustom5("");
        if (saDeptPojo.getTenantid() == null) saDeptPojo.setTenantid("");
        if (saDeptPojo.getRevision() == null) saDeptPojo.setRevision(0);
    }

    @Override
    public List<DeptinfoPojo> getDeptinfoList(String userid) {
        List<DeptinfoPojo> lst = new ArrayList<>();
        SaDeptuserPojo saDeptuserDB = this.saDeptuserMapper.getEntityByUser(userid);
        if (saDeptuserDB == null || saDeptuserDB.getDeptid() == null) {
            return null;
        } else {
            DeptinfoPojo deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(saDeptuserDB, deptinfoPojo);
            deptinfoPojo.setIsdeptadmin(saDeptuserDB.getIsadmin());
            lst.add(deptinfoPojo);
            this.getSubinfoAll(lst);
            return lst;
        }
    }

    @Override
    public List<SaDeptPojo> getListByParentid(String key) {
        return this.saDeptMapper.getListByParentid(key);
    }

    @Override
    public void getSubinfoAll(List<DeptinfoPojo> lst) {
        getDetailitem(lst, lst.get(0).getDeptid(), 1);
    }

    private void getDetailitem(List<DeptinfoPojo> lstinfo, String key, Integer level) {
        DeptinfoPojo deptinfoPojo;
        List<SaDeptPojo> lst = getListByParentid(key);
        for (SaDeptPojo pojo : lst) {
            deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(pojo, deptinfoPojo);
            deptinfoPojo.setDeptid(pojo.getId());
            deptinfoPojo.setIsdeptadmin(0);
            lstinfo.add(deptinfoPojo);
            if (level < 10) {
                getDetailitem(lstinfo, pojo.getId(), level + 1);
            }
        }
    }

    /**
     * @Description 注意 getDeptinfolistByUser方法 即使当前用户不是部门管理员,也会返回当前部门和所有下级部门信息,只不过部门信息的isdeptadmin字段都=0,
     *                  且如果当前用户未关联部门,则返回null
     */
    @Override
    public List<DeptinfoPojo> getDeptinfolistByUser(String userid) {
        List<DeptinfoPojo> lst = new ArrayList<>();
        SaDeptuserPojo deptuserPojo = null;
        // 如果是超级管理员，返回顶级部门(即Sa_Dept.Parentid=root")
        if (saUserMapper.getAdminMarkByUserid(userid) == 2) {
            deptuserPojo = saDeptuserMapper.getEntityBySuperAdmin();
        } else {// 一个user在Sa_DeptUser表里只能关联一个部门
            deptuserPojo = saDeptuserMapper.getEntityByUser(userid);
        }
        if (deptuserPojo == null || deptuserPojo.getDeptid() == null) {
//            throw new BaseBusinessException("用户未关联部门");
            return null;
        } else {
            DeptinfoPojo deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(deptuserPojo, deptinfoPojo);
            deptinfoPojo.setIsdeptadmin(deptuserPojo.getIsadmin());
            lst.add(deptinfoPojo);
            //查询部门下所有子部门
            this.getSubinfoAll(lst);
            return lst;
        }
    }
}
