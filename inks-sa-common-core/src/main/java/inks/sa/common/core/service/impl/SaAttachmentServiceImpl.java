package inks.sa.common.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.config.oss.service.FileController;
import inks.sa.common.core.domain.SaAttachmentEntity;
import inks.sa.common.core.domain.pojo.SaAttachmentPojo;
import inks.sa.common.core.mapper.SaAttachmentMapper;
import inks.sa.common.core.service.SaAttachmentService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 附件中心(SaAttachment)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-29 13:26:54
 */
@Service("saAttachmentService")
public class SaAttachmentServiceImpl implements SaAttachmentService {
    @Resource
    private SaAttachmentMapper saAttachmentMapper;
    @Resource
    private FileController fileController;


    @Override
    public SaAttachmentPojo getEntity(String key) {
        return this.saAttachmentMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaAttachmentPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaAttachmentPojo> lst = saAttachmentMapper.getPageList(queryParam);
            PageInfo<SaAttachmentPojo> pageInfo = new PageInfo<SaAttachmentPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaAttachmentPojo insert(SaAttachmentPojo saAttachmentPojo) {
        //初始化NULL字段
        cleanNull(saAttachmentPojo);
        SaAttachmentEntity saAttachmentEntity = new SaAttachmentEntity();
        BeanUtils.copyProperties(saAttachmentPojo, saAttachmentEntity);
        //生成雪花id
        saAttachmentEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saAttachmentEntity.setRevision(1);  //乐观锁
        this.saAttachmentMapper.insert(saAttachmentEntity);
        return this.getEntity(saAttachmentEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saAttachmentPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaAttachmentPojo update(SaAttachmentPojo saAttachmentPojo) {
        SaAttachmentEntity saAttachmentEntity = new SaAttachmentEntity();
        BeanUtils.copyProperties(saAttachmentPojo, saAttachmentEntity);
        this.saAttachmentMapper.update(saAttachmentEntity);
        return this.getEntity(saAttachmentEntity.getId());
    }


    @Override
    public int delete(String key, LoginUser loginUser) {
        try {
            SaAttachmentPojo saAttachmentPojo = this.saAttachmentMapper.getEntity(key);
            if (saAttachmentPojo == null) {
                throw new BaseBusinessException("未找到记录");
            }
            fileController.remove(saAttachmentPojo.getDirname()+"/"+saAttachmentPojo.getFilename());
            return this.saAttachmentMapper.delete(key);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    private static void cleanNull(SaAttachmentPojo saAttachmentPojo) {
        if (saAttachmentPojo.getGengroupid() == null) saAttachmentPojo.setGengroupid("");
        if (saAttachmentPojo.getFileoriname() == null) saAttachmentPojo.setFileoriname("");
        if (saAttachmentPojo.getBucketname() == null) saAttachmentPojo.setBucketname("");
        if (saAttachmentPojo.getDirname() == null) saAttachmentPojo.setDirname("");
        if (saAttachmentPojo.getFilename() == null) saAttachmentPojo.setFilename("");
        if (saAttachmentPojo.getContenttype() == null) saAttachmentPojo.setContenttype("");
        if (saAttachmentPojo.getFilesuffix() == null) saAttachmentPojo.setFilesuffix("");
        if (saAttachmentPojo.getStorage() == null) saAttachmentPojo.setStorage("");
        if (saAttachmentPojo.getRelateid() == null) saAttachmentPojo.setRelateid("");
        if (saAttachmentPojo.getFileurl() == null) saAttachmentPojo.setFileurl("");
        if (saAttachmentPojo.getEnabledmark() == null) saAttachmentPojo.setEnabledmark(0);
        if (saAttachmentPojo.getRownum() == null) saAttachmentPojo.setRownum(0);
        if (saAttachmentPojo.getRemark() == null) saAttachmentPojo.setRemark("");
        if (saAttachmentPojo.getCreateby() == null) saAttachmentPojo.setCreateby("");
        if (saAttachmentPojo.getCreatebyid() == null) saAttachmentPojo.setCreatebyid("");
        if (saAttachmentPojo.getCreatedate() == null) saAttachmentPojo.setCreatedate(new Date());
        if (saAttachmentPojo.getLister() == null) saAttachmentPojo.setLister("");
        if (saAttachmentPojo.getListerid() == null) saAttachmentPojo.setListerid("");
        if (saAttachmentPojo.getModifydate() == null) saAttachmentPojo.setModifydate(new Date());
        if (saAttachmentPojo.getModulecode() == null) saAttachmentPojo.setModulecode("");
        if (saAttachmentPojo.getCustom1() == null) saAttachmentPojo.setCustom1("");
        if (saAttachmentPojo.getCustom2() == null) saAttachmentPojo.setCustom2("");
        if (saAttachmentPojo.getCustom3() == null) saAttachmentPojo.setCustom3("");
        if (saAttachmentPojo.getCustom4() == null) saAttachmentPojo.setCustom4("");
        if (saAttachmentPojo.getCustom5() == null) saAttachmentPojo.setCustom5("");
        if (saAttachmentPojo.getCustom6() == null) saAttachmentPojo.setCustom6("");
        if (saAttachmentPojo.getCustom7() == null) saAttachmentPojo.setCustom7("");
        if (saAttachmentPojo.getCustom8() == null) saAttachmentPojo.setCustom8("");
        if (saAttachmentPojo.getCustom9() == null) saAttachmentPojo.setCustom9("");
        if (saAttachmentPojo.getCustom10() == null) saAttachmentPojo.setCustom10("");
        if (saAttachmentPojo.getDeptid() == null) saAttachmentPojo.setDeptid("");
        if (saAttachmentPojo.getTenantid() == null) saAttachmentPojo.setTenantid("");
        if (saAttachmentPojo.getTenantname() == null) saAttachmentPojo.setTenantname("");
        if (saAttachmentPojo.getRevision() == null) saAttachmentPojo.setRevision(0);
    }



}


