package inks.sa.common.core.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaValidatorPojo;
import inks.sa.common.core.domain.SaValidatorEntity;

import com.github.pagehelper.PageInfo;
import inks.sa.common.core.domain.vo.ValidationResponse;

import java.util.Map;

/**
 * 数据验证(SaValidator)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-18 14:23:32
 */
public interface SaValidatorService {


    SaValidatorPojo getEntity(String key);

    PageInfo<SaValidatorPojo> getPageList(QueryParam queryParam);

    SaValidatorPojo insert(SaValidatorPojo saValidatorPojo);

    SaValidatorPojo update(SaValidatorPojo saValidatorpojo);

    int delete(String key);

    ValidationResponse validate(String valicode, Map<String, Object> dataObj, LoginUser loginUser);
}
