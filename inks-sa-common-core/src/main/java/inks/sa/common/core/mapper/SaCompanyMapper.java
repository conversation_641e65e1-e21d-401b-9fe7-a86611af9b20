package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaCompanyEntity;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司信息表(SaCompany)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-22 12:48:49
 */
@Mapper
public interface SaCompanyMapper {


    SaCompanyPojo getEntity(@Param("key") String key);

    List<SaCompanyPojo> getPageList(QueryParam queryParam);

    int insert(SaCompanyEntity saCompanyEntity);

    int update(SaCompanyEntity saCompanyEntity);


    int delete(@Param("key") String key);

}

