package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaDeptuserPojo;
import inks.sa.common.core.service.SaDeptuserService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 组织用户表(Sa_DeptUser)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-05 13:00:44
 */
@RestController
@RequestMapping("SaDeptUser")
@Api(tags = "通用:部门用户(组织架构）")
public class A_SaDeptuserController {
    @Resource
    private SaDeptuserService saDeptuserService;

    @Resource
    private SaRedisService saRedisService;

    private final static Logger logger = LoggerFactory.getLogger(A_SaDeptuserController.class);


    @ApiOperation(value = " 获取组织用户表详细信息", notes = "获取组织用户表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.List")
    public R<SaDeptuserPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saDeptuserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.List")
    public R<PageInfo<SaDeptuserPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DeptUser.CreateDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDeptuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增组织用户表", notes = "新增组织用户表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.Add")
    public R<SaDeptuserPojo> create(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaDeptuserPojo saDeptuserPojo = JSONArray.parseObject(json, SaDeptuserPojo.class);
            saDeptuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDeptuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDeptuserPojo.setCreatedate(new Date());   // 创建时间
            saDeptuserPojo.setLister(loginUser.getRealname());   // 制表
            saDeptuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saDeptuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDeptuserService.insert(saDeptuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改组织用户表", notes = "修改组织用户表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.Edit")
    public R<SaDeptuserPojo> update(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaDeptuserPojo saDeptuserPojo = JSONArray.parseObject(json, SaDeptuserPojo.class);
            saDeptuserPojo.setLister(loginUser.getRealname());   // 制表
            saDeptuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saDeptuserPojo.setModifydate(new Date());   //修改时间
            //            saDeptuserPojo.setAssessor(""); // 审核员
            //            saDeptuserPojo.setAssessorid(""); // 审核员id
            //            saDeptuserPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDeptuserService.update(saDeptuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除组织用户表", notes = "删除组织用户表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saDeptuserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DeptUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaDeptuserPojo saDeptuserPojo = this.saDeptuserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDeptuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

