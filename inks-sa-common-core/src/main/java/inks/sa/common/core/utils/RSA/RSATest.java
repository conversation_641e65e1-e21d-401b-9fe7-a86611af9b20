package inks.sa.common.core.utils.RSA;


import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.RsaKey;
import inks.common.core.utils.RSAUtils;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Description RSA使用示例
 * @time 2023/11/17 10:22
 */
public class RSATest {


    // 已经有了公钥和私钥字符串: 直接使用加密解密 (没有公钥和私钥字符串,需先生成公钥和私钥字符串(见下面另一个方法))
    public static void main(String[] args) {
        try {
            // 公钥,私钥字符串
            String publicKeyStr = MyRSA.PUBLIC_KEY;
            String privateKeyStr = MyRSA.PRIVATE_KEY;
            // 待加密的文本 {"sn":"init222",lt:4070908800000 } 失效时间2099年
            String plainText = "{\"sn\":\"init222\",\"lt\":4070908800000}";
            // 使用My公钥加密
            String encryptedText = RSAEncryptor.encrypt(plainText, publicKeyStr);
            System.out.println("My加密后的文本: " + encryptedText);
            // 使用Core公钥加密
            String encrypt = RSAUtils.encrypt(plainText, RsaKey.PUBLIC_KEY);
            System.out.println("core包加密后的文本: " + encrypt);
            // 使用My私钥解密My
            String decryptedText = RSADecryptor.decrypt(encryptedText, privateKeyStr);
            System.out.println("My解密后的文本: " + decryptedText);
            // 使用My私钥解密Core
            String decrypt = decrypt(encrypt, privateKeyStr);
            System.out.println("使用My私钥解密Core: " + decrypt);

            // 使用Core私钥解密My
            //String sn = "unoi1bGkas9ekriMvdlriy434ANuViFCgIyu0LiEV1fx92G9FCu8QbsNcQWc35wJz/qVVpDLSTI1teAIBfG0005RtAg8YUOBndXLGLxAF/MjBaIKhQHDPSS1fy8zn5HCFaZrS1tyvY4a+6dTprkTkOy3yUIKbx8n2Ejd/KctzMGClwZqQVVkEuQNzyDJmbW9QunTkmlQjXkIRdiKRA21Jktv/1lBXoSqgvG9T9kc22SlCXVRaD5LoaktcLD4PbWPl91K3djeK7zguFYGPdQPacwtnfk1TnhsPwDAh+cOWOKtaF4oNeW/j5cwotWvX+Y/TS8N2m+8HTj5hIZWOq6mUw==";
            //String sn = "vD8aBunT02WtAYZkIBTrVuzQhJtiQINh/BGy4mmVlBzqWODyqAEHAR/baQXVOIiDBysegM0LFu7/J1ZHLRkOsXE3kSroDowMSrlHKkBdiPb2vRxu+CmQJaPkhXeAmciNuflxZGSb9i9eGsz1XFNZ1DIepP8En0N4Eah9FAPLOjLr2/IKYQbtA//bNj3JjjNA1BYknvbBaWZkqodN91bSJBxaSsn6pke3NMrFdKKpnopk+oWIqq+gLuLjlfLorRsoziht8iCgQ2xjnkor+HBAHGR2cM8eZ62DFA1N3Jen6wq7DZ16QR2e1theqN6hLsiYydwWPisRXsXakrT2v0fe8g==";
            String sn = "Pwx4L16D1TNkS/PowyEJIyigGR6iniaSLOUvjOAwAG+wK/11mDdupDQMjSwypdWp7HCtewGkPvpTuT1vPknc3msYxr4c/gRQHTbxxZMzjXr3OCOu++vHBawihScogF5ODfrtsKEXpL0gLDiRNjjoeF3LYcVzHP5+eUG38LfVBxypC+sJXIrLMiX/xWiB9rHAMEZah/Rf3H1mnZREqBDUvKPmcsHYlDucmnphgsoE+Ay4kkLN6f9IM5EMmDEpXRLZ0kYn71OBxvpIB3DINNX458sYw3UehMV+fJIhac5lkQXQ5lNQvuQokE1C+wzi+X3axhiDBwFmB34OsqZc/5L00Q==";
            String decrypt2 = RSAUtils.decrypt(sn, RsaKey.PRIVATE_KEY);
            System.out.println("使用Core私钥解密My: " + decrypt2);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    还没有公钥和私钥字符串: 先生成公钥和私钥字符串,再使用加密解密
//    public static void main(String[] args) throws NoSuchAlgorithmException {
//        // 每次都随机生成密钥对
//        KeyPair keyPair = RSAKeyGenerator.generateKeyPair();
//        PublicKey publicKey = keyPair.getPublic();
//        PrivateKey privateKey = keyPair.getPrivate();
//        // 显示生成的公钥和私钥
//        System.out.println("公钥: " + publicKey);//都是数字
//        System.out.println("私钥: " + privateKey);
//        // PublicKey,PrivateKey转String
//        String publicKeyStr = Base64.getEncoder().encodeToString(publicKey.getEncoded());
//        String privateKeyStr = Base64.getEncoder().encodeToString(privateKey.getEncoded());
//        System.out.println("privateKeyStr = " + privateKeyStr);//都是数字+英文
//        System.out.println("publicKeyStr = " + publicKeyStr);
//    }


    public static String encrypt(PublicKey publicKey, String plainText) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(1, publicKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return new String(encryptedBytes);
    }

    public static String decrypt(PrivateKey privateKey, String encryptedText) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(2, privateKey);
        byte[] decryptedBytes = cipher.doFinal(encryptedText.getBytes());
        return new String(decryptedBytes);
    }

        // 使用公钥字符串进行加密
        public static String encrypt(String plainText, String publicKeyStr) throws Exception {
            // 将公钥字符串转换为PublicKey对象
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 加密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        }

        // 使用私钥字符串进行解密
        public static String decrypt(String encryptedText, String privateKeyStr) throws Exception {
            // 将私钥字符串转换为PrivateKey对象
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            // 解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decryptedBytes);
        }

}
