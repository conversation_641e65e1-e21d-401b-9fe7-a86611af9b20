package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SadgformatPojo;
import inks.sa.common.core.domain.pojo.SadgformatitemdetailPojo;

/**
 * 列表格式(Sadgformat)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:44
 */
public interface SadgformatService {


    SadgformatPojo getEntity(String key, String tid);

    PageInfo<SadgformatitemdetailPojo> getPageList(QueryParam queryParam);


    SadgformatPojo getBillEntity(String key, String tid);

    PageInfo<SadgformatPojo> getBillList(QueryParam queryParam);

    PageInfo<SadgformatPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param sadgformatPojo 实例对象
     * @return 实例对象
     */
    SadgformatPojo insert(SadgformatPojo sadgformatPojo);

    /**
     * 修改数据
     *
     * @param sadgformatpojo 实例对象
     * @return 实例对象
     */
    SadgformatPojo update(SadgformatPojo sadgformatpojo);

    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    SadgformatPojo getBillEntityByCode(String code, String userid, String tid);

    SadgformatPojo getTenBillEntityByCode(String code, String tid);
}
