package inks.sa.common.core.config.oa;

import cn.hutool.json.XML;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.CacheConstants;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.*;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oa.aes.*;
import inks.sa.common.core.config.oa.vo.UtsWxeapprPojo;
import inks.sa.common.core.config.oa.vo.UtsWxeapprrecEntity;
import inks.sa.common.core.config.oa.vo.UtsWxeapprrecPojo;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.vo.ApprovalCallbackDto;
import inks.sa.common.core.domain.vo.ApprovalRequestDto;
import inks.sa.common.core.mapper.UtsWxeapprrecMapper;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.wxe.QyWeChat;
import io.swagger.annotations.ApiOperation;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 企业微信 自建应用校验回调
 */
@RestController
@RequestMapping("OA")
public class OAHandler {

    @Resource
    private SaJustauthService saJustauthService;
    //@Resource
    //private OkHttpClient okHttpClient; // 推荐Spring中定义为Bean
    private final static Logger log = LoggerFactory.getLogger(OAHandler.class);

    @Value("${inks.feign.UtsUrl:未配置UtsUrl}") //http://192.168.99.96:10684
    private String utsUrl;
    @Value("${inks.tid}")
    private String tid;
    @Resource
    private SaConfigService saConfigService;
    @Resource
    private UtsWxeapprrecMapper utsWxeapprrecMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 发起审批（支持企微和钉钉）
     *
     * @param apprid 审批单ID
     * @param type   类型（"wxe"=企微，"ding"=钉钉）
     * @return R 返回通用结果对象
     */
    public R sendApproval(String apprid, String type) {
        // 根据type选择接口路径
        String path = "wxe".equals(type) ? "S34M06B1/sendapprovel" : "S34M06B2/sendapprovel";
        // 组装GET请求URL（tid可选，如果不需要可移除）拼完格式：http://192.168.99.96:10684/S34M06B1/sendapprovel?key=xxx&tid=xxx
        String url = String.format("%s/%s?key=%s&tid=%s", utsUrl, path, apprid, tid);

        // 构建GET请求
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        OkHttpClient okHttpClient = new OkHttpClient();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return R.fail("发起审批失败：" + response.message());
            }

            // 读取响应体
            okhttp3.ResponseBody body = response.body();
            if (body == null) {
                return R.fail("响应体为空");
            }

            String responseBody = body.string();

            // 解析JSON响应
            JSONObject jsonObject = JSON.parseObject(responseBody);

            // 根据你的R类结构判断业务是否成功
            if (jsonObject.containsKey("code") && jsonObject.getIntValue("code") == 200) {
                // 业务成功
                return R.ok(jsonObject.get("data"));
            } else {
                // 业务失败，获取错误信息
                String msg = jsonObject.getString("msg");
                if (msg == null || msg.isEmpty()) {
                    msg = "发起审批业务失败";
                }
                return R.fail(msg);
            }

        } catch (Exception e) {
            return R.fail("发起审批异常：" + e.getMessage());
        }
    }

    @GetMapping
    public String verifyURL(@RequestParam(name = "msg_signature") String signature, String timestamp, String nonce, String echostr, String tid) throws AesException {
        log.info("-----开始签名校验-----");
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        log.info("企业微信 corpId:" + QyWeChat.corpId);
        QyWeChat.callbacktoken = mapcfg.get(ConfigConstant.WXE_CALLBACK_TOKEN);
        log.info("企业微信 callbacktoken:" + QyWeChat.callbacktoken);
        QyWeChat.callbackAESKey = mapcfg.get(ConfigConstant.WXE_CALLBACK_AESKEY);
        log.info("企业微信 callbackAESKey:" + QyWeChat.callbackAESKey);
        //token和encoding_aeskey就是上诉随机获取的值，corp_id是企业id，在企业微信管理页面点击： 我的企业，拉到最下方可以看到
        WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(QyWeChat.callbacktoken.trim(), QyWeChat.callbackAESKey.trim(), QyWeChat.corpId.trim());
        String sEchoStr = wxcpt.VerifyURL(signature, timestamp, nonce, echostr);
        log.info("-----签名校验通过-----");
        return sEchoStr;
    }

    @PostMapping
    public String callbackURL(HttpServletRequest request, @RequestParam(name = "msg_signature") String signature,
                              String timestamp, String nonce, String tid) throws IOException {
        log.info("-----开始审批回调-----");
        // 1. 获取原始加密消息体（非JSON）
        String encryptedXml = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        //logger.info("msg:" + JSON.toJSONString(msg));
        log.info("encryptedXml" + encryptedXml);
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        log.info("企业微信 corpId:" + QyWeChat.corpId);
        QyWeChat.callbacktoken = mapcfg.get(ConfigConstant.WXE_CALLBACK_TOKEN);
        log.info("企业微信 callbacktoken:" + QyWeChat.callbacktoken);
        QyWeChat.callbackAESKey = mapcfg.get(ConfigConstant.WXE_CALLBACK_AESKEY);
        log.info("企业微信 callbackAESKey:" + QyWeChat.callbackAESKey);
        log.info("审批回调：开始计算密钥");
        String sEncryptMsg = "";
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(QyWeChat.callbacktoken.trim(), QyWeChat.callbackAESKey.trim(), QyWeChat.corpId.trim());
            //String sReqData = FormatUtils.getReqData(null);
            // logger.info("sReqData:"+sReqData);
            String sMsg = wxcpt.DecryptMsg(signature, timestamp, nonce, encryptedXml);
            // logger.info("sMsg:"+sMsg);
            //ElementUtils element = new ElementUtils(sMsg);
            String sRespData = FormatUtils.getTextRespData("");
            sEncryptMsg = wxcpt.EncryptMsg(sRespData, timestamp, nonce);
            // logger.info("sEncryptMsg :"+sEncryptMsg );
            String jsonsMsg = XML.toJSONObject(sMsg).toString();
            log.info("jsonsMsg :" + jsonsMsg);
            ResultXML resultXML = JSONObject.parseObject(jsonsMsg, ResultXML.class);

            //获得审批单号
            Long spno = resultXML.getXml().getApprovalInfo().getSpNo();
            log.info("审批单号：" + spno);
            //查询审批记录
            UtsWxeapprrecPojo utsWxeapprrecPojo = utsWxeapprrecMapper.getEntityBySpno(spno + "", tid);
            utsWxeapprrecPojo.setCallbackdate(new Date());
            utsWxeapprrecPojo.setCallbackuuid(resultXML.getXml().getApprovalInfo().getSpRecord().get(resultXML.getXml().getApprovalInfo().getSpRecord().size() - 1).getDetails().getApprover().getUserId());
            utsWxeapprrecPojo.setCallbackmsg(resultXML.toString());
            utsWxeapprrecPojo.setModifydate(new Date());
            utsWxeapprrecPojo.setRemark(resultXML.getXml().getApprovalInfo().getSpRecord().get(resultXML.getXml().getApprovalInfo().getSpRecord().size() - 1).getDetails().getSpeech());
// 审批状态
            Integer spstatus = resultXML.getXml().getApprovalInfo().getSpStatus();
            String callbackResult;
            boolean needCallback = true;
            boolean approved = false;

            if (spstatus == 2) {
                log.info("审批回调：审核通过");
                callbackResult = "审批通过";
                approved = true;
            } else if (spstatus == 3) {
                log.info("审批回调：审批被驳回");
                callbackResult = "审批驳回";
            } else {
                log.info("审批回调：审批转发，无需处理");
                return null; // 审批转发不执行后续逻辑
            }

            // 1. 写入数据库
            utsWxeapprrecPojo.setCallbackresult(callbackResult);
            UtsWxeapprrecEntity utsWxeapprrecEntity = new UtsWxeapprrecEntity();
            BeanUtils.copyProperties(utsWxeapprrecPojo, utsWxeapprrecEntity);
            utsWxeapprrecMapper.update(utsWxeapprrecEntity);
            log.info("审批回调：写入数据库完成");

            // 2. 写入 Redis
            ApprrecPojo apprrecPojo = new ApprrecPojo();
            org.springframework.beans.BeanUtils.copyProperties(utsWxeapprrecPojo, apprrecPojo);
            // 查出最终审批人的oms账号
            String callbackuuid = utsWxeapprrecPojo.getCallbackuuid();
            SaJustauthPojo justAuthDB = saJustauthService.getJustauthByUuid(callbackuuid, "wxe", null);
            if (justAuthDB != null) {
                apprrecPojo.setUserid(justAuthDB.getUserid());
                apprrecPojo.setRealname(justAuthDB.getRealname());
            } else {
                apprrecPojo.setUserid("OA审批人未关联三方账号");
                apprrecPojo.setRealname("OA审批人未关联三方账号");
            }
            String cacheKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(cacheKey, apprrecPojo, 60L * 12, TimeUnit.MINUTES);
            log.info("审批回调：写入 Redis 完成 " + cacheKey);

            // 3. 执行回调
            String url = utsWxeapprrecPojo.getCallbackurl().replace("{key}", utsWxeapprrecPojo.getId()) + "&type=wxe";
            if (!approved) {
                url += "&approved=false";
            }
            log.info("审批回调：开始执行回调 URL -> " + url);
            SendRequest.sendGet(url);


        } catch (Exception ex) {
            log.info("审批出错" + ex.getMessage());
        }
        log.info("-----结束审批回调-----");
        return sEncryptMsg;
    }

    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<UtsWxeapprPojo>> getListByModuleCode(String code, String tid) {
        try {
            //有Tid参
            if (tid == null || tid.isEmpty()) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            List<UtsWxeapprPojo> list = this.utsWxeapprrecMapper.getListByModuleCode(code, tid);
            if (list != null) {
                for (UtsWxeapprPojo utsWxeapprPojo : list) {
                    String verifyKey = CacheConstants.APPR_CODES_KEY + utsWxeapprPojo.getId();
                    ApprrecPojo apprrecPojo = new ApprrecPojo();
                    org.springframework.beans.BeanUtils.copyProperties(utsWxeapprPojo, apprrecPojo);
                    apprrecPojo.setId("");
                    saRedisService.setCacheObject(verifyKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
                    utsWxeapprPojo.setDatatemp(null);
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // --------------------------------------------实体单据处(例如：销售订单)的通用【发起审批】和【审批回调】--------------------------------------------
    @Resource
    private VelocityEngine velocityEngine;
    @Resource
    private JdbcTemplate jdbcTemplate;
    // 要检查的必有字段
    private static final List<String> REQUIRED_FIELDS = Arrays.asList(
            "OaflowMark", "id", "Assessor", "Assessorid", "AssessDate"
    );

    /**
     * 最终版方法签名，只接收 Map，不感知 SaBusinessPojo
     */
    public ApprrecPojo sendApproval(ApprovalRequestDto request, LoginUser loginUser, Map<String, Object> billMap, String tableName) {
        log.info("开始发起审批流程 - 操作表名：{}, 单据ID: {}, 审批类型: {}, 模板ID: {}", tableName, billMap.get("id"), request.getType(), request.getApprid());
        // 检查表名不能是空或含有中文
        if (StringUtils.isBlank(tableName) || !tableName.matches("^[a-zA-Z0-9_]+$")) {
            log.error("表名不合法 - 表名: {}", tableName);
            throw new IllegalArgumentException("表名不合法，必须为字母、数字或下划线");
        }
        // 字段存在性检查 ✅检查传入的表名包含以下字段：OaflowMark、id、Assessor、Assessorid、AssessDate
        validateTableColumns(tableName);
        // 步骤 1: 业务前置校验 (对 Map 进行操作)
        Object oaflowmark = billMap.get("oaflowmark");
        if (Objects.equals(1, oaflowmark) || Objects.equals("1", String.valueOf(oaflowmark))) {
            log.warn("审批发起失败 - 单据已发起OA审批: {}", billMap.get("id"));
            throw new IllegalStateException("该单据已发起OA审批，请勿重复提交");
        }
        log.info("业务前置校验通过 - 单据ID: {}", billMap.get("id"));

        // 步骤 2: 获取缓存中的审批模板
        String templateCacheKey = CacheConstants.APPR_CODES_KEY + request.getApprid();
        ApprrecPojo apprrecPojo = saRedisService.getCacheObject(templateCacheKey, ApprrecPojo.class);
        if (apprrecPojo == null) {
            log.error("审批模板获取失败 - 模板ID: {}", request.getApprid());
            throw new IllegalArgumentException("审批模板不存在或已过期");
        }
        log.info("审批模板获取成功 - 模板ID: {}, 模板名称: {}", request.getApprid(), apprrecPojo.getApprname());

        // 步骤 3: 准备模板渲染所需的数据
        log.info("开始准备审批数据 - 用户ID: {}", loginUser.getUserid());
        ApprovePojo approvePojo = prepareApproveData(request, billMap, loginUser, apprrecPojo.getTemplateid());
        log.info("审批数据准备完成 - 创建人ID: {}", approvePojo.getCreatoruserid());

        // 步骤 4: 渲染模板并根据需要替换 process 节点
        log.info("开始渲染审批模板 - 是否包含自定义process: {}", request.getProcess() != null);
        String finalData = renderTemplate(apprrecPojo.getDatatemp(), approvePojo, request.getProcess());
        apprrecPojo.setDatatemp(finalData);
        log.info("模板渲染完成 - 模板长度: {}", finalData.length());

        // 步骤 5: 填充审批记录对象并存入 Redis
        log.info("开始创建审批记录");
        populateAndCacheRecord(apprrecPojo, request, loginUser);
        log.info("审批记录创建完成 - 记录ID: {}", apprrecPojo.getId());

        // 步骤 6: 触发第三方审批流程
        String type = StringUtils.defaultIfBlank(request.getType(), "wxe");
        if ("wxe".equals(type) || "ding".equals(type)) {
            log.info("开始发起第三方审批 - 类型: {}, 记录ID: {}", type, apprrecPojo.getId());
            R r = this.sendApproval(apprrecPojo.getId(), type);
            if (r.getCode() != 200) {
                log.error("第三方审批发起失败 - 类型: {}, 错误信息: {}", type, r.getMsg());
                throw new RuntimeException("调用第三方审批接口失败: " + r.getMsg());
            }
            log.info("第三方审批发起成功 - 类型: {}", type);
        } else {
            log.info("跳过第三方审批 - 审批类型: {}", type);
        }

        // --- 步骤 7: 更新业务单据状态 ---
        // 调用职责单一的更新方法
        log.info("开始更新单据OA流程标记 - 表名: {}, 单据ID: {}", tableName, billMap.get("id"));
        updateOaFlowMarkById(tableName, billMap);
        log.info("单据OA流程标记更新完成");

        log.info("审批流程发起完成 - 审批记录ID: {}, 单据ID: {}", apprrecPojo.getId(), billMap.get("id"));
        return apprrecPojo;
    }

    /**
     * 最终版更新方法：职责单一，逻辑固定。
     * 为指定表，根据 id 更新 oaflowmark 字段。
     *
     * @param tableName 目标表名
     * @param dataMap   包含 "id" 值的 Map
     */
    private void updateOaFlowMarkById(String tableName, Map<String, Object> dataMap) {
        // 1. 从 Map 中获取 "id" 的值
        Object primaryKeyValue = dataMap.get("id");
        if (primaryKeyValue == null) {
            throw new IllegalArgumentException("更新失败：业务数据中必须包含 'id' 字段。");
        }
        // 2. 构建SQL，所有标识符(表名、列名)均为硬编码，绝对安全。
        String sql = String.format("UPDATE %s SET oaflowmark = ? WHERE id = ?", tableName);
        // 3. 执行更新，将状态值硬编码为 1
        jdbcTemplate.update(sql, 1, primaryKeyValue);
    }

    /**
     * 检查指定表是否包含所有必需字段
     */
    public void validateTableColumns(String tableName) {
        String sql = "SELECT COLUMN_NAME FROM information_schema.COLUMNS " +
                "WHERE TABLE_SCHEMA = DATABASE() " +  // 当前数据库
                "AND TABLE_NAME = ?";

        try {
            List<String> existingColumns = jdbcTemplate.queryForList(sql, String.class, tableName);

            Set<String> existingColumnSet = existingColumns.stream()
                    .map(String::toLowerCase)  // 转小写，避免大小写问题
                    .collect(Collectors.toSet());

            // 检查是否缺失字段
            List<String> missingFields = REQUIRED_FIELDS.stream()
                    .filter(field -> !existingColumnSet.contains(field.toLowerCase()))
                    .collect(Collectors.toList());

            if (!missingFields.isEmpty()) {
                String errorMsg = "表 [" + tableName + "] 缺少必要字段: " + missingFields;
                log.error(errorMsg);
                throw new IllegalArgumentException(errorMsg);
            }

            log.info("表 [{}] 字段校验通过，包含所有必需字段。", tableName);

        } catch (Exception e) {
            log.error("检查表字段时发生错误 - 表名: {}", tableName, e);
            throw new RuntimeException("无法验证表结构: " + tableName, e);
        }
    }

    private ApprovePojo prepareApproveData(ApprovalRequestDto request, Map<String, Object> billMap, LoginUser loginUser, String templateId) {
        ApprovePojo approvePojo = new ApprovePojo();
        approvePojo.setObject(billMap);

        String type = request.getType();
        if (!"oms".equals(type)) {
            SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
            if (justAuth == null) {
                log.info("审批发起人第三方账号信息未找到");
                throw new IllegalStateException("审批发起人第三方账号信息未找到");
            }
            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);

            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(templateId);
        }
        return approvePojo;
    }

    private String renderTemplate(String templateContent, ApprovePojo approveData, com.fasterxml.jackson.databind.JsonNode processNode) {
        VelocityContext context = new VelocityContext();
        context.put("approvePojo", approveData);
        StringWriter writer = new StringWriter();
        velocityEngine.evaluate(context, writer, "TemplateRenderer", templateContent);
        String renderedJson = writer.toString();

        if (processNode != null && !processNode.isNull()) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(renderedJson);
                // 将JsonNode转换为JSONObject
                String processJsonString = processNode.toString();
                JSONObject processJsonObject = JSONObject.parseObject(processJsonString);
                jsonObject.put("process", processJsonObject);
                return jsonObject.toJSONString();
            } catch (Exception e) {
                throw new IllegalArgumentException("Process参数非标准JSON格式，替换失败", e);
            }
        }
        return renderedJson;
    }


    private void populateAndCacheRecord(ApprrecPojo apprrecPojo, ApprovalRequestDto request, LoginUser loginUser) {
        apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        apprrecPojo.setApprname("订单审批");
        apprrecPojo.setBillid(request.getKey());
        apprrecPojo.setResultcode("");
        apprrecPojo.setUserid("");
        apprrecPojo.setApprtype("");
        apprrecPojo.setCreateby(loginUser.getRealname());
        apprrecPojo.setCreatebyid(loginUser.getUserid());
        apprrecPojo.setCreatedate(new Date());
        apprrecPojo.setLister(loginUser.getRealname());
        apprrecPojo.setListerid(loginUser.getUserid());
        apprrecPojo.setModifydate(new Date());
        apprrecPojo.setTenantid(loginUser.getTenantid());
        apprrecPojo.setRemark(StringUtils.defaultString(request.getRemark()));
        String cacheKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
        saRedisService.setKeyValue(cacheKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
    }

    @Transactional
    public boolean handleApprovalCallback(ApprovalCallbackDto callback, Map<String, Object> billEntity, String tableName) {
        try {
            log.info("开始处理审批回调 - 操作表名：{}, 审批记录ID: {}, 审批结果: {}, 审批类型: {}, 单据ID: {}",
                    tableName, callback.getKey(), callback.getApproved(), callback.getType(), billEntity.get("id"));

            // 1. 读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + callback.getKey();
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            log.info("审批记录获取结果 - 记录存在: {}, 单据ID: {}",
                    apprrecPojo != null, apprrecPojo != null ? apprrecPojo.getBillid() : "null");

            if (apprrecPojo == null) {
                log.error("审批记录不存在或已过期 - 记录ID: {}", callback.getKey());
                throw new IllegalArgumentException("审批记录不存在或已过期");
            }

            // 2. 设置默认审批类型
            String type = StringUtils.defaultIfBlank(callback.getType(), "wxe");
            log.info("审批类型确定 - 类型: {}", type);

            // 3. 获取单据ID
            String billId = (String) billEntity.get("id");
            if (StringUtils.isBlank(billId)) {
                log.error("单据ID为空 - billEntity: {}", billEntity);
                throw new IllegalArgumentException("单据ID不能为空");
            }
            log.info("单据ID获取成功 - 单据ID: {}", billId);

            // 4. 使用通用JDBC更新OaFlowMark=0
            log.info("开始更新OA流程标记 - 表名: {}, 单据ID: {}", tableName, billId);
            String updateOaFlowMarkSql = "UPDATE " + tableName + " SET oaflowmark = 0 WHERE id = ?";
            jdbcTemplate.update(updateOaFlowMarkSql, billId);
            billEntity.put("oaflowmark", 0);
            log.info("OA流程标记更新完成 - 设置为0");

            // 5. 若审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(callback.getApproved())) {
                log.info("【审批被拒绝，流程结束】 - 审批记录ID: {}", callback.getKey());
                return true;
            }

            // 6. 根据审批类型设置审批人信息
            log.info("开始设置审批人信息 - 审批类型: {}", type);
            String assessorId = null;
            String assessor = null;

            if ("oms".equals(type)) {
                // 点击同意审批：审批人字段赋值
                assessorId = apprrecPojo.getUserid();
                assessor = apprrecPojo.getRealname();
                log.info("OMS审批人信息设置完成 - 审批人ID: {}, 审批人: {}", assessorId, assessor);
            } else {
                // 第三方审批（wxe/ding）
                log.info("开始获取第三方审批人信息 - 回调UUID: {}, 类型: {}", apprrecPojo.getCallbackuuid(), type);
                SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(
                        apprrecPojo.getCallbackuuid(), type, null);
                if (justauthByUuid == null) {
                    log.error("第三方审批人信息获取失败 - 回调UUID: {}, 类型: {}", apprrecPojo.getCallbackuuid(), type);
                    throw new IllegalArgumentException("审批回调失败,未找到第三方账号");
                }
                assessorId = justauthByUuid.getUserid();
                assessor = justauthByUuid.getRealname();
                log.info("第三方审批人信息获取成功 - 审批人ID: {}, 审批人: {}", assessorId, assessor);
            }

            // 7. 使用通用JDBC更新审批信息
            Date assessDate = new Date();
            log.info("开始更新审批信息 - 审批人ID: {}, 审批人: {}, 审批时间: {}", assessorId, assessor, assessDate);
            String updateApprovalSql = "UPDATE " + tableName + " SET assessorid = ?, assessor = ?, assessdate = ? WHERE id = ?";
            jdbcTemplate.update(updateApprovalSql, assessorId, assessor, assessDate, billId);
            log.info("审批信息更新完成");
            log.info("【审批同意，回调处理完成】 - 审批记录ID: {}, 单据ID: {}", callback.getKey(), billId);
            return true;

        } catch (Exception e) {
            log.error("审批回调处理失败 - 审批记录ID: {}, 错误信息: {}", callback.getKey(), e.getMessage(), e);
            throw new RuntimeException("审批回调处理失败：" + e.getMessage(), e);
        }
    }

}
