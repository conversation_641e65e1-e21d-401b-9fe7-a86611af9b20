package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaIndeximgEntity;
import inks.sa.common.core.domain.pojo.SaIndeximgPojo;
import inks.sa.common.core.mapper.SaIndeximgMapper;
import inks.sa.common.core.service.SaIndeximgService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaIndeximg)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
@Service("saIndeximgService")
public class SaIndeximgServiceImpl implements SaIndeximgService {
    @Resource
    private SaIndeximgMapper saIndeximgMapper;


    @Override
    public SaIndeximgPojo getEntity(String key) {
        return this.saIndeximgMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaIndeximgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaIndeximgPojo> lst = saIndeximgMapper.getPageList(queryParam);
            PageInfo<SaIndeximgPojo> pageInfo = new PageInfo<SaIndeximgPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saIndeximgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaIndeximgPojo insert(SaIndeximgPojo saIndeximgPojo) {
        //初始化NULL字段
        if (saIndeximgPojo.getId() == null) saIndeximgPojo.setId("");
        if (saIndeximgPojo.getPictureurl() == null) saIndeximgPojo.setPictureurl("");
        if (saIndeximgPojo.getDirname() == null) saIndeximgPojo.setDirname("");
        if (saIndeximgPojo.getFilename() == null) saIndeximgPojo.setFilename("");
        if (saIndeximgPojo.getProdid() == null) saIndeximgPojo.setProdid("");
        if (saIndeximgPojo.getDemandid() == null) saIndeximgPojo.setDemandid("");
        if (saIndeximgPojo.getSeq() == null) saIndeximgPojo.setSeq(0);
        if (saIndeximgPojo.getStatus() == null) saIndeximgPojo.setStatus(0);
        if (saIndeximgPojo.getType() == null) saIndeximgPojo.setType("");
        if (saIndeximgPojo.getCreateby() == null) saIndeximgPojo.setCreateby("");
        if (saIndeximgPojo.getCreatebyid() == null) saIndeximgPojo.setCreatebyid("");
        if (saIndeximgPojo.getCreatedate() == null) saIndeximgPojo.setCreatedate(new Date());
        if (saIndeximgPojo.getLister() == null) saIndeximgPojo.setLister("");
        if (saIndeximgPojo.getListerid() == null) saIndeximgPojo.setListerid("");
        if (saIndeximgPojo.getModifydate() == null) saIndeximgPojo.setModifydate(new Date());
        if (saIndeximgPojo.getCustom1() == null) saIndeximgPojo.setCustom1("");
        if (saIndeximgPojo.getCustom2() == null) saIndeximgPojo.setCustom2("");
        if (saIndeximgPojo.getCustom3() == null) saIndeximgPojo.setCustom3("");
        if (saIndeximgPojo.getCustom4() == null) saIndeximgPojo.setCustom4("");
        if (saIndeximgPojo.getCustom5() == null) saIndeximgPojo.setCustom5("");
        if (saIndeximgPojo.getCustom6() == null) saIndeximgPojo.setCustom6("");
        if (saIndeximgPojo.getCustom7() == null) saIndeximgPojo.setCustom7("");
        if (saIndeximgPojo.getCustom8() == null) saIndeximgPojo.setCustom8("");
        if (saIndeximgPojo.getCustom9() == null) saIndeximgPojo.setCustom9("");
        if (saIndeximgPojo.getCustom10() == null) saIndeximgPojo.setCustom10("");
        if (saIndeximgPojo.getTenantid() == null) saIndeximgPojo.setTenantid("");
        if (saIndeximgPojo.getRevision() == null) saIndeximgPojo.setRevision(0);
        SaIndeximgEntity saIndeximgEntity = new SaIndeximgEntity();
        BeanUtils.copyProperties(saIndeximgPojo, saIndeximgEntity);

        saIndeximgEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saIndeximgEntity.setRevision(1);  //乐观锁
        this.saIndeximgMapper.insert(saIndeximgEntity);
        return this.getEntity(saIndeximgEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saIndeximgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaIndeximgPojo update(SaIndeximgPojo saIndeximgPojo) {
        SaIndeximgEntity saIndeximgEntity = new SaIndeximgEntity();
        BeanUtils.copyProperties(saIndeximgPojo, saIndeximgEntity);
        this.saIndeximgMapper.update(saIndeximgEntity);
        return this.getEntity(saIndeximgEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saIndeximgMapper.delete(key);
    }


}
