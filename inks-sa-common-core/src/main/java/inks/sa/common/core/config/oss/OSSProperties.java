package inks.sa.common.core.config.oss;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * oss 属性
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */

@ConfigurationProperties(prefix = "oss")
public class OSSProperties {
    private <PERSON><PERSON><PERSON> aliyun;
    private Minio minio;

    public OSSProperties() {
    }

    public OSSProperties(<PERSON><PERSON><PERSON> aliyun, <PERSON>o minio) {
        this.aliyun = aliyun;
        this.minio = minio;
    }

    public AliYun getAliYun() {
        return this.aliyun;
    }

    public void setAliYun(<PERSON><PERSON><PERSON> aliyun) {
        this.aliyun = aliyun;
    }

    public Minio getMinio() {
        return this.minio;
    }

    public void setMinio(Minio minio) {
        this.minio = minio;
    }

    public static class AliYun {
        private boolean active;
        private String endpoint;
        private String accessKeyId;
        private String accessKeySecret;

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getAccessKeyId() {
            return accessKeyId;
        }

        public void setAccessKeyId(String accessKeyId) {
            this.accessKeyId = accessKeyId;
        }

        public String getAccessKeySecret() {
            return accessKeySecret;
        }

        public void setAccessKeySecret(String accessKeySecret) {
            this.accessKeySecret = accessKeySecret;
        }
    }

    public static class Minio {
        private boolean active;
        private String endpoint;
        private String accessKey;
        private String secretKey;

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }

        public String getEndpoint() {
            return endpoint;
        }

        public void setEndpoint(String endpoint) {
            this.endpoint = endpoint;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public void setAccessKey(String accessKey) {
            this.accessKey = accessKey;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }
    }
}
