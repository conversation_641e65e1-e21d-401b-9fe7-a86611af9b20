package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 组织用户表(SaDeptuser)实体类
 *
 * <AUTHOR>
 * @since 2025-05-05 13:00:44
 */
@Data
public class SaDeptuserPojo implements Serializable {
    private static final long serialVersionUID = 522795207377359449L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 组织id
    @Excel(name = "组织id") 
    private String deptid;
     // 组织编码
    @Excel(name = "组织编码") 
    private String deptcode;
     // 组织名称
    @Excel(name = "组织名称") 
    private String deptname;
     // 用户ID
    @Excel(name = "用户ID") 
    private String userid;
     // 登录名
    @Excel(name = "登录名") 
    private String username;
     // 姓名
    @Excel(name = "姓名") 
    private String realname;
     // 是否主管
    @Excel(name = "是否主管") 
    private Integer isadmin;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;
    //IsDeptAdmi 就是这里的isadmin,只用一次:用这个别名去BeanUtils.copyProperties()
    private Integer isdeptadmin;


}

