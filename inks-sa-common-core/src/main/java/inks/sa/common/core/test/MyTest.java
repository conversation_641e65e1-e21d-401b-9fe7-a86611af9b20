package inks.sa.common.core.test;

import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.junit.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class MyTest {
    @Test
    public void test() {
        System.out.println(111111);
    }

    @Test
    public void sendTextMessage() throws IOException {
        // 模拟参数赋值
        String webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fdef02ed-52e9-48ec-b2f4-7deb58c3e470";
        String content = "1";
        List<String> mentionedList = Arrays.asList("user1", "user2"); // 模拟@用户

        OkHttpClient client = new OkHttpClient();

        // 构建JSON请求体
        JSONObject text = new JSONObject();
        text.put("content", content);
        text.put("mentioned_list", mentionedList); // 支持@指定用户或@all

        JSONObject body = new JSONObject();
        body.put("msgtype", "text");
        body.put("text", text);

        // 构建HTTP请求
        Request request = new Request.Builder()
                .url(webhookUrl)
                .post(RequestBody.create(body.toString(), MediaType.parse("application/json")))
                .build();

        // 发送请求并处理响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code: " + response);
            System.out.println("消息发送成功：" + response.body().string());
        }
    }





        @Test
        public void sendMarkdownMessage() throws IOException {
            // 模拟参数赋值
            String webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fdef02ed-52e9-48ec-b2f4-7deb58c3e470";
            String content = "## 这是一个Markdown消息\n" +
                    "### 欢迎使用企业微信机器人！\n" +
                    "你可以发送 **Markdown 格式的内容** 给团队。";

            OkHttpClient client = new OkHttpClient();

            // 构建Markdown消息内容
            JSONObject markdown = new JSONObject();
            markdown.put("content", content);

            JSONObject body = new JSONObject();
            body.put("msgtype", "markdown");
            body.put("markdown", markdown);

            // 构建HTTP请求
            Request request = new Request.Builder()
                    .url(webhookUrl)
                    .post(RequestBody.create(body.toString(), MediaType.parse("application/json")))
                    .build();

            // 发送请求并处理响应
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) throw new IOException("Unexpected code: " + response);
                System.out.println("Markdown消息发送成功：" + response.body().string());
            }
        }


}