package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 报表中心(SaReports)实体类
 *
 * <AUTHOR>
 * @since 2025-03-25 10:23:28
 */
@Data
public class SaReportsPojo implements Serializable {
    private static final long serialVersionUID = 308710529929954818L;
    // id
    @Excel(name = "id")
    private String id;
    // 通用分组
    @Excel(name = "通用分组")
    private String gengroupid;
    // 模块编码
    @Excel(name = "模块编码")
    private String modulecode;
    // 报表类型
    @Excel(name = "报表类型")
    private String rpttype;
    // 报表名称
    @Excel(name = "报表名称")
    private String rptname;
    // 报表数据
    @Excel(name = "报表数据")
    private String rptdata;
    // 单页行数
    @Excel(name = "单页行数")
    private Integer pagerow;
    // 远程打印模版Url
    @Excel(name = "远程打印模版Url")
    private String tempurl;
    // 模版文件名
    @Excel(name = "模版文件名")
    private String filename;
    // 远程打印机SN
    @Excel(name = "远程打印机SN")
    private String printersn;
    // 序号
    @Excel(name = "序号")
    private Integer rownum;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // grf文本备用
    @Excel(name = "grf文本备用")
    private String grfdata;
    // 长
    @Excel(name = "长")
    private Double paperlength;
    // 宽
    @Excel(name = "宽")
    private Double paperwidth;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;



}

