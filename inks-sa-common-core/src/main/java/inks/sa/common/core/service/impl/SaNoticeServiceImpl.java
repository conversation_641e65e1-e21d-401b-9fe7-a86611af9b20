package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaNoticeEntity;
import inks.sa.common.core.domain.pojo.SaNoticePojo;
import inks.sa.common.core.mapper.SaNoticeMapper;
import inks.sa.common.core.service.SaNoticeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaNotice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
@Service("saNoticeService")
public class SaNoticeServiceImpl implements SaNoticeService {
    @Resource
    private SaNoticeMapper saNoticeMapper;


    @Override
    public SaNoticePojo getEntity(String key) {
        return this.saNoticeMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaNoticePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaNoticePojo> lst = saNoticeMapper.getPageList(queryParam);
            PageInfo<SaNoticePojo> pageInfo = new PageInfo<SaNoticePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saNoticePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaNoticePojo insert(SaNoticePojo saNoticePojo) {
        //初始化NULL字段
        if (saNoticePojo.getTitle() == null) saNoticePojo.setTitle("");
        if (saNoticePojo.getType() == null) saNoticePojo.setType("");
        if (saNoticePojo.getContent() == null) saNoticePojo.setContent("");
        if (saNoticePojo.getStatus() == null) saNoticePojo.setStatus(0);
        if (saNoticePojo.getRemark() == null) saNoticePojo.setRemark("");
        if (saNoticePojo.getCreateby() == null) saNoticePojo.setCreateby("");
        if (saNoticePojo.getCreatebyid() == null) saNoticePojo.setCreatebyid("");
        if (saNoticePojo.getCreatedate() == null) saNoticePojo.setCreatedate(new Date());
        if (saNoticePojo.getLister() == null) saNoticePojo.setLister("");
        if (saNoticePojo.getListerid() == null) saNoticePojo.setListerid("");
        if (saNoticePojo.getModifydate() == null) saNoticePojo.setModifydate(new Date());
        if (saNoticePojo.getCustom1() == null) saNoticePojo.setCustom1("");
        if (saNoticePojo.getCustom2() == null) saNoticePojo.setCustom2("");
        if (saNoticePojo.getCustom3() == null) saNoticePojo.setCustom3("");
        if (saNoticePojo.getCustom4() == null) saNoticePojo.setCustom4("");
        if (saNoticePojo.getCustom5() == null) saNoticePojo.setCustom5("");
        if (saNoticePojo.getCustom6() == null) saNoticePojo.setCustom6("");
        if (saNoticePojo.getCustom7() == null) saNoticePojo.setCustom7("");
        if (saNoticePojo.getCustom8() == null) saNoticePojo.setCustom8("");
        if (saNoticePojo.getCustom9() == null) saNoticePojo.setCustom9("");
        if (saNoticePojo.getCustom10() == null) saNoticePojo.setCustom10("");
        if (saNoticePojo.getTenantid() == null) saNoticePojo.setTenantid("");
        if (saNoticePojo.getRevision() == null) saNoticePojo.setRevision(0);
        SaNoticeEntity saNoticeEntity = new SaNoticeEntity();
        BeanUtils.copyProperties(saNoticePojo, saNoticeEntity);

        saNoticeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saNoticeEntity.setRevision(1);  //乐观锁
        this.saNoticeMapper.insert(saNoticeEntity);
        return this.getEntity(saNoticeEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saNoticePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaNoticePojo update(SaNoticePojo saNoticePojo) {
        SaNoticeEntity saNoticeEntity = new SaNoticeEntity();
        BeanUtils.copyProperties(saNoticePojo, saNoticeEntity);
        this.saNoticeMapper.update(saNoticeEntity);
        return this.getEntity(saNoticeEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saNoticeMapper.delete(key);
    }


}
