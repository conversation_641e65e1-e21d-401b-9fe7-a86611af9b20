package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;
import inks.sa.common.core.service.SaCompanyService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 公司信息表(Sa_Company)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-22 12:48:49
 */
@RestController
@RequestMapping("SaCompany")
@Api(tags = "通用:公司信息")
public class A_SaCompanyController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaCompanyController.class);

    @Resource
    private SaCompanyService saCompanyService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "保存公司信息(针对id=1,无则新增,有则修改)", notes = "", produces = "application/json")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Company.List")
    public R<SaCompanyPojo> save(@RequestBody String json) {
        try {
            Date now = new Date();
            SaCompanyPojo saCompanyPojoFront = JSONArray.parseObject(json, SaCompanyPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 设置基本信息
            saCompanyPojoFront.setId("1");
            saCompanyPojoFront.setModifydate(now);
            saCompanyPojoFront.setLister(loginUser.getRealName());
            saCompanyPojoFront.setListerid(loginUser.getUserid());

            // 判断是否存在公司信息
            SaCompanyPojo companyPojoDB = saCompanyService.getEntity("1");
            if (companyPojoDB == null) {
                saCompanyPojoFront.setCreateby(loginUser.getRealName());
                saCompanyPojoFront.setCreatebyid(loginUser.getUserid());
                saCompanyPojoFront.setCreatedate(now);
                return R.ok(saCompanyService.insert(saCompanyPojoFront));
            } else {
                return R.ok(saCompanyService.update(saCompanyPojoFront));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取公司信息(等同saCompanyMapper.getEntity(1))", notes = "", produces = "application/json")
    @PostMapping("/getCompanyInfo")
    public R<SaCompanyPojo> getCompanyInfo() {
        try {
            // 获得当前用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(saCompanyService.getCompanyInfo());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取公司信息表详细信息", notes = "获取公司信息表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Company.List")
    public R<SaCompanyPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCompanyService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Company.List")
    public R<PageInfo<SaCompanyPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Company.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCompanyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增公司信息表", notes = "新增公司信息表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Company.Add")
    public R<SaCompanyPojo> create(@RequestBody String json) {
        try {
            SaCompanyPojo saCompanyPojo = JSONArray.parseObject(json, SaCompanyPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCompanyPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCompanyPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCompanyPojo.setCreatedate(new Date());   // 创建时间
            saCompanyPojo.setLister(loginUser.getRealname());   // 制表
            saCompanyPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCompanyPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saCompanyService.insert(saCompanyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改公司信息表", notes = "修改公司信息表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Company.Edit")
    public R<SaCompanyPojo> update(@RequestBody String json) {
        try {
            SaCompanyPojo saCompanyPojo = JSONArray.parseObject(json, SaCompanyPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCompanyPojo.setLister(loginUser.getRealname());   // 制表
            saCompanyPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCompanyPojo.setModifydate(new Date());   //修改时间
//            saCompanyPojo.setAssessor(""); // 审核员
//            saCompanyPojo.setAssessorid(""); // 审核员id
//            saCompanyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saCompanyService.update(saCompanyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除公司信息表", notes = "删除公司信息表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Company.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCompanyService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    //@PreAuthorize(hasPermi = "Sa_Company.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//        // 获得用户数据
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        SaCompanyPojo saCompanyPojo = this.saCompanyService.getEntity(key);
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(saCompanyPojo);
//        // 加入公司信息
//        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
//        //从redis中获取Reprot内容
//        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//        String content;
//        if (reportsPojo != null) {
//            content = reportsPojo.getRptdata();
//        } else {
//            throw new BaseBusinessException("未找到报表");
//        }
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response = ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
}

