package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaLogPojo;
import inks.sa.common.core.domain.SaLogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 通用日志(SaLog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-17 16:22:15
 */
public interface SaLogService {


    SaLogPojo getEntity(String key);

    PageInfo<SaLogPojo> getPageList(QueryParam queryParam);

    SaLogPojo insert(SaLogPojo saLogPojo);

    SaLogPojo update(SaLogPojo saLogpojo);

    int delete(String key);
}
