package inks.sa.common.core.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Service
@FeignClient(name = "grfClient", url = "${inks.feign.GrfUrl}")
//@FeignClient(name = "grfClient", url = "http://192.168.99.69:18801")
public interface GrfFeignClient {

    //    通过在 @PostMapping 注解中添加 consumes 属性，你可以明确指定 Feign 客户端期望的请求 Content-Type 为 JSON 格式。
//    解决报错信息中的 415 Unsupported Media Type 问题。
    @PostMapping(value = "/Grf/getPdfByRedis", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<byte[]> getGrfReport(@RequestBody String json);

}