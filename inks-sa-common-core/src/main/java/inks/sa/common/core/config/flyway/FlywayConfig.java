//package inks.service.sa.cmr.config.flyway;
//
//import org.flywaydb.core.Flyway;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.DependsOn;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//
///***
// * <AUTHOR>
// * @Description 以下配置相当于yml中的如下配置:(2选1即可, 但是推荐使用yml配置 同时存在yml优先级更高)
// *   flyway:
// *     baseline-on-migrate: true
// *     enabled: true
// *     locations: classpath:/db/migration
// * @time 2023/11/23 9:23
// */
//@Configuration
//public class FlywayConfig {
//
//    @Resource
//    private DataSource dataSource;
//
//    /**
//     @PostConstruct： 这个注解标注在方法上，用于指定在构造函数执行完毕后立即执行的方法。在 Spring 容器初始化 bean 时，
//     如果发现有 @PostConstruct 注解的方法，它会在调用构造函数后立即执行这个方法。在上述的代码中，migrate 方法被标注为 @PostConstruct，
//     因此在 FlywayConfig bean 初始化完成后会自动执行这个方法。
//     @DependsOn 主要是用于指定 bean 之间的依赖关系，确保某个 bean 在另一个 bean 初始化之前完成初始化。
//     如果你将 Flyway 迁移的触发方式从应用初始化时改为前端调用，那么就不再需要 @DependsOn("dataSourceHelper") 这个注解了。
//     在这种情况下，因为 Flyway 迁移的触发点是前端调用而不是应用初始化，所以与其他 bean 的初始化顺序无关。你可以安全地去掉 @DependsOn("dataSourceHelper")
//     */
//    @PostConstruct
//    @DependsOn("dataSourceHelper")
//    public void migrate() {
//        //baselineOnMigrate(true) 如果数据库中没有 Flyway 的元数据表（通常是 flyway_schema_history），设置为 true 会在首次执行迁移时自动创建这个表，并将当前数据库状态标记为迁移的起始点。
//        Flyway flyway = Flyway.configure().dataSource(dataSource).locations("classpath:db/migration",
//                "filesystem:db/migration").baselineOnMigrate(true).load();
//        // 调用 migrate() 方法执行迁移操作。这会检查数据库的当前版本，并根据配置的位置找到并执行相应的 SQL 脚本，将数据库升级到最新版本。
//        flyway.migrate();
//    }
//
//}
