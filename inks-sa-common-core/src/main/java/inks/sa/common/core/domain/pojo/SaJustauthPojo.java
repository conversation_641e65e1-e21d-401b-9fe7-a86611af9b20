package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 第三方登录(SaJustauth)实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:01:59
 */
@Data
public class SaJustauthPojo implements Serializable {
    private static final long serialVersionUID = -98409739806669659L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 用户id
    @Excel(name = "用户id") 
    private String userid;
     // 登录名
    @Excel(name = "登录名") 
    private String username;
     // 姓名
    @Excel(name = "姓名") 
    private String realname;
     // 昵称
    @Excel(name = "昵称") 
    private String nickname;
     // ding/wxe/openid
    @Excel(name = "ding/wxe/openid") 
    private String authtype;
     // uuid
    @Excel(name = "uuid") 
    private String authuuid;
     // Unionid
    @Excel(name = "Unionid") 
    private String unionid;
     // avatar
    @Excel(name = "avatar") 
    private String authavatar;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

