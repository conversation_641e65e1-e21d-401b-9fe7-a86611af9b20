package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaWarningPojo;
import inks.sa.common.core.domain.SaWarningEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预警(SaWarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Mapper
public interface SaWarningMapper {


    SaWarningPojo getEntity(@Param("key") String key);

    List<SaWarningPojo> getPageList(QueryParam queryParam);

    int insert(SaWarningEntity saWarningEntity);

    int update(SaWarningEntity saWarningEntity);

    int delete(@Param("key") String key);
    
}

