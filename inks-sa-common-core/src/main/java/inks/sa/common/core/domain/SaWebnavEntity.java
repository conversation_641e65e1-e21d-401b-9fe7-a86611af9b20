package inks.sa.common.core.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * Pc导航(SaWebnav)实体类
 *
 * <AUTHOR>
 * @since 2024-03-02 13:42:49
 */
public class SaWebnavEntity implements Serializable {
    private static final long serialVersionUID = -43262525907284757L;
    // Navid
    private String navid;
    // 导航编码
    private String navcode;
    // 导航名称
    private String navname;
    // 导航内容
    private String navcontent;
    // 排列序号
    private Integer rownum;
    // 有效标识
    private Integer enabledmark;
    // 许可编码
    private String permissioncode;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 乐观锁
    private Integer revision;

    // Navid
    public String getNavid() {
        return navid;
    }

    public void setNavid(String navid) {
        this.navid = navid;
    }

    // 导航编码
    public String getNavcode() {
        return navcode;
    }

    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }

    // 导航名称
    public String getNavname() {
        return navname;
    }

    public void setNavname(String navname) {
        this.navname = navname;
    }

    // 导航内容
    public String getNavcontent() {
        return navcontent;
    }

    public void setNavcontent(String navcontent) {
        this.navcontent = navcontent;
    }

    // 排列序号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 许可编码
    public String getPermissioncode() {
        return permissioncode;
    }

    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

