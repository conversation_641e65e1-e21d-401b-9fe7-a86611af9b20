package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaBillcodePojo;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 单据编码(Sa_BillCode)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:43
 */

@RestController
@RequestMapping("SaBillCode")
@Api(tags = "通用:单据编码")
public class A_SaBillcodeController {

    private static final Logger log = LoggerFactory.getLogger(A_SaBillcodeController.class);
    @Resource
    private SaBillcodeService sabillcodeService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取单据编码", notes = "获取单据", produces = "application/json")
    @RequestMapping(value = "/getBillCode", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_BillCode.List")
    public R<String> getBillCode(String code, @RequestParam(required = false) String tablename, @RequestParam(required = false) String prefix) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.sabillcodeService.getSerialNo(code, loginUser.getTenantid(), tablename, prefix));
        } catch (BaseBusinessException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            return R.fail("系统错误：" + e.getMessage());
        }
    }

    @ApiOperation(value = "get新编码 传入表名.字段 如：tablename=Qms_Defect、column=DefCode", notes = "get新编码", produces = "application/json")
    @RequestMapping(value = "/getNextCode", method = RequestMethod.GET)
    public R<String> getNextCode(@RequestParam() String tablename, String column) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 编码最大数字部分加1返回
            String nextCode = this.sabillcodeService.getNextCode(tablename, column);
            return R.ok(nextCode);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取单据编码详细信息", notes = "获取单据编码详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.List")
    public R<SaBillcodePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sabillcodeService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.List")
    public R<PageInfo<SaBillcodePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_BillCode.Modulecode");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.sabillcodeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增单据编码", notes = "新增单据编码", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.Add")
    public R<SaBillcodePojo> create(@RequestBody String json) {
        try {
            SaBillcodePojo sabillcodePojo = JSONArray.parseObject(json, SaBillcodePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sabillcodePojo.setLister(loginUser.getRealname());   //用户名
            if (sabillcodePojo.getTenantid() == null) sabillcodePojo.setTenantid(loginUser.getTenantid());   //租户id
            sabillcodePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.sabillcodeService.insert(sabillcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改单据编码", notes = "修改单据编码", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.Edit")
    public R<SaBillcodePojo> update(@RequestBody String json) {
        try {
            SaBillcodePojo sabillcodePojo = JSONArray.parseObject(json, SaBillcodePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sabillcodePojo.setLister(loginUser.getRealname());   //用户名
            sabillcodePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.sabillcodeService.update(sabillcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除单据编码", notes = "删除单据编码", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sabillcodeService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillCode.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaBillcodePojo sabillcodePojo = this.sabillcodeService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(sabillcodePojo);

        //从redis中获取XML内容
        String xml = saRedisService.getValue("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


}

