package inks.sa.common.core.config.flyway;

import org.flywaydb.core.api.callback.Callback;
import org.flywaydb.core.api.callback.Context;
import org.flywaydb.core.api.callback.Event;

/**
 * Flyway 进度回调
 * 监听 Flyway 的真实执行事件
 */
public class FlywayProgressCallback implements Callback {
    
    private final ProgressPageManager progressManager;
    private int totalMigrations = 0;
    private int completedMigrations = 0;
    
    public FlywayProgressCallback(ProgressPageManager progressManager) {
        this.progressManager = progressManager;
    }
    
    @Override
    public String getCallbackName() {
        return "FlywayProgressCallback";
    }

    @Override
    public boolean supports(Event event, Context context) {
        // 监听主要的迁移事件
        return event == Event.BEFORE_MIGRATE ||
               event == Event.AFTER_MIGRATE ||
               event == Event.BEFORE_EACH_MIGRATE ||
               event == Event.AFTER_EACH_MIGRATE;
    }
    
    @Override
    public boolean canHandleInTransaction(Event event, Context context) {
        return true;
    }
    
    @Override
    public void handle(Event event, Context context) {
        try {
            switch (event) {
                case BEFORE_MIGRATE:
                    handleBeforeMigrate(context);
                    break;
                case AFTER_MIGRATE:
                    handleAfterMigrate(context);
                    break;
                case BEFORE_EACH_MIGRATE:
                    handleBeforeEachMigrate(context);
                    break;
                case AFTER_EACH_MIGRATE:
                    handleAfterEachMigrate(context);
                    break;
                default:
                    // 其他事件暂不处理
                    break;
            }
        } catch (Exception e) {
            // 忽略回调中的错误，避免影响 Flyway 执行
            System.err.println("Flyway回调处理错误: " + e.getMessage());
        }
    }
    
    private void handleBeforeMigrate(Context context) {
        progressManager.addLog("开始 Flyway 数据库迁移...");
        progressManager.updateProgress("准备执行数据库迁移...", 72);
    }
    
    private void handleAfterMigrate(Context context) {
        progressManager.addLog("Flyway 数据库迁移完成！");
        progressManager.updateProgress("数据库迁移执行完成", 95);
    }
    
    private void handleBeforeEachMigrate(Context context) {
        if (context.getMigrationInfo() != null) {
            String version = context.getMigrationInfo().getVersion() != null ? 
                context.getMigrationInfo().getVersion().toString() : "未知版本";
            String description = context.getMigrationInfo().getDescription();
            
            progressManager.addLog("开始执行迁移: " + version + " - " + description);
        }
    }
    
    private void handleAfterEachMigrate(Context context) {
        completedMigrations++;
        
        if (context.getMigrationInfo() != null) {
            String version = context.getMigrationInfo().getVersion() != null ? 
                context.getMigrationInfo().getVersion().toString() : "未知版本";
            String description = context.getMigrationInfo().getDescription();
            
            progressManager.addLog("完成迁移: " + version + " - " + description);
            
            // 更新进度
            if (totalMigrations > 0) {
                int progress = 75 + (completedMigrations * 20 / totalMigrations);
                progressManager.updateProgress("正在执行迁移... (" + completedMigrations + "/" + totalMigrations + ")", 
                    Math.min(95, progress));
            }
        }
    }
    

    
    /**
     * 设置总迁移数量
     */
    public void setTotalMigrations(int total) {
        this.totalMigrations = total;
        if (total > 0) {
            progressManager.addLog("发现 " + total + " 个迁移脚本需要执行");
        }
    }
}
