package inks.sa.common.core.config.oss;


import inks.sa.common.core.config.oss.service.OSSConfigManager;
import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * minio oss配置类
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Configuration(proxyBeanMethods = false) // 保持proxyBeanMethods为false
@ConditionalOnClass({MinioClient.class})
public class MinioOSSConfiguration {

    @Bean
    public MinioClient minioClient(OSSConfigManager configManager) {
        return MinioClient.builder()
                .endpoint(configManager.getMinioEndpoint())
                .credentials(configManager.getMinioAccessKey(), configManager.getMinioSecretKey())
                .build();
    }

    @Bean
    public Storage minioStorage(MinioClient minioClient) { // 通过参数注入已创建的Bean
        return new MinioStorage(minioClient);
    }
}