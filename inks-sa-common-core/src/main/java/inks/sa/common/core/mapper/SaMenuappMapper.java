package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaMenuappEntity;
import inks.sa.common.core.domain.pojo.SaMenuappPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * APP导航(SaMenuapp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Mapper
public interface SaMenuappMapper {


    SaMenuappPojo getEntity(@Param("key") String key);

    List<SaMenuappPojo> getPageList(QueryParam queryParam);

    int insert(SaMenuappEntity saMenuappEntity);

    int update(SaMenuappEntity saMenuappEntity);

    int delete(@Param("key") String key);

    List<SaMenuappPojo> getListByPid(String key);

    List<SaMenuappPojo> getListAll();

    List<SaMenuappPojo> getListByNavids(@Param("navids") List<String> navids);
}

