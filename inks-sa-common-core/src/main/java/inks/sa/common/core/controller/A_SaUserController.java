package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.RSAUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oss.service.OSSConfigManager;
import inks.sa.common.core.config.oss.service.OssService;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.domain.SaConfigEntity;
import inks.sa.common.core.domain.pojo.*;
import inks.sa.common.core.mapper.SaAuthcodeMapper;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaUserService;
import inks.sa.common.core.service.impl.SaConfigServiceImpl;
import inks.sa.common.core.service.impl.SaUserServiceImpl;
import inks.sa.common.core.utils.LicenseUtils;
import inks.sa.common.core.utils.PrintColor;
import inks.sa.common.core.utils.RSA.MyRSA;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 用户服务(Sa_User)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:09
 */

@RestController
@RequestMapping("SaUser")
@Api(tags = "通用:用户服务")
public class A_SaUserController {

    @Resource
    private SaUserService saUserService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaUserMapper saUserMapper;
    @Resource
    private SaConfigMapper saConfigMapper;
    @Resource
    private SaAuthcodeMapper saAuthcodeMapper;

    @Autowired
    private OSSConfigManager configManager;

    private String osstype;

    @PostConstruct
    public void init() {
        osstype = String.valueOf(configManager.getOssType());
    }

    @Autowired
    @Qualifier("ossMinioServiceImpl")
    private OssService fileInfoMinioService; //minio上传
    @Autowired
    @Qualifier("ossAliyunServiceImpl")
    private OssService fileInfoAliYunService; //aliyun上传


    //默认公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
    @Value("${inks.oam.api:http://oam.inksyun.com}")
    private String OAM_API;
    @Value("${inks.oam.appid:wx7850d75f765d0dce}")
    private String OAM_APPID;

    @Resource
    private SaJustauthService saJustauthService;


    @ApiOperation(value = "头像上传", notes = "图片上传", produces = "application/json")
    @RequestMapping(value = "/uploadPic", method = RequestMethod.POST)
    public R<String> uploadPic(MultipartFile file, HttpServletRequest request, String ossType) {
        try {
            //String dir = "picture/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            FileInfo fileInfo = this.ossService.uploadPic(file);
            FileInfo fileInfo = null;
            if (StringUtils.isBlank(ossType)) {
                ossType = osstype;
            }
            if (ossType.equals("minio")) {
                fileInfo = fileInfoMinioService.upload(file, "picture", null);
            } else if (ossType.equals("aliyun")) {
                fileInfo = fileInfoAliYunService.upload(file, "picture", null);
            }

            SaUserPojo saUserPojo = new SaUserPojo();
            saUserPojo.setAvatar(fileInfo.getDirname() + "/" + fileInfo.getFilename());
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saUserPojo.setId(loginUser.getUserid());
            //更新用户信息(保存头像)
            saUserService.update(saUserPojo);
            return R.ok(fileInfo.getFileurl());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //type不为空时，表示是Web登录，进行权限校验roletype和Adminmark都不为1时，说明不是无Web端登录权限
    @ApiOperation(value = "用户名密码登录", notes = "用户名密码登录", produces = "application/json")
    @RequestMapping(value = "login", method = RequestMethod.POST)
    public R<Map<String, Object>> login(@RequestBody String json, HttpServletRequest request, String type) {
        try {
            LoginUserPojo loginUserPojo = JSON.parseObject(json, LoginUserPojo.class);
            Map<String, Object> tokenMap = saUserService.loginCheck(loginUserPojo.getUsername(), loginUserPojo.getPassword(), request, type);
            return R.ok(tokenMap, "登录成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "第三方账号登录 传入：authuuid、authtype", notes = "", produces = "application/json")
    @RequestMapping(value = "loginByJustAuth", method = RequestMethod.POST)
    public R<Map<String, Object>> loginByJustAuth(@RequestBody String json, HttpServletRequest request, String type) {
        try {
            SaJustauthPojo saJustauthPojo = JSON.parseObject(json, SaJustauthPojo.class);
            SaUserPojo userDB = saUserService.getEntityByJustAuth(saJustauthPojo.getAuthtype(), saJustauthPojo.getAuthuuid());
            String decryptPassword = SaUserServiceImpl.Decrypt(userDB.getPassword());
            Map<String, Object> tokenMap = saUserService.loginCheck(userDB.getUsername(), decryptPassword, request, type);
            return R.ok(tokenMap, "登录成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "授权码登陆，并将授权码-token的映射存入Sa_Redis", notes = "", produces = "application/json")
    @RequestMapping(value = "loginByAuthCode", method = RequestMethod.POST)
    public R<Map<String, Object>> loginByAuthCode(String authCode, HttpServletRequest request, String type) {
        try {
            SaAuthcodePojo saAuthcodePojo = saAuthcodeMapper.getEntityByAuthCode(authCode);
            if (saAuthcodePojo == null) {
                throw new BaseBusinessException("授权码无效,请联系管理员");
            }
            // 登录并获取token
            Map<String, Object> tokenMap = saUserService.loginCheck(saAuthcodePojo.getUsername(), saAuthcodePojo.getUserpassword(), request, type);
            String accessToken = tokenMap.get("access_token").toString();
            // 更新授权码-accessToken的映射到 Sa_Redis表
            String authCodeKey = MyConstant.AUTHCODE_KEY + AESUtil.Encrypt(authCode);
            saRedisService.setKeyValue(authCodeKey, accessToken, 43200);
            return R.ok(tokenMap, "登录成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过openidToken登录(oam服务redis中有键值对openid_token:openid)", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenidToken", method = RequestMethod.POST)
    public R<Map<String, Object>> loginByOpenidToken(String openidToken, HttpServletRequest request) {
        try {
            String openidFromOam = getOpenidFromOam(openidToken);
            Map<String, Object> token = saUserService.loginCheckByOpenid(openidFromOam, request);
            return R.ok(token, "登录成功");
        } catch (BaseBusinessException | IOException e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "直接通过openid登录", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenid", method = RequestMethod.GET)
    public R<Map<String, Object>> loginByOpenid(String openid, HttpServletRequest request) {
        try {
            Map<String, Object> token = saUserService.loginCheckByOpenid(openid, request);
            return R.ok(token, "登录成功");
        } catch (BaseBusinessException e) {
            return R.fail(e.getMessage());
        }
    }

    private String getOpenidFromOam(String openidToken) throws IOException {
        // 创建 OkHttpClient 对象
        OkHttpClient client = new OkHttpClient();
        // 创建请求 URL
        String url = OAM_API + "/wx/qrcode/{appid}/getOpenidByOpenidToken";
        url = url.replace("{appid}", OAM_APPID) + "?openidToken=" + openidToken;
        // 创建请求
        Request requestOam = new Request.Builder()
                .url(url)
                .get()
                .build();
        // 发起请求并处理响应
        try (Response response = client.newCall(requestOam).execute()) {
            if (!response.isSuccessful()) {
                throw new BaseBusinessException("OkHttp3发送请求失败：" + response.code());
            }
            //oam.responseBody格式--->     有openid:{"code":200,"msg":null,"data":"ozJNq6-aO6sKR4ehV3BSmqooAvFI"}
            //                            无openid:{"code":500,"msg":"redis中未找到openidToken或已过期","data":null}
            String responseBody = response.body().string(); // response.body()会消耗响应体的内容，并将其关闭,若再次尝试访问响应体时就会抛出 java.lang.IllegalStateException: closed 异常
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("data");
            if (StringUtils.isBlank(openid)) {
                throw new BaseBusinessException(responseBody);
            }
            return openid;
        }
    }


    @ApiOperation(value = "获取用户信息", notes = "获取用户信息", produces = "application/json")
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.GET)
    public R<SaUserPojo> getUserInfo() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser();
            SaUserPojo saUserPojo = saUserService.getUserInfo(loginUser);
            return R.ok(saUserPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "登录", notes = "登录", produces = "application/json")
//    @RequestMapping(value = "/wxlogin", method = RequestMethod.POST)
//    public R<Map<String, Object>> wxlogin(@RequestBody String json, HttpServletRequest request) {
//        System.out.println("-----------------------------------------------------------------------------");
//        System.out.println("我想要的值是：" + json);
//        try {
//            WechatLoginPojo wechatLoginPojo = JSON.parseObject(json, WechatLoginPojo.class);
//            Map<String, Object> map = wechatMiniappLoginService.login(wechatLoginPojo, request);
//            return R.ok(map, "登录成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


//    @ApiOperation(value = "修改密码", notes = "修改密码", produces = "application/json")
//    @RequestMapping(value = "/updatePass", method = RequestMethod.POST)
//    public R updatePass(@RequestBody String key) {
//        try {
//            LoginUser loginUser = this.saRedisService.getLoginUser();
//            SaUserloginPojo saUserloginPojo = new SaUserloginPojo();
//            saUserloginPojo.setUserid(loginUser.getUserid());
//            saUserloginPojo.setUserpassword(AESUtil.Encrypt(key));
//            saUserloginPojo.setLister(loginUser.getRealName());
//            saUserloginPojo.setListerid(loginUser.getUserid());
//            saUserloginService.insert(saUserloginPojo);
//            return R.ok();
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    @ApiOperation(value = "用户修改个人密码(先校验旧密码)", notes = "修改密码", produces = "application/json")
    @RequestMapping(value = "/updatePass", method = RequestMethod.GET)
    public R updatePass(String oldpassword, String newpassword) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser();
            // 先校验旧密码
            SaUserPojo saUserPojoDB = this.saUserMapper.checkPasswordByUserid(loginUser.getUserid(), AESUtil.Encrypt(oldpassword));
            if (saUserPojoDB == null) {
                return R.fail("旧密码错误");
            }
            // 再更新新密码
            SaUserPojo saUserPojo = new SaUserPojo();
            saUserPojo.setId(loginUser.getUserid());
//            saUserPojo.setPassword(AESUtil.Encrypt(newpassword));
            // update方法里做了加密了 这里不需要再加密
            saUserPojo.setPassword(newpassword);
            saUserPojo.setLister(loginUser.getRealName());
            saUserPojo.setModifydate(new Date());
            saUserService.update(saUserPojo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员admin账号初始化密码", notes = "", produces = "application/json")
    @RequestMapping(value = "/initPass", method = RequestMethod.GET)
    public R initPass(String password) {
        try {
            //加密密码
            String encryptPassword = AESUtil.Encrypt(password);
            saUserMapper.updateAdminPassword(encryptPassword);
            return R.ok("初始化密码成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员默认重置任一用户密码123456; 若传入密码,则重置为指定密码", notes = "管理员重置任一用户密码", produces = "application/json")
    @RequestMapping(value = "/resetPass", method = RequestMethod.GET)
    public R resetPass(String userid, String newpassword) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0) {
                return R.fail("非管理员无权重置密码");
            } else if (loginUser.getIsadmin() == 1) {
                int adminMark = saUserMapper.getAdminMarkByUserid(userid);
                if (adminMark == 1 || adminMark == 2) {
                    return R.fail("普通管理员无权重置管理员密码,请联系超级管理员重置");
                }
            }
            // 再更新新密码
            SaUserPojo saUserPojo = new SaUserPojo();
            saUserPojo.setId(userid);
            String password = StringUtils.isBlank(newpassword) ? "123456" : newpassword;
            saUserPojo.setPassword(password);
            saUserPojo.setLister(loginUser.getRealName());
            saUserPojo.setModifydate(new Date());
            saUserService.update(saUserPojo);
            return R.ok("重置密码成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @GetMapping("/{filename:.+}")
//    public String getFile(@PathVariable String filename) {
//        return "5668e41a079f2990127f6be8e7dc4988";
//    }

    @ApiOperation(value = "获取当前yml.license解析出来的map.sn", notes = "MAC-->MD5", produces = "application/json")
    @RequestMapping(value = "/getSN", method = RequestMethod.GET)
    public R<String> getSN() {
        return R.ok(LicenseUtils.getYmlSN());
    }

    @ApiOperation(value = "解密(直接读取Config参数中的system.registrkey,再解密后返回)", notes = "", produces = "application/json")
    @RequestMapping(value = "/decrypt", method = RequestMethod.GET)
    public R<String> decrypt() {
        try {
            // 获取加密的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":**********}
            String systemRegistrkey = saConfigMapper.getSystemRegistrkey();
            if (StringUtils.isBlank(systemRegistrkey)) {
                return R.fail("system.registrkey为空");
            }
            // RSA私钥解密为原文
            String decrypt = RSAUtils.decrypt(systemRegistrkey, MyRSA.PRIVATE_KEY);
            return R.ok(decrypt);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "解密+校验硬件(先解密成功返回解密文本,若未注册则返500,注册了但硬件不匹配返402)", notes = "", produces = "application/json")
    @RequestMapping(value = "/checkHard", method = RequestMethod.GET)
    public R<String> checkHard() {
        try {
            // 获取加密的密文CfgKey = 'system.registrkey' 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":**********}
            String systemRegistrkey = saConfigMapper.getSystemRegistrkey();
            if (StringUtils.isBlank(systemRegistrkey)) {
                // 没有就自动生成一行注册信息 sn是init111
                SaConfigPojo saConfigPojo = new SaConfigPojo();
                saConfigPojo.setCfgkey("system.registrkey");
                saConfigPojo.setCfgvalue("CnrUbrA3FnybEgoiZJ7CEiY54rOH7wTiLu7zjfr/Cqg+9queb0jOv3KehWD++4cfRrNbjWwMXZGaR5LATSAj9uJbV6gp0Kg578Z7NroN00+8lYLzRP766nLUV8Y6hmd9vLQw6CMxTMbabMulXOrV6RwBijcop2xqk7+wbPn8ZX0RxyNiBohV85k3YXYjlHb9c2PVteYLwuP3RDUc4CERlYdwAHXxbjnscPp//yWXoRG8s3uO3zWuCfykVlxkoksHW7SG9I5cYv8fKfDy/tWROCOAR9DWIIdKnytMWduY1Z7rNwzdVURRWU/iV76ig78bOK+RiIqXr1j4bxIjAh/rYw==");
                saConfigPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                SaConfigServiceImpl.cleanNull(saConfigPojo);
                SaConfigEntity saConfigEntity = new SaConfigEntity();
                BeanUtils.copyProperties(saConfigPojo, saConfigEntity);
                saConfigMapper.insert(saConfigEntity);
                systemRegistrkey = saConfigPojo.getCfgvalue();
                PrintColor.lv("首次注册,自动生成注册信息，请配置system.registrkey");
                //return R.fail("未注册!system.registrkey为空");
            }
            // 解密Config中'system.registrkey'
            String decryptConfig = RSAUtils.decrypt(systemRegistrkey, MyRSA.PRIVATE_KEY);
            Map<String, String> map = JSON.parseObject(decryptConfig, new TypeReference<Map<String, String>>() {
            });
            String configSN = map.get("sn");
            String ymlSN = LicenseUtils.getYmlSN();
            Long ymlLT = LicenseUtils.getYmlLT();
            long currentTimestamp = System.currentTimeMillis(); // 当前时间戳
            if (currentTimestamp > ymlLT) {
                throw new RuntimeException("装机证书lt已过期！失效时间戳:" + ymlLT);
            }
            if (configSN.equals(ymlSN)) {//对比yml.license解密后的sn
                return R.ok(decryptConfig);
            } else {
                return R.fail(402, "硬件校验失败 configSN:" + configSN + " ymlSN:" + ymlSN);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


//    -------------------------------------------------------------------------------------------------


    @ApiOperation(value = " 获取用户详细信息", notes = "获取用户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaUserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saUserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaUserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_User.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.saUserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增用户", notes = "新增用户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaUserPojo> create(@RequestBody String json) {
        try {
            SaUserPojo saUserPojo = JSONArray.parseObject(json, SaUserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            // 登录用户名校验不能重复(忽略大小写)
            if (saUserService.checkUsername(saUserPojo.getUsername(), null)) {
                throw new BaseBusinessException("用户名已存在");
            }
            Integer isAdmin = loginUser.getIsadmin();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (isAdmin == 0) {
                throw new BaseBusinessException("非管理员无权创建用户");
            }
            if (isAdmin == 1 && (saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() == 1)) {
                throw new BaseBusinessException("普通管理员无权创建管理员用户");
            }
            saUserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saUserPojo.setCreatedate(new Date());   // 创建时间
            saUserPojo.setLister(loginUser.getRealname());   // 制表
            saUserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saUserService.insert(saUserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改用户(禁止传入加密后密码,再次加密)", notes = "修改用户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaUserPojo> update(@RequestBody String json) {
        try {
            SaUserPojo saUserPojo = JSONArray.parseObject(json, SaUserPojo.class);
//            if ("admin".equals(saUserPojo.getUsername())) {
//                throw new BaseBusinessException("禁止修改admin用户的Username");
//            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            // 登录用户名校验不能重复(忽略大小写)
            if (saUserService.checkUsername(saUserPojo.getUsername(), saUserPojo.getId())) {
                throw new BaseBusinessException("用户名已存在");
            }
            String userid = loginUser.getUserid();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0 && !userid.equals(saUserPojo.getId())) {
                throw new BaseBusinessException("非管理员无权修改其他用户信息");
            }
            if (loginUser.getIsadmin() == 1 && saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() != 0) {
                throw new BaseBusinessException("普通管理员无权修改其他管理员信息");
            }
            saUserPojo.setLister(loginUser.getRealname());   // 制表
            saUserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saUserService.update(saUserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "传入id和wxopenid(实际上是传入的是openidToken,后端转为openid后存入Wxopenid)", notes = "修改用户", produces = "application/json")
    @RequestMapping(value = "/updateOpenid", method = RequestMethod.POST)
    public R<SaUserPojo> updateOpenid(@RequestBody String json) {
        try {
            SaUserPojo saUserPojo = JSONArray.parseObject(json, SaUserPojo.class);
            String openidFromOam = getOpenidFromOam(saUserPojo.getWxopenid());
            // 创建openid绑定关系到Sa_JustAuth表
            SaJustauthPojo saJustauthPojo = new SaJustauthPojo();
            saJustauthPojo.setUserid(saUserPojo.getId());
            saJustauthPojo.setUsername(saUserPojo.getUsername());
            saJustauthPojo.setRealname(saUserPojo.getRealname());
            saJustauthPojo.setAuthtype("openid");
            saJustauthPojo.setAuthuuid(openidFromOam);
            saJustauthService.insert(saJustauthPojo);
            return R.ok(this.saUserService.update(saUserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除用户", notes = "删除用户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            SaUserPojo saUserPojo = saUserService.getEntity(key);
            String userid = loginUser.getUserid();
            if ("admin".equals(saUserPojo.getUsername())) {
                throw new BaseBusinessException("禁止删除admin用户");
            }
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0 && !userid.equals(saUserPojo.getId())) {
                throw new BaseBusinessException("非管理员无权删除其他用户信息");
            }
            if (loginUser.getIsadmin() == 1 && saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() != 0) {
                throw new BaseBusinessException("普通管理员无权删除其他管理员信息");
            }
            // 检查Userid是否被引用 (返回使用位置(表名))
//            List<String> usedPlace = saUserService.checkUseridUsed(key);
//            if (CollectionUtils.isNotEmpty(usedPlace)) {
//                throw new BaseBusinessException("禁止删除用户,被以下表引用:" + usedPlace.toString() );
//            }
            return R.ok(this.saUserService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaUserPojo saUserPojo = this.saUserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saUserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

