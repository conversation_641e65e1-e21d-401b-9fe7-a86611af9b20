package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaDictitemEntity;
import inks.sa.common.core.domain.pojo.SaDictitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 字典子表(SaDictitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:35
 */
@Mapper
public interface SaDictitemMapper {


    SaDictitemPojo getEntity(@Param("key") String key);


    List<SaDictitemPojo> getPageList(QueryParam queryParam);

    List<SaDictitemPojo> getList(@Param("Pid") String Pid);


    int insert(SaDictitemEntity saDictitemEntity);


    int update(SaDictitemEntity saDictitemEntity);


    int delete(@Param("key") String key);

}

