package inks.sa.common.core.config.oss.service;

import cn.hutool.core.util.IdUtil;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import io.minio.*;
import io.minio.errors.ErrorResponseException;
import io.minio.http.Method;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;

/**
 * @Description 视频流播放+分片大文件上传
 * <AUTHOR>
 * @CreateTime 2021/5/31 10:28
 */
public class File_VideoController {
    private static final Logger log = LoggerFactory.getLogger(File_VideoController.class);

    @Resource
    private OSSConfigManager configManager;

    @Resource
    private MinioClient minioClient;

    private String BUCKET;
    private String endpoint;
    private String accessKey;
    private String secretKey;

    private boolean initialized = false;
    private boolean minioAvailable = false;

    private static final ConcurrentHashMap<String, ChunkInfo> CHUNK_INFO_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Long> CHUNK_LAST_ACCESS_TIME = new ConcurrentHashMap<>();
    private static final Map<String, MergeProgress> MERGE_PROGRESS_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Future<?>> ACTIVE_MERGE_TASKS = new ConcurrentHashMap<>();


    @Value("${file.upload.temp-dir:${java.io.tmpdir}/file-upload-chunks}")
    private String tempDirBase;
    private Path tempDirPath;

    @Value("${file.upload.expiration-hours:24}")
    private int chunkExpirationHours;

    private ExecutorService mergeExecutor;

    @PostConstruct
    public void init() {
        try {
            this.BUCKET = configManager.getMinioBucket();
            this.endpoint = configManager.getMinioEndpoint();
            this.accessKey = configManager.getMinioAccessKey();
            this.secretKey = configManager.getMinioSecretKey();
            log.info("File_VideoController initialized with bucket: {}, endpoint: {}", BUCKET, endpoint);

            // 初始化用于合并操作的线程池
            mergeExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors()); // 可以根据需要调整线程数
            log.info("Merge executor service initialized.");

        } catch (Exception e) {
            log.error("Error during basic configuration initialization", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (mergeExecutor != null && !mergeExecutor.isShutdown()) {
            log.info("Shutting down merge executor service...");
            mergeExecutor.shutdown();
            try {
                if (!mergeExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    mergeExecutor.shutdownNow();
                    if (!mergeExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error("Merge executor service did not terminate.");
                    }
                }
            } catch (InterruptedException ie) {
                mergeExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("Merge executor service shut down.");
        }
    }


    private synchronized boolean initSystem() {
        if (initialized) {
            return minioAvailable;
        }
        try {
            log.info("Initializing File_VideoController system...");
            try {
                this.tempDirPath = Paths.get(tempDirBase);
                if (!Files.exists(this.tempDirPath)) {
                    Files.createDirectories(this.tempDirPath);
                    log.info("Created temporary directory for chunks: {}", this.tempDirPath.toAbsolutePath());
                } else {
                    log.info("Temporary directory for chunks already exists: {}", this.tempDirPath.toAbsolutePath());
                }
                cleanupExpiredChunks();
            } catch (IOException e) {
                log.error("Failed to initialize temp directory", e);
            }

            try {
                boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(BUCKET).build());
                if (!bucketExists) {
                    log.info("Bucket '{}' does not exist. Creating it...", BUCKET);
                    minioClient.makeBucket(MakeBucketArgs.builder().bucket(BUCKET).build());
                    log.info("Bucket '{}' created successfully.", BUCKET);
                } else {
                    log.info("Bucket '{}' already exists.", BUCKET);
                }
                minioAvailable = true;
                log.info("MinIO client is available");
            } catch (Exception e) {
                log.error("Failed to verify MinIO client availability. File storage operations will be unavailable", e);
                minioAvailable = false;
            }
            initialized = true;
            return minioAvailable;
        } catch (Exception e) {
            log.error("Failed to initialize system", e);
            return false;
        }
    }

    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void cleanupExpiredChunks() {
        log.info("Running scheduled cleanup of expired chunk uploads");
        long now = System.currentTimeMillis();
        long expirationMs = chunkExpirationHours * 60 * 60 * 1000L;

        Iterator<Map.Entry<String, Long>> iterator = CHUNK_LAST_ACCESS_TIME.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            String uploadId = entry.getKey();
            long lastAccess = entry.getValue();

            if (now - lastAccess > expirationMs) {
                ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId); // No remove here, check if merge is active
                if (chunkInfo != null && !ACTIVE_MERGE_TASKS.containsKey(uploadId)) { // Only cleanup if not actively merging
                    CHUNK_INFO_MAP.remove(uploadId);
                    try {
                        FileUtils.deleteDirectory(new File(chunkInfo.getTempDir()));
                        log.info("Cleaned up expired upload temp directory: {} for upload ID: {}",
                                chunkInfo.getTempDir(), uploadId);
                    } catch (IOException e) {
                        log.warn("Failed to delete expired temp directory: {}", chunkInfo.getTempDir(), e);
                    }
                    MERGE_PROGRESS_MAP.remove(uploadId);
                    iterator.remove();
                    log.info("Removed expired upload record for upload ID: {}", uploadId);
                } else if (chunkInfo == null) { // If chunkInfo is gone but last access time exists
                    iterator.remove();
                    MERGE_PROGRESS_MAP.remove(uploadId); // Also clean its progress if any
                    log.info("Removed orphaned expired upload record for upload ID: {}", uploadId);
                }
            }
        }
    }

    @ApiOperation("初始化分片上传")
    @PostMapping("/initMultipartUpload")
    public R<Map<String, Object>> initMultipartUpload(
            @RequestParam("fileName") String fileName,
            @RequestParam("fileSize") Long fileSize,
            @RequestParam("chunkSize") Integer chunkSize,
            @RequestParam(value = "uploadId", required = false) String existingUploadId) {

        if (!StringUtils.hasText(fileName) || fileSize == null || chunkSize == null || fileSize <= 0 || chunkSize <= 0) {
            return R.fail("参数错误: 文件名、文件大小和分片大小必须有效");
        }

        try {
            if (!initialized) initSystem();

            String uploadId = StringUtils.hasText(existingUploadId) ? existingUploadId : UUID.randomUUID().toString();
            ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);

            Map<String, Object> result = new HashMap<>();

            if (chunkInfo != null) {
                //  任务已存在，检查是否与请求参数匹配 (简单检查文件名和大小)
                if (chunkInfo.getOriginalFileName().equals(fileName) && chunkInfo.getTotalSize() == fileSize) {
                    log.info("恢复已存在的分片上传任务. UploadID: {}, 文件名: {}", uploadId, fileName);
                    result.put("resumed", true);
                } else {
                    // ID冲突但文件信息不匹配，视为错误或强制生成新ID
                    log.warn("UploadID {} 已存在但文件信息不匹配. 原文件名: {}, 请求文件名: {}. 生成新任务.",
                            uploadId, chunkInfo.getOriginalFileName(), fileName);
                    uploadId = UUID.randomUUID().toString(); // 生成新的 uploadId
                    chunkInfo = null; // 重置 chunkInfo
                    result.put("resumed", false);
                }
            } else {
                result.put("resumed", false);
            }


            if (chunkInfo == null) { // 创建新任务
                String suffix = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
                String newFileName = IdUtil.fastSimpleUUID() + suffix; // MinIO上的最终文件名
                String objectName = generateObjectPath(newFileName);

                log.info("初始化新的分片上传. 原始文件名: '{}', MinIO对象名: '{}', UploadID: {}",
                        fileName, objectName, uploadId);

                Path tempUploadDir = this.tempDirPath.resolve(uploadId);
                if (!Files.exists(tempUploadDir)) { // 避免重复创建导致错误
                    Files.createDirectories(tempUploadDir);
                }


                chunkInfo = createChunkInfo(fileName, newFileName, objectName, fileSize, chunkSize, tempUploadDir.toString());
                CHUNK_INFO_MAP.put(uploadId, chunkInfo);
            }

            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());

            result.put("uploadId", uploadId);
            result.put("chunkSize", chunkInfo.getChunkSize()); // Use stored chunk size
            result.put("totalChunks", chunkInfo.getTotalChunks());
            result.put("objectName", chunkInfo.getObjectName());
            result.put("uploadedChunks", chunkInfo.getUploadedChunks()); // 返回已上传分片的状态
            result.put("chunkHashes", chunkInfo.getChunkHashes()); // 返回已上传分片的哈希 (如果需要)

            log.info("分片上传初始化/恢复成功. UploadID: {}, 对象: {}", uploadId, chunkInfo.getObjectName());
            return R.ok(result);
        } catch (IOException e) {
            log.error("IO错误: {}", e.getMessage(), e);
            return R.fail("创建临时目录失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("初始化分片上传失败: {}", e.getMessage(), e);
            return R.fail("初始化分片上传失败: " + e.getMessage());
        }
    }


    private String generateObjectPath(String fileName) {
        return "files/" + DateUtils.datePath() + "/" + fileName;
    }

    private ChunkInfo createChunkInfo(String originalFileName, String internalFileName, String objectName, long totalSize,
                                      int chunkSize, String tempDir) {
        ChunkInfo info = new ChunkInfo();
        info.setOriginalFileName(originalFileName);
        info.setFileName(internalFileName); // MinIO 上的文件名
        info.setObjectName(objectName);
        info.setTotalSize(totalSize);
        info.setChunkSize(chunkSize);
        info.setTempDir(tempDir);
        int totalChunks = (int) Math.ceil((double) totalSize / chunkSize);
        info.setTotalChunks(totalChunks);
        info.setUploadedChunks(new boolean[totalChunks]);
        info.setChunkHashes(new String[totalChunks]); // 初始化哈希数组
        return info;
    }


    @ApiOperation("上传分片")
    @PostMapping("/uploadChunk")
    public R<Map<String, Object>> uploadChunk(
            @RequestParam("file") MultipartFile file,
            @RequestParam("uploadId") String uploadId,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("chunkHash") String chunkHash) { // 新增 chunkHash 参数

        if (file.isEmpty()) return R.fail("分片文件不能为空");

        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        if (chunkInfo == null) return R.fail("无效的上传ID: " + uploadId);

        if (chunkNumber < 0 || chunkNumber >= chunkInfo.getTotalChunks()) {
            return R.fail("无效的分片序号: " + chunkNumber + ", 总分片数: " + chunkInfo.getTotalChunks());
        }

        // 如果分片已上传且哈希匹配，则可以跳过 (更完整的断点续传)
        // if (chunkInfo.getUploadedChunks()[chunkNumber] && chunkHash.equals(chunkInfo.getChunkHashes()[chunkNumber])) {
        //     log.debug("分片 #{} 已上传且校验通过，跳过. UploadID: {}", chunkNumber, uploadId);
        //     // ... 返回成功信息
        // }


        try {
            log.debug("上传分片 #{} UploadID: {}", chunkNumber, uploadId);

            File chunkFile = new File(chunkInfo.getTempDir(), "chunk_" + chunkNumber);
            file.transferTo(chunkFile);

            // 校验分片MD5
            String serverChunkHash;
            try (InputStream inputStream = new FileInputStream(chunkFile)) {
                serverChunkHash = DigestUtils.md5Hex(inputStream);
            }
            if (!serverChunkHash.equalsIgnoreCase(chunkHash)) {
                log.warn("分片 #{} 校验失败. UploadID: {}. 前端Hash: {}, 服务端Hash: {}", chunkNumber, uploadId, chunkHash, serverChunkHash);
                FileUtils.deleteQuietly(chunkFile); // 删除校验失败的分片
                return R.fail(Collections.singletonMap("chunkNumber", chunkNumber), "分片校验失败，请重试");
            }
            log.debug("分片 #{} 校验成功. UploadID: {}", chunkNumber, uploadId);


            chunkInfo.getUploadedChunks()[chunkNumber] = true;
            chunkInfo.getChunkHashes()[chunkNumber] = serverChunkHash; // 存储服务端计算的哈希
            CHUNK_LAST_ACCESS_TIME.put(uploadId, System.currentTimeMillis());

            int uploadedCount = countUploadedChunks(chunkInfo.getUploadedChunks());
            log.debug("分片 #{} 上传完成. 已上传: {}/{} UploadID: {}", chunkNumber, uploadedCount, chunkInfo.getTotalChunks(), uploadId);

            Map<String, Object> result = new HashMap<>();
            result.put("uploadedChunks", uploadedCount);
            result.put("totalChunks", chunkInfo.getTotalChunks());
            result.put("isCompleted", uploadedCount == chunkInfo.getTotalChunks());

            return R.ok(result);
        } catch (IOException e) {
            log.error("IO错误: {}", e.getMessage(), e);
            return R.fail("分片保存失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("上传分片 #{} 失败 UploadID {}: {}", chunkNumber, uploadId, e.getMessage(), e);
            return R.fail("上传分片失败: " + e.getMessage());
        }
    }

    private int countUploadedChunks(boolean[] uploadedChunks) {
        int count = 0;
        for (boolean uploaded : uploadedChunks) {
            if (uploaded) count++;
        }
        return count;
    }

    @ApiOperation("完成分片上传（异步）")
    @PostMapping("/completeMultipartUpload")
    public R<Map<String, Object>> completeMultipartUpload(@RequestParam("uploadId") String uploadId) {
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);
        if (chunkInfo == null) {
            return R.fail("无效的上传ID: " + uploadId);
        }
        log.info("请求完成分片上传. UploadID: {}, 对象: {}", uploadId, chunkInfo.getObjectName());

        // 检查是否已在处理
        if (ACTIVE_MERGE_TASKS.containsKey(uploadId) && !ACTIVE_MERGE_TASKS.get(uploadId).isDone()) {
            log.warn("上传ID {} 的合并任务已在进行中.", uploadId);
            return R.fail("合并任务已在进行中，请通过getMergeProgress查询进度。");
        }

        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "QUEUED", 0, null, false));

        Future<?> mergeTask = mergeExecutor.submit(() -> {
            Thread.currentThread().setName("merge-upload-" + uploadId);
            boolean success = false;
            try {
                // 检查所有分片是否已上传
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "CHECKING", 5, null, false));
                for (int i = 0; i < chunkInfo.getTotalChunks(); i++) {
                    if (Thread.currentThread().isInterrupted()) throw new InterruptedException("任务被取消(检查分片时)");
                    if (!chunkInfo.getUploadedChunks()[i]) {
                        log.warn("尝试完成上传 {} 但分片 {} 尚未上传.", uploadId, i);
                        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(i, chunkInfo.getTotalChunks(), "ERROR", 0, "分片 " + i + " 尚未上传", false));
                        return; // 异步任务终止
                    }
                }

                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(0, chunkInfo.getTotalChunks(), "MERGING", 10, null, false));
                File mergedFile = new File(chunkInfo.getTempDir(), chunkInfo.getFileName()); // 使用MinIO的文件名
                log.info("开始合并分片 UploadID: {} 到 {}", uploadId, mergedFile.getAbsolutePath());

                try (FileOutputStream fos = new FileOutputStream(mergedFile);
                     BufferedOutputStream bos = new BufferedOutputStream(fos)) {
                    for (int i = 0; i < chunkInfo.getTotalChunks(); i++) {
                        if (Thread.currentThread().isInterrupted()) throw new InterruptedException("任务被取消(合并文件时)");
                        int progress = 10 + (i * 60) / chunkInfo.getTotalChunks(); // 合并占60%进度
                        MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(i + 1, chunkInfo.getTotalChunks(), "MERGING", progress, null, false));
                        File chunkFile = new File(chunkInfo.getTempDir(), "chunk_" + i);
                        Files.copy(chunkFile.toPath(), bos);
                    }
                }
                log.info("分片合并完成 UploadID: {}. 大小: {}", uploadId, mergedFile.length());

                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "PREPARING_UPLOAD", 75, null, false));
                String contentType = Files.probeContentType(mergedFile.toPath());
                if (contentType == null || (!contentType.startsWith("video/") && !contentType.startsWith("application/octet-stream"))) {
                    String fileNameLower = chunkInfo.getFileName().toLowerCase();
                    if (fileNameLower.endsWith(".mp4")) contentType = "video/mp4";
                    else if (fileNameLower.endsWith(".webm")) contentType = "video/webm";
                    else if (fileNameLower.endsWith(".ogv")) contentType = "video/ogg";
                    else contentType = "application/octet-stream"; // 默认
                }
                log.info("确定内容类型为 {} 对于 UploadID: {}", contentType, uploadId);

                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "UPLOADING_TO_MINIO", 80, null, false));
                try (InputStream is = Files.newInputStream(mergedFile.toPath())) {
                    if (!minioAvailable) initSystem(); // 确保MinIO可用
                    if (minioAvailable) {
                        minioClient.putObject(
                                PutObjectArgs.builder()
                                        .bucket(BUCKET)
                                        .object(chunkInfo.getObjectName())
                                        .contentType(contentType)
                                        .stream(is, mergedFile.length(), -1)
                                        .build()
                        );
                    } else {
                        log.error("MinIO客户端未初始化，无法上传文件 UploadID: {}", uploadId);
                        throw new RuntimeException("MinIO客户端未初始化");
                    }
                }
                log.info("合并文件 {} 上传到MinIO作为 {} (UploadID: {})", mergedFile.getName(), chunkInfo.getObjectName(), uploadId);
                success = true;
            } catch (InterruptedException e) {
                log.warn("合并/上传任务被取消 UploadID: {}", uploadId, e);
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(
                        MERGE_PROGRESS_MAP.getOrDefault(uploadId, new MergeProgress()).getCurrent(),
                        chunkInfo.getTotalChunks(),
                        "CANCELLED",
                        MERGE_PROGRESS_MAP.getOrDefault(uploadId, new MergeProgress()).getPercent(),
                        "任务被用户取消", true));
            } catch (Exception e) {
                log.error("异步完成分片上传失败 UploadID {}: {}", uploadId, e.getMessage(), e);
                MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(
                        MERGE_PROGRESS_MAP.getOrDefault(uploadId, new MergeProgress()).getCurrent(),
                        chunkInfo.getTotalChunks(),
                        "ERROR",
                        MERGE_PROGRESS_MAP.getOrDefault(uploadId, new MergeProgress()).getPercent(),
                        "合并或上传失败: " + e.getMessage(), false));
            } finally {
                if (success) {
                    MERGE_PROGRESS_MAP.put(uploadId, new MergeProgress(chunkInfo.getTotalChunks(), chunkInfo.getTotalChunks(), "COMPLETED", 100, null, false));
                }

                // 清理临时目录和映射条目，无论成功、失败还是取消
                // 但保留 CHUNK_INFO_MAP 和 CHUNK_LAST_ACCESS_TIME 以便后续查询或清理过期任务
                // MERGE_PROGRESS_MAP 中的最终状态 (COMPLETED/ERROR/CANCELLED) 会保留一段时间
                try {
                    FileUtils.deleteDirectory(new File(chunkInfo.getTempDir()));
                    log.info("清理临时目录: {} (UploadID: {})", chunkInfo.getTempDir(), uploadId);
                } catch (IOException e) {
                    log.warn("无法清理临时目录 {}: {} (UploadID: {})", chunkInfo.getTempDir(), e.getMessage(), uploadId);
                }

                // 移除活跃任务标记
                ACTIVE_MERGE_TASKS.remove(uploadId);
                log.info("移除 UploadID {} 的活跃合并任务标记.", uploadId);

                // 5分钟后清理MERGE_PROGRESS_MAP中的此条目
                final String finalUploadId = uploadId; // for use in lambda
                new Timer().schedule(new TimerTask() {
                    @Override
                    public void run() {
                        MERGE_PROGRESS_MAP.remove(finalUploadId);
                        // 同时清理 CHUNK_INFO_MAP 和 CHUNK_LAST_ACCESS_TIME，因为此时合并流程已结束
                        CHUNK_INFO_MAP.remove(finalUploadId);
                        CHUNK_LAST_ACCESS_TIME.remove(finalUploadId);
                        log.info("延时清理完成: 移除合并进度、分片信息和最后访问时间 for UploadID: {}", finalUploadId);
                    }
                }, 5 * 60 * 1000);
            }
        });
        ACTIVE_MERGE_TASKS.put(uploadId, mergeTask);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "文件合并与上传任务已启动");
        response.put("uploadId", uploadId);
        return R.ok(response);
    }

    @ApiOperation("获取合并进度")
    @GetMapping("/getMergeProgress")
    public R<Map<String, Object>> getMergeProgress(@RequestParam("uploadId") String uploadId) {
        MergeProgress progress = MERGE_PROGRESS_MAP.get(uploadId);
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId); // 用于获取 objectName 和 bucket

        if (progress == null) {
            return R.fail("没有找到上传ID对应的进度: " + uploadId);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("current", progress.getCurrent());
        result.put("total", progress.getTotal());
        result.put("status", progress.getStatus());
        result.put("percent", progress.getPercent());
        result.put("errorMessage", progress.getErrorMessage());
        result.put("cancelled", progress.isCancelled());

        if ("COMPLETED".equals(progress.getStatus()) && chunkInfo != null) {
            try {
                String url = getPresignedObjectUrl(chunkInfo.getObjectName(), 7);
                result.put("url", url);
                result.put("objectName", chunkInfo.getObjectName());
                result.put("fileName", chunkInfo.getFileName()); // MinIO上的文件名
                result.put("originalFileName", chunkInfo.getOriginalFileName());
                // contentType 和 size 在此阶段不易直接获取，除非在MergeProgress中存储，或再次查询MinIO
                // 为简化，可以让前端在成功后自行处理这些信息或从complete的最终响应中获取
                result.put("bucket", BUCKET);
            } catch (Exception e) {
                log.warn("获取COMPLETED状态文件的预签名URL失败 UploadID: {}", uploadId, e);
                result.put("url", "Error generating URL");
            }
        }
        return R.ok(result);
    }


    @ApiOperation("取消分片上传")
    @PostMapping("/cancelMultipartUpload")
    public R<String> cancelMultipartUpload(@RequestParam("uploadId") String uploadId) {
        log.info("请求取消上传任务. UploadID: {}", uploadId);
        ChunkInfo chunkInfo = CHUNK_INFO_MAP.get(uploadId);

        if (chunkInfo == null && !MERGE_PROGRESS_MAP.containsKey(uploadId) && !ACTIVE_MERGE_TASKS.containsKey(uploadId)) {
            return R.fail("无效或已完成/清理的上传ID: " + uploadId);
        }

        // 尝试中断正在进行的合并任务
        Future<?> mergeTask = ACTIVE_MERGE_TASKS.get(uploadId);
        if (mergeTask != null && !mergeTask.isDone()) {
            log.info("尝试取消正在进行的合并任务. UploadID: {}", uploadId);
            mergeTask.cancel(true); // true to interrupt the running thread
            ACTIVE_MERGE_TASKS.remove(uploadId);
        }

        // 更新/设置进度为已取消
        MergeProgress currentProgress = MERGE_PROGRESS_MAP.getOrDefault(uploadId, new MergeProgress(0, chunkInfo != null ? chunkInfo.getTotalChunks() : 0, "CANCELLED", 0, "用户取消", true));
        currentProgress.setStatus("CANCELLED");
        currentProgress.setErrorMessage("用户取消");
        currentProgress.setCancelled(true);
        MERGE_PROGRESS_MAP.put(uploadId, currentProgress);

        // 清理资源
        if (chunkInfo != null) {
            try {
                FileUtils.deleteDirectory(new File(chunkInfo.getTempDir()));
                log.info("已删除临时目录: {} for UploadID: {}", chunkInfo.getTempDir(), uploadId);
            } catch (IOException e) {
                log.warn("取消时删除临时目录失败 {}: {}", chunkInfo.getTempDir(), e.getMessage());
            }
        }
        CHUNK_INFO_MAP.remove(uploadId);
        CHUNK_LAST_ACCESS_TIME.remove(uploadId);
        // MERGE_PROGRESS_MAP 中的条目会在其定时器或上述更新后被保留一段时间，然后由定时清理任务处理

        log.info("上传任务已取消. UploadID: {}", uploadId);
        return R.ok("上传任务已取消");
    }


    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @ApiOperation("从MinIO流式播放")
    @GetMapping("stream/**")
    public void streamFromMinio(HttpServletRequest request, HttpServletResponse response) {
        // ... (原有代码不变)
        try {
            String uri = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
            String pattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
            String objectName = antPathMatcher.extractPathWithinPattern(pattern, uri);

            log.info("尝试流式传输文件: URI='{}', 对象名='{}'", request.getRequestURI(), objectName);

            if (!initialized) initSystem();
            if (!minioAvailable) {
                sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "MinIO客户端初始化失败");
                return;
            }

            ObjectStat stat = minioClient.statObject(
                    StatObjectArgs.builder().bucket(BUCKET).object(objectName).build());
            long fileSize = stat.length();
            String contentType = stat.contentType();
            handleRangeRequest(request, response, objectName, fileSize, contentType);

        } catch (IllegalArgumentException e) {
            log.warn("无效的请求路径: {}", e.getMessage());
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, e.getMessage());
        } catch (ErrorResponseException e) {
            if (e.errorResponse().errorCode().equals("NoSuchKey")) {
                log.warn("请求的对象不存在: {}", request.getRequestURI());
                sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, "找不到请求的文件");
            } else {
                log.error("MinIO错误: {}", e.getMessage(), e);
                sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "MinIO服务器错误");
            }
        } catch (Exception e) {
            log.error("流式传输时出错: {}", e.getMessage(), e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "流式传输出错");
        }
    }

    private void handleRangeRequest(HttpServletRequest request, HttpServletResponse response,
                                    String objectName, long fileSize, String contentType) throws Exception {
        // ... (原有代码不变)
        Range range = parseRangeHeader(request, fileSize);
        response.setContentType(contentType);
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("ETag", "W/\"" + objectName.hashCode() + "\""); // Simple ETag
        long contentLength;

        if (range != null) {
            long startPos = range.start;
            long endPos = range.end;
            contentLength = endPos - startPos + 1;
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            response.setHeader("Content-Range", "bytes " + startPos + "-" + endPos + "/" + fileSize);
        } else {
            contentLength = fileSize;
            response.setStatus(HttpServletResponse.SC_OK);
        }
        response.setHeader("Content-Length", String.valueOf(contentLength));
        if (!"HEAD".equals(request.getMethod())) {
            streamObjectContent(objectName, response, range, contentLength);
        }
    }

    private Range parseRangeHeader(HttpServletRequest request, long fileSize) {
        // ... (原有代码不变)
        String rangeHeader = request.getHeader("Range");
        if (rangeHeader == null || !rangeHeader.startsWith("bytes=")) return null;
        try {
            String rangeValue = rangeHeader.substring("bytes=".length());
            String[] ranges = rangeValue.split(",")[0].trim().split("-"); // Only first range
            long startPos = ranges[0].isEmpty() ? 0 : Long.parseLong(ranges[0]);
            long endPos = (ranges.length > 1 && !ranges[1].isEmpty()) ? Long.parseLong(ranges[1]) : fileSize - 1;
            if (startPos < 0 || endPos >= fileSize || startPos > endPos) return null;
            return new Range(startPos, endPos);
        } catch (NumberFormatException e) {
            log.warn("解析范围头时出错: {}", rangeHeader, e);
            return null;
        }
    }

    private void streamObjectContent(String objectName, HttpServletResponse response,
                                     Range range, long contentLength) throws Exception {
        // ... (原有代码不变)
        InputStream objectStream = null;
        try {
            if (!minioAvailable) initSystem();
            if (!minioAvailable) {
                sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "MinIO客户端未初始化");
                return;
            }

            GetObjectArgs.Builder argsBuilder = GetObjectArgs.builder()
                    .bucket(BUCKET)
                    .object(objectName);
            if (range != null) {
                argsBuilder.offset(range.start).length(contentLength);
            }
            objectStream = minioClient.getObject(argsBuilder.build());

            byte[] buffer = new byte[8192]; // 8KB buffer
            int bytesRead;
            OutputStream outputStream = response.getOutputStream();
            while ((bytesRead = objectStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        } finally {
            IOUtils.closeQuietly(objectStream);
        }
    }

    private void sendErrorResponse(HttpServletResponse response, int statusCode, String message) {
        // ... (原有代码不变)
        try {
            if (!response.isCommitted()) {
                response.sendError(statusCode, message);
            }
        } catch (IOException e) {
            log.error("发送错误响应时出错", e);
        }
    }

    private static class Range {
        // ... (原有代码不变)
        private final long start;
        private final long end;
        public Range(long start, long end) { this.start = start; this.end = end; }
        public long getStart() { return start; }
        public long getEnd() { return end; }
    }

    @ApiOperation("获取缩略图")
    @GetMapping("/thumbnail")
    public void getThumbnail(@RequestParam("objectName") String objectName, HttpServletResponse response) {
        // ... (原有代码不变)
        if (!StringUtils.hasText(objectName)) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, "对象名不能为空");
            return;
        }
        log.info("请求缩略图 对象: {}", objectName);
        try {
            response.setContentType("image/png");
            try (InputStream is = getClass().getResourceAsStream("/static/images/default-video-thumbnail.png")) {
                if (is != null) {
                    IOUtils.copy(is, response.getOutputStream());
                } else {
                    log.warn("默认缩略图 '/static/images/default-video-thumbnail.png' 未找到.");
                    sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, "默认缩略图未找到");
                }
            }
        } catch (Exception e) {
            log.error("获取缩略图失败 对象 {}: {}", objectName, e.getMessage(), e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "获取缩略图失败.");
        }
    }

    @ApiOperation("生成预签名URL")
    @GetMapping("/presignedUrl")
    public R<String> getPresignedUrl(
            @RequestParam("objectName") String objectName,
            @RequestParam(value = "expireTimeDays", defaultValue = "7") Integer expireTimeDays) {
        // ... (原有代码不变)
        try {
            String decodedObjectName = URLDecoder.decode(objectName, StandardCharsets.UTF_8.name());
            log.info("生成预签名URL 对象: {}, 解码: {}, 过期: {} 天", objectName, decodedObjectName, expireTimeDays);
            String url = getPresignedObjectUrl(decodedObjectName, expireTimeDays);
            return R.ok(url);
        } catch (Exception e) {
            log.error("生成临时访问URL失败 对象 {}: {}", objectName, e.getMessage(), e);
            return R.fail("生成临时访问URL失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取服务器配置信息")
    @GetMapping("/getServerConfig")
    public R<Map<String, Object>> getServerConfig() {
        // ... (原有代码不变)
        Map<String, Object> config = new HashMap<>();
        config.put("bucket", BUCKET);
        return R.ok(config);
    }

    private String getPresignedObjectUrl(String objectName, int expireTimeDays) throws Exception {
        // ... (原有代码不变)
        if (!minioAvailable) initSystem();
        if (minioAvailable) {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(BUCKET)
                            .object(objectName)
                            .expiry(expireTimeDays * 24 * 3600) // seconds
                            .build());
        } else {
            log.error("MinIO客户端未初始化，无法生成预签名URL");
            throw new RuntimeException("MinIO客户端未初始化");
        }
    }

    @Data @NoArgsConstructor @AllArgsConstructor
    public static class ChunkInfo {
        private String originalFileName; // 用户上传的原始文件名
        private String fileName;         // MinIO上存储的文件名 (通常是UUID化的)
        private String objectName;
        private long totalSize;
        private int chunkSize;
        private int totalChunks;
        private boolean[] uploadedChunks;
        private String[] chunkHashes; // 新增：存储每个分片的哈希值
        private String tempDir;
    }

    @Data @NoArgsConstructor @AllArgsConstructor
    public static class MergeProgress {
        private int current;
        private int total;
        private String status; // e.g., QUEUED, CHECKING, MERGING, UPLOADING_TO_MINIO, COMPLETED, ERROR, CANCELLED
        private int percent;
        private String errorMessage;
        private boolean cancelled; // 新增：标记是否被取消
    }
}