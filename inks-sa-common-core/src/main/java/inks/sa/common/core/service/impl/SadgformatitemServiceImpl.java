package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SadgformatitemEntity;
import inks.sa.common.core.domain.pojo.SadgformatitemPojo;
import inks.sa.common.core.mapper.SadgformatitemMapper;
import inks.sa.common.core.service.SadgformatitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 列表项目(Sadgformatitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 09:55:09
 */
@Service("sadgformatitemService")
public class SadgformatitemServiceImpl implements SadgformatitemService {
    @Resource
    private SadgformatitemMapper sadgformatitemMapper;


    @Override
    public SadgformatitemPojo getEntity(String key, String tid) {
        return this.sadgformatitemMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<SadgformatitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SadgformatitemPojo> lst = sadgformatitemMapper.getPageList(queryParam);
            PageInfo<SadgformatitemPojo> pageInfo = new PageInfo<SadgformatitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public List<SadgformatitemPojo> getList(String Pid, String tid) {
        try {
            List<SadgformatitemPojo> lst = sadgformatitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param sadgformatitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SadgformatitemPojo insert(SadgformatitemPojo sadgformatitemPojo) {
        //初始化item的NULL
        SadgformatitemPojo itempojo = this.clearNull(sadgformatitemPojo);
        SadgformatitemEntity sadgformatitemEntity = new SadgformatitemEntity();
        BeanUtils.copyProperties(itempojo, sadgformatitemEntity);

        sadgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        sadgformatitemEntity.setRevision(1);  //乐观锁
        this.sadgformatitemMapper.insert(sadgformatitemEntity);
        return this.getEntity(sadgformatitemEntity.getId(), sadgformatitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param sadgformatitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SadgformatitemPojo update(SadgformatitemPojo sadgformatitemPojo) {
        SadgformatitemEntity sadgformatitemEntity = new SadgformatitemEntity();
        BeanUtils.copyProperties(sadgformatitemPojo, sadgformatitemEntity);
        this.sadgformatitemMapper.update(sadgformatitemEntity);
        return this.getEntity(sadgformatitemEntity.getId(), sadgformatitemEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.sadgformatitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param sadgformatitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SadgformatitemPojo clearNull(SadgformatitemPojo sadgformatitemPojo) {
        //初始化NULL字段
        if (sadgformatitemPojo.getPid() == null) sadgformatitemPojo.setPid("");
        if (sadgformatitemPojo.getItemcode() == null) sadgformatitemPojo.setItemcode("");
        if (sadgformatitemPojo.getItemname() == null) sadgformatitemPojo.setItemname("");
        if (sadgformatitemPojo.getDefwidth() == null) sadgformatitemPojo.setDefwidth("");
        if (sadgformatitemPojo.getMinwidth() == null) sadgformatitemPojo.setMinwidth("");
        if (sadgformatitemPojo.getDisplaymark() == null) sadgformatitemPojo.setDisplaymark(0);
        if (sadgformatitemPojo.getFixed() == null) sadgformatitemPojo.setFixed(0);
        if (sadgformatitemPojo.getSortable() == null) sadgformatitemPojo.setSortable(0);
        if (sadgformatitemPojo.getOrderfield() == null) sadgformatitemPojo.setOrderfield("");
        if (sadgformatitemPojo.getOverflow() == null) sadgformatitemPojo.setOverflow(0);
        if (sadgformatitemPojo.getFormatter() == null) sadgformatitemPojo.setFormatter("");
        if (sadgformatitemPojo.getClassname() == null) sadgformatitemPojo.setClassname("");
        if (sadgformatitemPojo.getAligntype() == null) sadgformatitemPojo.setAligntype("");
        if (sadgformatitemPojo.getEventname() == null) sadgformatitemPojo.setEventname("");
        if (sadgformatitemPojo.getEditmark() == null) sadgformatitemPojo.setEditmark(0);
        if (sadgformatitemPojo.getOperationmark() == null) sadgformatitemPojo.setOperationmark(0);
        if (sadgformatitemPojo.getRownum() == null) sadgformatitemPojo.setRownum(0);
        if (sadgformatitemPojo.getRemark() == null) sadgformatitemPojo.setRemark("");
        if (sadgformatitemPojo.getTenantid() == null) sadgformatitemPojo.setTenantid("");
        if (sadgformatitemPojo.getRevision() == null) sadgformatitemPojo.setRevision(0);
        return sadgformatitemPojo;
    }
}
