package inks.sa.common.core.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaLoginlogPojo;
import inks.sa.common.core.domain.SaLoginlogEntity;
import inks.sa.common.core.mapper.SaLoginlogMapper;
import inks.sa.common.core.service.SaLoginlogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 登录日志(SaLoginlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-24 10:10:27
 */
@Service("saLoginlogService")
public class SaLoginlogServiceImpl implements SaLoginlogService {
    @Resource
    private SaLoginlogMapper saLoginlogMapper;

    @Override
    public SaLoginlogPojo getEntity(String key) {
        return this.saLoginlogMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaLoginlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLoginlogPojo> lst = saLoginlogMapper.getPageList(queryParam);
            PageInfo<SaLoginlogPojo> pageInfo = new PageInfo<SaLoginlogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaLoginlogPojo insert(SaLoginlogPojo saLoginlogPojo) {
        //初始化NULL字段
        cleanNull(saLoginlogPojo);
        SaLoginlogEntity saLoginlogEntity = new SaLoginlogEntity(); 
        BeanUtils.copyProperties(saLoginlogPojo,saLoginlogEntity);
        //生成雪花id
          saLoginlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          this.saLoginlogMapper.insert(saLoginlogEntity);
        return this.getEntity(saLoginlogEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saLoginlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLoginlogPojo update(SaLoginlogPojo saLoginlogPojo) {
        SaLoginlogEntity saLoginlogEntity = new SaLoginlogEntity(); 
        BeanUtils.copyProperties(saLoginlogPojo,saLoginlogEntity);
        this.saLoginlogMapper.update(saLoginlogEntity);
        return this.getEntity(saLoginlogEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saLoginlogMapper.delete(key) ;
    }
    

    private static void cleanNull(SaLoginlogPojo saLoginlogPojo) {
        if(saLoginlogPojo.getUserid()==null) saLoginlogPojo.setUserid("");
        if(saLoginlogPojo.getUsername()==null) saLoginlogPojo.setUsername("");
        if(saLoginlogPojo.getRealname()==null) saLoginlogPojo.setRealname("");
        if(saLoginlogPojo.getIpaddr()==null) saLoginlogPojo.setIpaddr("");
        if(saLoginlogPojo.getLoginlocation()==null) saLoginlogPojo.setLoginlocation("");
        if(saLoginlogPojo.getBrowsername()==null) saLoginlogPojo.setBrowsername("");
        if(saLoginlogPojo.getHostsystem()==null) saLoginlogPojo.setHostsystem("");
        if(saLoginlogPojo.getDirection()==null) saLoginlogPojo.setDirection("");
        if(saLoginlogPojo.getLoginstatus()==null) saLoginlogPojo.setLoginstatus(0);
        if(saLoginlogPojo.getLoginmsg()==null) saLoginlogPojo.setLoginmsg("");
        if(saLoginlogPojo.getLogintime()==null) saLoginlogPojo.setLogintime(new Date());
        if(saLoginlogPojo.getTenantid()==null) saLoginlogPojo.setTenantid("");
        if(saLoginlogPojo.getTenantname()==null) saLoginlogPojo.setTenantname("");
   }

}
