package inks.sa.common.core.service.impl;

import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.SaJustauthEntity;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.mapper.SaJustauthMapper;
import inks.sa.common.core.service.SaJustauthService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 第三方登录(SaJustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:02:00
 */
@Service("saJustauthService")
public class SaJustauthServiceImpl implements SaJustauthService {
    @Resource
    private SaJustauthMapper saJustauthMapper;

    @Override
    public SaJustauthPojo getEntity(String key) {
        return this.saJustauthMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaJustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaJustauthPojo> lst = saJustauthMapper.getPageList(queryParam);
            PageInfo<SaJustauthPojo> pageInfo = new PageInfo<SaJustauthPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaJustauthPojo insert(SaJustauthPojo saJustauthPojo) {
        // 检查ding/wxe/openid是否已存在
        if (isNotBlank(saJustauthPojo.getAuthuuid())) {
            SaJustauthPojo justauthPojo = saJustauthMapper.getEntityByAuthuuid(saJustauthPojo.getAuthuuid());
            if (justauthPojo != null) {
                throw new BaseBusinessException("该AuthUuid已绑定账号:" + justauthPojo.getUsername());
            }
        }
        //初始化NULL字段
        cleanNull(saJustauthPojo);
        SaJustauthEntity saJustauthEntity = new SaJustauthEntity(); 
        BeanUtils.copyProperties(saJustauthPojo,saJustauthEntity);
        //生成雪花id
          saJustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saJustauthEntity.setRevision(1);  //乐观锁
          this.saJustauthMapper.insert(saJustauthEntity);
        return this.getEntity(saJustauthEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saJustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaJustauthPojo update(SaJustauthPojo saJustauthPojo) {
        SaJustauthEntity saJustauthEntity = new SaJustauthEntity(); 
        BeanUtils.copyProperties(saJustauthPojo,saJustauthEntity);
        this.saJustauthMapper.update(saJustauthEntity);
        return this.getEntity(saJustauthEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saJustauthMapper.delete(key) ;
    }

    @Override
    public int deleteByAuthUuid(String authuuid) {
        return this.saJustauthMapper.deleteByAuthUuid(authuuid) ;
    }

    private static void cleanNull(SaJustauthPojo saJustauthPojo) {
        if(saJustauthPojo.getUserid()==null) saJustauthPojo.setUserid("");
        if(saJustauthPojo.getUsername()==null) saJustauthPojo.setUsername("");
        if(saJustauthPojo.getRealname()==null) saJustauthPojo.setRealname("");
        if(saJustauthPojo.getNickname()==null) saJustauthPojo.setNickname("");
        if(saJustauthPojo.getAuthtype()==null) saJustauthPojo.setAuthtype("");
        if(saJustauthPojo.getAuthuuid()==null) saJustauthPojo.setAuthuuid("");
        if(saJustauthPojo.getUnionid()==null) saJustauthPojo.setUnionid("");
        if(saJustauthPojo.getAuthavatar()==null) saJustauthPojo.setAuthavatar("");
        if(saJustauthPojo.getCreateby()==null) saJustauthPojo.setCreateby("");
        if(saJustauthPojo.getCreatebyid()==null) saJustauthPojo.setCreatebyid("");
        if(saJustauthPojo.getCreatedate()==null) saJustauthPojo.setCreatedate(new Date());
        if(saJustauthPojo.getLister()==null) saJustauthPojo.setLister("");
        if(saJustauthPojo.getListerid()==null) saJustauthPojo.setListerid("");
        if(saJustauthPojo.getModifydate()==null) saJustauthPojo.setModifydate(new Date());
        if(saJustauthPojo.getCustom1()==null) saJustauthPojo.setCustom1("");
        if(saJustauthPojo.getCustom2()==null) saJustauthPojo.setCustom2("");
        if(saJustauthPojo.getCustom3()==null) saJustauthPojo.setCustom3("");
        if(saJustauthPojo.getCustom4()==null) saJustauthPojo.setCustom4("");
        if(saJustauthPojo.getCustom5()==null) saJustauthPojo.setCustom5("");
        if(saJustauthPojo.getTenantid()==null) saJustauthPojo.setTenantid("");
        if(saJustauthPojo.getTenantname()==null) saJustauthPojo.setTenantname("");
        if(saJustauthPojo.getRevision()==null) saJustauthPojo.setRevision(0);
   }

    @Override
    public SaJustauthPojo getJustauthByUuid(String key, String type, String tid) {

        return this.saJustauthMapper.getJustauthByUuid(key, type, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaJustauthPojo getJustauthByUserid(String key, String type, String tid) {
        return this.saJustauthMapper.getJustauthByUserid(key, type, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public   List<JustauthPojo> getAdminListByDeptid(String key, String type, String tid) {
        return this.saJustauthMapper.getAdminListByDeptid(key, type, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public  List<JustauthPojo> getListByUnionid( String key){
        return this.saJustauthMapper.getListByUnionid(key);
    }
}
