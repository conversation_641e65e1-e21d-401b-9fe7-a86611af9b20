package inks.sa.common.core.annotation.aspect;

import inks.common.core.domain.LoginUser;
import inks.common.core.exception.PreAuthorizeException;
import inks.common.core.utils.StringUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.service.SaRedisService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.PatternMatchUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Collection;

/**
 * Sa_Redis版本权限注解
 */
@Aspect
@Component
public class Sa_PreAuthorizeAspect {
    private static final String ALL_PERMISSION = "*.*";
    private static final String SUPER_ADMIN = "admin";
    private static final Integer ARRAY_EMPTY = 0;
    @Resource
    private SaRedisService saRedisService;

    public Sa_PreAuthorizeAspect() {
    }

    @Around("@annotation(inks.sa.common.core.annotation.PreAuthorize)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Signature signature = point.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        PreAuthorize annotation = method.getAnnotation(PreAuthorize.class);
        if (annotation == null) {
            return point.proceed();
        } else if (StringUtils.isNotEmpty(annotation.hasPermi())) {
            if (this.hasPermi(annotation.hasPermi())) {
                return point.proceed();
            } else {
                LoginUser userInfo = this.saRedisService.getLoginUser();
                if (StringUtils.isNull(userInfo)) {
                    throw new PreAuthorizeException("userinfo is null");
                } else {
                    throw new PreAuthorizeException();
                }
            }
        } else if (StringUtils.isNotEmpty(annotation.lacksPermi())) {
            if (this.lacksPermi(annotation.lacksPermi())) {
                return point.proceed();
            } else {
                throw new PreAuthorizeException();
            }
        } else if (ARRAY_EMPTY < annotation.hasAnyPermi().length) {
            if (this.hasAnyPermi(annotation.hasAnyPermi())) {
                return point.proceed();
            } else {
                throw new PreAuthorizeException();
            }
        } else {
            return point.proceed();
        }
    }

    public boolean hasPermi(String permission) {
        LoginUser userInfo = this.saRedisService.getLoginUser();
        if (StringUtils.isNull(userInfo)) {
            return false;
        } else if (userInfo.getIsadmin() != null && (userInfo.getIsadmin() == 1 || userInfo.getIsadmin() == 2)) {//2:超管
            return true;
        } else {
            return !CollectionUtils.isEmpty(userInfo.getPermissions()) && this.hasPermissions(userInfo.getPermissions(), permission);
        }
    }

    public boolean hasPermiCode(LoginUser loginUser, String permission) {
        if (StringUtils.isNull(loginUser)) {
            return false;
        } else {
            return !CollectionUtils.isEmpty(loginUser.getPermissions()) && this.hasPermissions(loginUser.getPermissions(), permission);
        }
    }

    public boolean lacksPermi(String permission) {
        return !this.hasPermi(permission);
    }

    public boolean hasAnyPermi(String[] permissions) {
        LoginUser userInfo = this.saRedisService.getLoginUser();
        if (!StringUtils.isNull(userInfo) && !CollectionUtils.isEmpty(userInfo.getPermissions())) {
            Collection<String> authorities = userInfo.getPermissions();

            for (String permission : permissions) {
                if (permission != null && this.hasPermissions(authorities, permission)) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }

    private boolean hasPermissions(Collection<String> authorities, String permission) {
        return authorities.stream().filter(StringUtils::hasText).anyMatch((x) -> {
            return PatternMatchUtils.simpleMatch(x, permission);
        });
    }
}
