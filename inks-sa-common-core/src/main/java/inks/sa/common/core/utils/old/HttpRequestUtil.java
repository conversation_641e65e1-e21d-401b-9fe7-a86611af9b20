//package inks.sa.common.core.utils;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.*;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.nio.charset.StandardCharsets;
//import java.util.Map;
//
///**
// * 发送http请求工具
// */
//public class HttpRequestUtil {
//
//
//    private final static Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);
//
//    public static String postRequest(String url, Map<String, String> headers, String params) {
//        OutputStreamWriter out = null;
//        BufferedReader reader = null;
//        OutputStream outputStream = null;
//        StringBuffer response = new StringBuffer();
//        try {
//            URL httpUrl = null; // HTTP URL类 用这个类来创建连接
//            // 创建URL
//            httpUrl = new URL(url);
//            // 建立连接
//            HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
//            conn.setRequestMethod("POST");
//            conn.setRequestProperty("Content-Type", "application/json");
//            conn.setRequestProperty("Connection", "keep-alive");
//            conn.setRequestProperty("accept-language", "zh-CN,zh;q=0.9");
//            conn.setRequestProperty("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
//            conn.setRequestProperty("User-Agent",
//                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36");
//
//            if (headers != null && !headers.isEmpty()) {
//                headers.forEach((k, v) -> {
//                    conn.setRequestProperty(k, v);
//                });
//            }
//
//            conn.setUseCaches(false);// 设置不要缓存
//            conn.setInstanceFollowRedirects(true);
//            conn.setDoOutput(true);
//            conn.setDoInput(true);
//            conn.connect();
//            outputStream = conn.getOutputStream();
//            outputStream.write(params.getBytes(StandardCharsets.UTF_8));
//            outputStream.flush();
//
//            // 读取响应
//            reader = new BufferedReader(
//                    new InputStreamReader(conn.getInputStream()
//                            , StandardCharsets.UTF_8)
//            );
//            String str = null;
//
//            while ((str = reader.readLine()) != null) {
//                response.append(str + "\r\n");
//            }
//            reader.close();
//            // 断开连接
//            conn.disconnect();
//            return response.toString();
//        } catch (Exception e) {
//            logger.error("发送 请求出现异常！" + e);
//            e.printStackTrace();
//        }
//        // 使用finally块来关闭输出流、输入流
//        finally {
//            try {
//                if (out != null) {
//                    out.close();
//                }
//                if (reader != null) {
//                    reader.close();
//                }
//                if (outputStream != null) {
//                    outputStream.close();
//                }
//            } catch (IOException ex) {
//                ex.printStackTrace();
//            }
//        }
//
//        return null;
//    }
//
//    /**
//     * 发送请求
//     *
//     * @param url     请求地址
//     * @param headers 请求头
//     * @return
//     */
//    public static String getRequest(String url, Map<String, String> headers) {
//        OutputStreamWriter out = null;
//        BufferedReader reader = null;
//        StringBuffer response = new StringBuffer();
//        try {
//            URL httpUrl = null; // HTTP URL类 用这个类来创建连接
//            // 创建URL
//            httpUrl = new URL(url);
//            // 建立连接
//            HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
//            conn.setRequestMethod("GET");
//            conn.setRequestProperty("Content-Type", "text/html; charset=utf-8");
//            conn.setRequestProperty("Connection", "keep-alive");
//            conn.setRequestProperty("accept-language", "zh-CN,zh;q=0.9");
//            conn.setRequestProperty("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
//            conn.setRequestProperty("User-Agent",
//                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36");
//
//            if (headers != null && !headers.isEmpty()) {
//                headers.forEach((k, v) -> {
//                    conn.setRequestProperty(k, v);
//                });
//            }
//
//            conn.setUseCaches(false);// 设置不要缓存
//            conn.setInstanceFollowRedirects(true);
//            conn.setDoOutput(true);
//            conn.setDoInput(true);
//            conn.connect();
//            // 读取响应
//            reader = new BufferedReader(
//                    new InputStreamReader(conn.getInputStream()
//                            , StandardCharsets.UTF_8)
//            );
//            String str = null;
//
//            while ((str = reader.readLine()) != null) {
//                response.append(str + "\r\n");
//            }
//            reader.close();
//            // 断开连接
//            conn.disconnect();
//            return response.toString();
//        } catch (Exception e) {
//            logger.error("发送 请求出现异常！" + e);
//            e.printStackTrace();
//        }
//        // 使用finally块来关闭输出流、输入流
//        finally {
//            try {
//                if (out != null) {
//                    out.close();
//                }
//                if (reader != null) {
//                    reader.close();
//                }
//            } catch (IOException ex) {
//                ex.printStackTrace();
//            }
//        }
//
//        return null;
//    }
//
//    public static void main(String[] args) {
//        System.out.println(HttpRequestUtil.postRequest("http://localhost:10290/D96M09B1/getCompanyList", null, "{\"key\":\"嘉兴应凯\"}"));
//    }
//}
