package inks.sa.common.core.config.flyway;

import inks.sa.common.core.utils.PrintColor;

import java.awt.Desktop;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 增强的浏览器启动工具类
 * 支持多种操作系统和环境，包括飞牛OS
 */
public class BrowserLauncher {
    
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();
    private static final boolean IS_WINDOWS = OS_NAME.contains("windows");
    private static final boolean IS_MAC = OS_NAME.contains("mac");
    private static final boolean IS_LINUX = OS_NAME.contains("linux");
    private static final boolean IS_FNOS = OS_NAME.contains("fnos") || checkIsFnos();
    private static final boolean IS_DOCKER = checkIsDocker();
    
    /**
     * 尝试打开指定URL
     * @param url 要打开的URL
     * @return 是否成功打开
     */
    public static boolean openUrl(String url) {
        PrintColor.red("尝试自动打开浏览器: " + url);

        // Docker环境特殊处理
        if (IS_DOCKER) {
            return handleDockerEnvironment(url);
        }

        // 方法0: 检查用户自定义浏览器路径
        if (tryCustomBrowser(url)) {
            return true;
        }

        // 方法1: 使用Java Desktop API
        if (tryDesktopApi(url)) {
            return true;
        }

        // 方法2: 使用系统命令
        if (trySystemCommand(url)) {
            return true;
        }

        // 方法3: 尝试常见浏览器路径
        if (tryCommonBrowsers(url)) {
            return true;
        }

        PrintColor.red("所有自动打开方式都失败，需要手动访问");
        printUsageHelp(url);
        return false;
    }

    /**
     * 方法0: 尝试用户自定义浏览器
     */
    private static boolean tryCustomBrowser(String url) {
        String customBrowser = getCustomBrowserPath();

        if (customBrowser != null && !customBrowser.trim().isEmpty()) {
            try {
                ProcessBuilder pb = new ProcessBuilder(customBrowser.trim(), url);
                pb.start();
                PrintColor.red("✓ 自定义浏览器打开成功: " + customBrowser);
                return true;
            } catch (Exception e) {
                PrintColor.red("✗ 自定义浏览器失败: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * 获取自定义浏览器路径
     * 优先级: 系统属性 > 环境变量 > 配置文件
     */
    private static String getCustomBrowserPath() {
        // 1. 系统属性
        String customBrowser = System.getProperty("flyway.browser.path");
        if (customBrowser != null && !customBrowser.trim().isEmpty()) {
            return customBrowser;
        }

        // 2. 环境变量
        customBrowser = System.getenv("FLYWAY_BROWSER_PATH");
        if (customBrowser != null && !customBrowser.trim().isEmpty()) {
            return customBrowser;
        }

        // 3. 配置文件
        try (InputStream is = BrowserLauncher.class.getResourceAsStream("/flyway-browser.properties")) {
            if (is != null) {
                Properties props = new Properties();
                props.load(is);
                customBrowser = props.getProperty("flyway.browser.path");
                if (customBrowser != null && !customBrowser.trim().isEmpty()) {
                    return customBrowser;
                }
            }
        } catch (Exception e) {
            // 忽略配置文件读取错误
        }

        return null;
    }
    
    /**
     * 方法1: 尝试使用Java Desktop API
     */
    private static boolean tryDesktopApi(String url) {
        try {
            if (Desktop.isDesktopSupported()) {
                Desktop desktop = Desktop.getDesktop();
                if (desktop.isSupported(Desktop.Action.BROWSE)) {
                    desktop.browse(URI.create(url));
                    PrintColor.red("✓ Desktop API 打开成功");
                    return true;
                }
            }
        } catch (Exception e) {
            PrintColor.red("✗ Desktop API 失败: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 方法2: 使用系统命令
     */
    private static boolean trySystemCommand(String url) {
        try {
            String[] command = getSystemCommand(url);
            if (command != null) {
                ProcessBuilder pb = new ProcessBuilder(command);
                pb.start();
                PrintColor.red("✓ 系统命令打开成功: " + String.join(" ", command));
                return true;
            }
        } catch (Exception e) {
            PrintColor.red("✗ 系统命令失败: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 获取系统特定的打开命令
     */
    private static String[] getSystemCommand(String url) {
        if (IS_WINDOWS) {
            return new String[]{"rundll32", "url.dll,FileProtocolHandler", url};
        } else if (IS_MAC) {
            return new String[]{"open", url};
        } else if (IS_LINUX) {
            // 飞牛OS 特殊处理
            if (IS_FNOS) {
                // 飞牛OS 可能的打开命令
                if (commandExists("fnos-open")) {
                    return new String[]{"fnos-open", url};
                }
                if (commandExists("fnos-browser")) {
                    return new String[]{"fnos-browser", url};
                }
            }

            // 优先尝试 xdg-open (标准Linux桌面环境)
            if (commandExists("xdg-open")) {
                return new String[]{"xdg-open", url};
            }
            // 其他Linux发行版的备选方案
            if (commandExists("gnome-open")) {
                return new String[]{"gnome-open", url};
            }
            if (commandExists("kde-open")) {
                return new String[]{"kde-open", url};
            }
            if (commandExists("exo-open")) {
                return new String[]{"exo-open", url};
            }
        }
        return null;
    }
    
    /**
     * 方法3: 尝试常见浏览器路径
     */
    private static boolean tryCommonBrowsers(String url) {
        List<String> browsers = getCommonBrowserPaths();
        
        for (String browser : browsers) {
            try {
                ProcessBuilder pb = new ProcessBuilder(browser, url);
                pb.start();
                PrintColor.red("✓ 浏览器路径打开成功: " + browser);
                return true;
            } catch (Exception e) {
                // 继续尝试下一个浏览器
            }
        }
        
        PrintColor.red("✗ 所有常见浏览器路径都失败");
        return false;
    }
    
    /**
     * 获取常见浏览器路径列表
     */
    private static List<String> getCommonBrowserPaths() {
        List<String> browsers = new ArrayList<>();
        
        if (IS_WINDOWS) {
            // Windows 常见浏览器路径
            browsers.add("C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe");
            browsers.add("C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe");
            browsers.add("C:\\Program Files\\Mozilla Firefox\\firefox.exe");
            browsers.add("C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe");
            browsers.add("C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe");
            browsers.add("C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe");
        } else if (IS_MAC) {
            // macOS 常见浏览器路径
            browsers.add("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome");
            browsers.add("/Applications/Firefox.app/Contents/MacOS/firefox");
            browsers.add("/Applications/Safari.app/Contents/MacOS/Safari");
            browsers.add("/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge");
        } else if (IS_LINUX) {
            // Linux 常见浏览器命令 (包括飞牛OS)
            browsers.add("google-chrome");
            browsers.add("google-chrome-stable");
            browsers.add("chromium");
            browsers.add("chromium-browser");
            browsers.add("firefox");
            browsers.add("mozilla-firefox");
            browsers.add("opera");
            browsers.add("konqueror");
            browsers.add("epiphany");
            browsers.add("midori");
            // 飞牛OS 特定浏览器
            if (IS_FNOS) {
                browsers.add("fnos-browser");
                browsers.add("fnos-chrome");
                browsers.add("fnos-firefox");
                browsers.add("/usr/bin/fnos-browser");
                browsers.add("/opt/fnos/browser/fnos-browser");
            }

            // 其他轻量级浏览器
            browsers.add("webkit");
            browsers.add("links");
            browsers.add("lynx");
            browsers.add("w3m");
        }
        
        return browsers;
    }
    
    /**
     * Docker环境特殊处理
     */
    private static boolean handleDockerEnvironment(String url) {
        PrintColor.red("🐳 检测到Docker环境，使用特殊处理方式");

        // 尝试通过宿主机端口映射访问
        String dockerUrl = convertToDockerAccessibleUrl(url);
        if (!dockerUrl.equals(url)) {
            PrintColor.red("🔄 转换为宿主机访问地址: " + dockerUrl);
        }

        // 在Docker中，我们无法直接打开宿主机浏览器
        // 但可以尝试一些特殊方法

        // 方法1: 检查是否有X11转发
        if (tryX11Forward(dockerUrl)) {
            return true;
        }

        // 方法2: 尝试通过宿主机命令
        if (tryHostCommand(dockerUrl)) {
            return true;
        }

        // 方法3: 生成可点击的链接
        generateClickableLink(dockerUrl);

        // Docker环境下提供特殊的使用说明
        printDockerUsageHelp(dockerUrl);
        return false; // Docker环境下通常无法自动打开，但提供了替代方案
    }

    /**
     * 检查是否为Docker环境
     */
    private static boolean checkIsDocker() {
        try {
            // 检查Docker特有的文件
            if (new java.io.File("/.dockerenv").exists()) {
                return true;
            }

            // 检查环境变量
            if (System.getenv("DOCKER_CONTAINER") != null ||
                System.getProperty("docker.container") != null) {
                return true;
            }

            // 检查cgroup文件 (Java 8兼容方式)
            java.io.File cgroupFile = new java.io.File("/proc/1/cgroup");
            if (cgroupFile.exists()) {
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.FileReader(cgroupFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (line.contains("docker")) {
                            return true;
                        }
                    }
                } catch (Exception e) {
                    // 忽略读取错误
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为飞牛OS
     */
    private static boolean checkIsFnos() {
        try {
            // 检查飞牛OS特有的文件或目录
            return new java.io.File("/etc/fnos-release").exists() ||
                   new java.io.File("/usr/bin/fnos-browser").exists() ||
                   new java.io.File("/opt/fnos").exists() ||
                   System.getProperty("fnos.version") != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查命令是否存在
     */
    private static boolean commandExists(String command) {
        try {
            ProcessBuilder pb;
            if (IS_WINDOWS) {
                pb = new ProcessBuilder("where", command);
            } else {
                pb = new ProcessBuilder("which", command);
            }
            Process process = pb.start();
            return process.waitFor() == 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 转换为Docker可访问的URL
     */
    private static String convertToDockerAccessibleUrl(String url) {
        // 检查是否有端口映射配置
        String hostPort = System.getenv("HOST_PORT");
        String hostIp = System.getenv("HOST_IP");

        if (hostPort != null || hostIp != null) {
            try {
                java.net.URI uri = java.net.URI.create(url);
                String newHost = hostIp != null ? hostIp : "localhost";
                int newPort = hostPort != null ? Integer.parseInt(hostPort) : uri.getPort();

                return String.format("%s://%s:%d%s",
                    uri.getScheme(), newHost, newPort, uri.getPath());
            } catch (Exception e) {
                // 如果转换失败，返回原URL
            }
        }

        return url;
    }

    /**
     * 尝试X11转发
     */
    private static boolean tryX11Forward(String url) {
        if (System.getenv("DISPLAY") != null) {
            try {
                ProcessBuilder pb = new ProcessBuilder("xdg-open", url);
                pb.start();
                PrintColor.red("✓ X11转发打开成功");
                return true;
            } catch (Exception e) {
                PrintColor.red("✗ X11转发失败: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * 尝试通过宿主机命令
     */
    private static boolean tryHostCommand(String url) {
        // 检查是否挂载了Docker socket
        if (new java.io.File("/var/run/docker.sock").exists()) {
            try {
                // 尝试在宿主机上执行打开命令
                String[] command = {"docker", "run", "--rm", "-v", "/tmp/.X11-unix:/tmp/.X11-unix",
                                  "-e", "DISPLAY=" + System.getenv("DISPLAY"),
                                  "alpine/curl", "curl", "-s", url};
                ProcessBuilder pb = new ProcessBuilder(command);
                pb.start();
                PrintColor.red("✓ 宿主机命令执行成功");
                return true;
            } catch (Exception e) {
                PrintColor.red("✗ 宿主机命令失败: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * 生成可点击的链接
     */
    private static void generateClickableLink(String url) {
        PrintColor.red("🔗 可点击链接: " + url);
        PrintColor.red("📋 请复制上述链接到浏览器中访问");

        // 如果是支持ANSI转义序列的终端，生成可点击链接
        try {
            String clickableLink = "\u001B]8;;" + url + "\u001B\\" + url + "\u001B]8;;\u001B\\";
            System.out.println("🖱️  点击链接: " + clickableLink);
        } catch (Exception e) {
            // 忽略ANSI转义序列错误
        }
    }

    /**
     * Docker环境使用帮助
     */
    private static void printDockerUsageHelp(String url) {
        PrintColor.red("🐳 Docker环境访问指南:");
        PrintColor.red("   1. 直接访问: " + url);
        PrintColor.red("   2. 端口映射配置:");
        PrintColor.red("      docker run -p 8080:8080 -e HOST_IP=localhost -e HOST_PORT=8080 your-image");
        PrintColor.red("   3. X11转发 (Linux宿主机):");
        PrintColor.red("      docker run -e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix your-image");
        PrintColor.red("   4. 宿主机访问:");
        PrintColor.red("      在宿主机浏览器中访问: http://localhost:8080/flyway-progress.html");
    }

    /**
     * 打印使用帮助信息
     */
    private static void printUsageHelp(String url) {
        if (IS_DOCKER) {
            printDockerUsageHelp(url);
            return;
        }

        PrintColor.red("💡 自动打开浏览器失败的解决方案:");
        PrintColor.red("   1. 手动复制上述URL到浏览器中访问");
        PrintColor.red("   2. 设置自定义浏览器路径 (按优先级):");
        PrintColor.red("      a) 系统属性: -Dflyway.browser.path=/path/to/browser");
        PrintColor.red("      b) 环境变量: export FLYWAY_BROWSER_PATH=/path/to/browser");
        PrintColor.red("      c) 配置文件: 编辑 flyway-browser.properties");
        if (IS_FNOS) {
            PrintColor.red("   3. 飞牛OS用户推荐路径:");
            PrintColor.red("      - /usr/bin/fnos-browser");
            PrintColor.red("      - /opt/fnos/browser/fnos-browser");
            PrintColor.red("      - /usr/local/bin/fnos-chrome");
        }
        PrintColor.red("   4. 查看配置文件: resources/flyway-browser.properties");
    }

    /**
     * 获取系统信息用于调试
     */
    public static String getSystemInfo() {
        return String.format("操作系统: %s, Java版本: %s, Desktop支持: %s, 飞牛OS: %s, Docker: %s",
            OS_NAME,
            System.getProperty("java.version"),
            Desktop.isDesktopSupported(),
            IS_FNOS ? "是" : "否",
            IS_DOCKER ? "是" : "否");
    }
}
