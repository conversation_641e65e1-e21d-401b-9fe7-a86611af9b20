package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaMenuappEntity;
import inks.sa.common.core.domain.pojo.SaMenuappPojo;
import inks.sa.common.core.mapper.SaMenuappMapper;
import inks.sa.common.core.service.SaMenuappService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * APP导航(SaMenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Service("saMenuappService")
public class SaMenuappServiceImpl implements SaMenuappService {
    @Resource
    private SaMenuappMapper saMenuappMapper;

    @Override
    public SaMenuappPojo getEntity(String key) {
        return this.saMenuappMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaMenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMenuappPojo> lst = saMenuappMapper.getPageList(queryParam);
            PageInfo<SaMenuappPojo> pageInfo = new PageInfo<SaMenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaMenuappPojo insert(SaMenuappPojo saMenuappPojo) {
        //初始化NULL字段
        cleanNull(saMenuappPojo);
        SaMenuappEntity saMenuappEntity = new SaMenuappEntity(); 
        BeanUtils.copyProperties(saMenuappPojo,saMenuappEntity);
        //生成雪花id
          saMenuappEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
          this.saMenuappMapper.insert(saMenuappEntity);
        return this.getEntity(saMenuappEntity.getNavid());
  
    }

    /**
     * 修改数据
     *
     * @param saMenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMenuappPojo update(SaMenuappPojo saMenuappPojo) {
        SaMenuappEntity saMenuappEntity = new SaMenuappEntity(); 
        BeanUtils.copyProperties(saMenuappPojo,saMenuappEntity);
        this.saMenuappMapper.update(saMenuappEntity);
        return this.getEntity(saMenuappEntity.getNavid());
    }


    @Override
    public int delete(String key) {
        return this.saMenuappMapper.delete(key) ;
    }

    @Override
    public List<SaMenuappPojo> getListByPid(String key) {
        return  this.saMenuappMapper.getListByPid(key);
    }

    @Override
    public List<SaMenuappPojo> getListAll() {
        return this.saMenuappMapper.getListAll();
    }

    @Override
    public List<SaMenuappPojo> getListByNavids(List<String> navids) {
        return this.saMenuappMapper.getListByNavids(navids);
    }

    private static void cleanNull(SaMenuappPojo saMenuappPojo) {
        if(saMenuappPojo.getNavpid()==null) saMenuappPojo.setNavpid("");
        if(saMenuappPojo.getNavtype()==null) saMenuappPojo.setNavtype("");
        if(saMenuappPojo.getNavcode()==null) saMenuappPojo.setNavcode("");
        if(saMenuappPojo.getNavname()==null) saMenuappPojo.setNavname("");
        if(saMenuappPojo.getNavgroup()==null) saMenuappPojo.setNavgroup("");
        if(saMenuappPojo.getRownum()==null) saMenuappPojo.setRownum(0);
        if(saMenuappPojo.getImagecss()==null) saMenuappPojo.setImagecss("");
        if(saMenuappPojo.getIconurl()==null) saMenuappPojo.setIconurl("");
        if(saMenuappPojo.getNavigateurl()==null) saMenuappPojo.setNavigateurl("");
        if(saMenuappPojo.getMvcurl()==null) saMenuappPojo.setMvcurl("");
        if(saMenuappPojo.getModuletype()==null) saMenuappPojo.setModuletype("");
        if(saMenuappPojo.getModulecode()==null) saMenuappPojo.setModulecode("");
        if(saMenuappPojo.getRolecode()==null) saMenuappPojo.setRolecode("");
        if(saMenuappPojo.getImageindex()==null) saMenuappPojo.setImageindex("");
        if(saMenuappPojo.getImagestyle()==null) saMenuappPojo.setImagestyle("");
        if(saMenuappPojo.getEnabledmark()==null) saMenuappPojo.setEnabledmark(0);
        if(saMenuappPojo.getRemark()==null) saMenuappPojo.setRemark("");
        if(saMenuappPojo.getPermissioncode()==null) saMenuappPojo.setPermissioncode("");
        if(saMenuappPojo.getFunctionid()==null) saMenuappPojo.setFunctionid("");
        if(saMenuappPojo.getFunctioncode()==null) saMenuappPojo.setFunctioncode("");
        if(saMenuappPojo.getFunctionname()==null) saMenuappPojo.setFunctionname("");
        if(saMenuappPojo.getLister()==null) saMenuappPojo.setLister("");
        if(saMenuappPojo.getCreatedate()==null) saMenuappPojo.setCreatedate(new Date());
        if(saMenuappPojo.getModifydate()==null) saMenuappPojo.setModifydate(new Date());
        if(saMenuappPojo.getDeletemark()==null) saMenuappPojo.setDeletemark(0);
        if(saMenuappPojo.getDeletelister()==null) saMenuappPojo.setDeletelister("");
        if(saMenuappPojo.getDeletedate()==null) saMenuappPojo.setDeletedate(new Date());
   }

}
