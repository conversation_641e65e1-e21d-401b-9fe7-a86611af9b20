package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.service.*;
import inks.sa.common.core.utils.wxe.QyWeChat;
import inks.sa.common.core.utils.wxe.QyWeChatUtils;
import inks.sa.common.core.utils.wxe.SendRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@RestController
@RequestMapping("SaUser/wxelogin")
@Api(tags = "通用:用户服务-企业微信登录")
public class A_SaUser_WxeLoginController {
    @Resource
    private SaJustauthService saJustauthService;

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(A_SaUser_WxeLoginController.class);
    @Resource
    private SaJustauthService justAuthService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaConfigService saConfigService;
    @Resource
    private SaUserService saUserService;
    @Resource
    private SaDeptService saDeptService;
    @Resource
    private A_SaUserroleController saUserroleController;
    @Value("${inks.tid}")
    private String yml_tid;

    @GetMapping("/redirectWeb")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void redirectWeb(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
        loginurl = mapcfg.get(ConfigConstant.WXE_WEBLOGINURL);

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        //LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "wxe", tid);
        //第三方账号登录 传入：authuuid、authtype
        SaUserPojo saUserDB = saUserService.getEntityByJustAuth("wxe", userid);
        if (saUserDB == null) {
            response.sendRedirect("");
            return;
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUsername(saUserDB.getUsername());
        loginUser.setPassword(saUserDB.getPassword());


        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        saRedisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }

    @GetMapping("/redirectApp")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void redirectApp(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
        loginurl = mapcfg.get(ConfigConstant.WXE_APPLOGINURL);

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        //第三方账号登录 传入：authuuid、authtype
        LoginUser loginUser =  saUserService.getLoginUserByJust("wxe", userid);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }
        loginUser.setTenantid(yml_tid);
        //20250505 加入部门信息（组织框架）
        List<DeptinfoPojo> lstdept = saDeptService.getDeptinfoList(loginUser.getUserid());
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setLstdept(lstdept);
        tenantInfo.setIsdeptadmin(0);
        if (CollectionUtils.isNotEmpty(lstdept)) {
            tenantInfo.setLstdept(lstdept);
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            loginUser.setTenantinfo(tenantInfo);
        }

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        loginUser.setToken(tokenuuid);
        saRedisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时token:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        logger.info("logurl:" + loginurl);

        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }



    /**
     * @Description
     * /redirectApp：源免登url state是tid
     * https://open.weixin.qq.com/connect/oauth2/authorize?response_type=code&state=tid-inks-pms&appid=ww3742ed7d2688ceXX&redirect_uri=https%3A%2F%2Fpmsapi.inksyun.com%2FSaUser%2Fwxelogin%2FredirectApp%3Ftid%3Dtid-inks-pms&scope=snsapi_base#wechat_redirect
     * /requJustAuth：免登授权URL state是token
     * system.ding.selfjusturl
     * https://open.weixin.qq.com/connect/oauth2/authorize?response_type=code&state={token}&appid=ww3742ed7d2688ceXX&redirect_uri=https%3A%2F%2Fpmsapi.inksyun.com%2FSaUser%2Fwxelogin%2FrequJustAuth%3Ftid%3Dtid-inks-pms&scope=snsapi_base#wechat_redirect
     */
    @GetMapping("/requJustAuth")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void requJustAuth(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
        loginurl = mapcfg.get(ConfigConstant.WXE_APPLOGINURL);

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String wxe_userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (wxe_userid != null) {
            logger.info("企业微信免登 uuid:" + wxe_userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        //第三方账号登录 传入：authuuid、authtype
        LoginUser loginUser =  saUserService.getLoginUserByJust("wxe", wxe_userid);
        if (loginUser == null) {
            // 如果Sa_JustAuth表中没有记录，就创建一个新的记录关联userid和wxe_userid
            LoginUser loginUserFromToken = saRedisService.getLoginUserFromToken(state);
            if (loginUserFromToken != null) {
                SaJustauthPojo saJustauthPojo = new SaJustauthPojo();
                saJustauthPojo.setAuthuuid(wxe_userid);
                saJustauthPojo.setAuthtype("wxe");
                saJustauthPojo.setUserid(loginUserFromToken.getUserid());
                saJustauthPojo.setUsername(loginUserFromToken.getUsername());
                saJustauthPojo.setRealname(loginUserFromToken.getRealname());
                saJustauthPojo.setTenantid(loginUserFromToken.getTenantid());
                saJustauthPojo.setCreateby("/requJustAuth自动创建");
                saJustauthService.insert(saJustauthPojo);
            } else {
                // token也无效
                response.sendRedirect("");
                return;
            }
        }

        loginUser.setTenantid(yml_tid);
        //20250505 加入部门信息（组织框架）
        List<DeptinfoPojo> lstdept = saDeptService.getDeptinfoList(loginUser.getUserid());
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setLstdept(lstdept);
        tenantInfo.setIsdeptadmin(0);
        if (CollectionUtils.isNotEmpty(lstdept)) {
            tenantInfo.setLstdept(lstdept);
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            loginUser.setTenantinfo(tenantInfo);
        }

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        loginUser.setToken(tokenuuid);
        saRedisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时token:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        logger.info("logurl:" + loginurl);

        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }


    @ApiOperation(value = "读取LoginUser用户信息", notes = "读取LoginUser解析用户信息", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key, HttpServletRequest request) {
        //解析用户信息
        LoginUser loginUser = saRedisService.getLoginUserFromToken(key);
        try {
            if (loginUser == null) {
                logger.info("key已过期失效：" + key);
                return R.fail("key已过期失效");
            }
            logger.info("Loginuser：" + loginUser);

            R<LoginUser> loginUserR = this.saUserroleController.updateTokenPerm(key);
            LoginUser loginUserNew = loginUserR.getData();
            Map<String, Object> tokenMap = saRedisService.createToken(loginUserNew);
            logger.info("免登最终token：" + tokenMap.get("access_token").toString());
            //删除缓存用户信息
            saRedisService.deleteObject("login_tokens:" + key);
            //重新存入redis
            Map<String, Object> map = tokenMap;   //tokenService.createToken(loginUser);
            return R.ok(map);
        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
