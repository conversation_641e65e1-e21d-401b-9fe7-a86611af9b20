package inks.sa.common.core.annotation.aspect;

import com.alibaba.fastjson.JSON;
import inks.common.core.domain.LoginUser;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.ip.IpUtils;
import inks.sa.common.core.annotation.OperLog;
import inks.sa.common.core.domain.pojo.SaOperlogPojo;
import inks.sa.common.core.service.SaOperlogService;
import inks.sa.common.core.service.SaRedisService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
public class Sa_OperLogAspect {
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaOperlogService saOperlogService;

    public Sa_OperLogAspect() {
    }

    @AfterReturning(
            pointcut = "@annotation(controllerLog)",
            returning = "jsonResult"
    )
    public void doAfterReturning(JoinPoint joinPoint, OperLog controllerLog, Object jsonResult) {
        SaOperlogPojo saOperlogPojo = this.setEntity(joinPoint, controllerLog);
        saOperlogPojo.setJsonresult(JSON.toJSONString(jsonResult));
        LoginUser userInfo = this.saRedisService.getLoginUser();
        saOperlogPojo.setTenantid(userInfo.getTenantid());
        saOperlogPojo.setOpername(userInfo.getRealName());
        saOperlogPojo.setOperuserid(userInfo.getUserid());
        saOperlogPojo.setStatus(0);
        this.saOperlogService.insert(saOperlogPojo);
    }

    @AfterThrowing(
            value = "@annotation(controllerLog)",
            throwing = "e"
    )
    public void doAfterThrowing(JoinPoint joinPoint, OperLog controllerLog, Exception e) {
        SaOperlogPojo saOperlogPojo = this.setEntity(joinPoint, controllerLog);
        saOperlogPojo.setErrormsg(e.getMessage());
        saOperlogPojo.setStatus(1);//抛异常
        LoginUser userInfo = this.saRedisService.getLoginUser();
        saOperlogPojo.setTenantid(userInfo.getTenantid());
        saOperlogPojo.setOpername(userInfo.getRealName());
        saOperlogPojo.setOperuserid(userInfo.getUserid());
        this.saOperlogService.insert(saOperlogPojo);
    }

    public SaOperlogPojo setEntity(JoinPoint point, OperLog controllerLog) {
        SaOperlogPojo saOperlogPojo = new SaOperlogPojo();
        saOperlogPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        saOperlogPojo.setOperurl(ServletUtils.getRequest().getRequestURI());
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        saOperlogPojo.setOperip(ip);
        saOperlogPojo.setOpertitle(controllerLog.title());
        saOperlogPojo.setOperatortype(controllerLog.operatorType().ordinal());
        saOperlogPojo.setBusinesstype(controllerLog.businessType().ordinal());
        Object[] paramValues = point.getArgs();
        Map<String, Object> map = new HashMap<>();
        String[] paramNames = ((CodeSignature) point.getSignature()).getParameterNames();

        for (int i = 0; i < paramNames.length; ++i) {
            map.put(paramNames[i], paramValues[i]);
        }

        saOperlogPojo.setOperparam(map.toString());
        saOperlogPojo.setOpertime(new Date());
        saOperlogPojo.setRequestmethod(ServletUtils.getRequest().getMethod());
        saOperlogPojo.setMethod(point.getTarget().getClass().getName() + "." + point.getSignature().getName() + "()");
        return saOperlogPojo;
    }
}
