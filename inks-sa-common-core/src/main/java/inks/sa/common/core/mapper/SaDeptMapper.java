package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDeptPojo;
import inks.sa.common.core.domain.SaDeptEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户组织架构(SaDept)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-05 12:36:30
 */
@Mapper
public interface SaDeptMapper {


    SaDeptPojo getEntity(@Param("key") String key);

    List<SaDeptPojo> getPageList(QueryParam queryParam);

    int insert(SaDeptEntity saDeptEntity);

    int update(SaDeptEntity saDeptEntity);

    int delete(@Param("key") String key);

    List<SaDeptPojo> getListByParentid(String parentid);
}

