package inks.sa.common.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Configuration;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.Base64;

/**
 * Swagger页面用户名密码
 * 链接输入密码可直接访问方式：***********************************/swagger-ui.html
 */
@Configuration
@ServletComponentScan
@WebFilter(urlPatterns = {"/swagger-ui.html", "/webjars/*", "/swagger-resources/*", "/v2/api-docs/*"})
public class SwaggerAuthConfig implements Filter {

    @Value("${inks.swagger.username:inks}")
    private String USERNAME;

    @Value("${inks.swagger.password:8866}")
    private String PASSWORD;

    // Define protected paths
    private static final Set<String> PROTECTED_PATHS = new HashSet<>(Arrays.asList(
            "/swagger-ui.html",
            "/webjars/",
            "/swagger-resources/",
            "/v2/api-docs/"
    ));

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化操作
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String path = httpRequest.getRequestURI();

        // Check if the current path should be protected
        boolean requiresAuth = PROTECTED_PATHS.stream()
                .anyMatch(protectedPath -> path.startsWith(protectedPath));

        if (!requiresAuth) {
            // If path is not protected, proceed without authentication
            chain.doFilter(request, response);
            return;
        }

        // Handle authentication for protected paths
        String authHeader = httpRequest.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Basic ")) {
            String encodedCredentials = authHeader.substring(6);
            String decodedCredentials = new String(Base64.getDecoder().decode(encodedCredentials));
            String[] credentials = decodedCredentials.split(":", 2);

            if (credentials.length == 2 &&
                    USERNAME.equals(credentials[0]) &&
                    PASSWORD.equals(credentials[1])) {
                chain.doFilter(request, response);
                return;
            }
        }

        httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        httpResponse.setHeader("WWW-Authenticate", "Basic realm=\"Swagger\"");
    }

    @Override
    public void destroy() {
        // 清理操作
    }
}