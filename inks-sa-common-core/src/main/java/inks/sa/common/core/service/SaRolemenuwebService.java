package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaRolemenuwebPojo;

import java.util.List;

/**
 * 角色菜单Web(SaRolemenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
public interface SaRolemenuwebService {


    SaRolemenuwebPojo getEntity(String key);

    PageInfo<SaRolemenuwebPojo> getPageList(QueryParam queryParam);

    SaRolemenuwebPojo insert(SaRolemenuwebPojo saRolemenuwebPojo);

    SaRolemenuwebPojo update(SaRolemenuwebPojo saRolemenuwebpojo);

    int delete(String key);

    Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser);
}
