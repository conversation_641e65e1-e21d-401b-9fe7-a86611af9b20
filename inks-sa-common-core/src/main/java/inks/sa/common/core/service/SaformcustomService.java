package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaformcustomPojo;

/**
 * 自定义界面(Saformcustom)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-09 10:48:38
 */
public interface SaformcustomService {


    SaformcustomPojo getEntity(String key);

    PageInfo<SaformcustomPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saformcustomPojo 实例对象
     * @return 实例对象
     */
    SaformcustomPojo insert(SaformcustomPojo saformcustomPojo);

    /**
     * 修改数据
     *
     * @param saformcustompojo 实例对象
     * @return 实例对象
     */
    SaformcustomPojo update(SaformcustomPojo saformcustompojo);

    int delete(String key);

    SaformcustomPojo getEntityByCode(String key);
}
