package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaPermissionEntity;
import inks.sa.common.core.domain.pojo.SaPermissionPojo;
import inks.sa.common.core.mapper.SaPermissionMapper;
import inks.sa.common.core.service.SaPermissionService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 权限关系(SaPermission)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:34
 */
@Service("saPermissionService")
public class SaPermissionServiceImpl implements SaPermissionService {
    @Resource
    private SaPermissionMapper saPermissionMapper;

    private static void cleanNull(SaPermissionPojo saPermissionPojo) {
        if (saPermissionPojo.getId() == null) saPermissionPojo.setId("");
        if (saPermissionPojo.getResourcetype() == null) saPermissionPojo.setResourcetype("");
        if (saPermissionPojo.getResourceid() == null) saPermissionPojo.setResourceid("");
        if (saPermissionPojo.getPermid() == null) saPermissionPojo.setPermid("");
        if (saPermissionPojo.getPermcode() == null) saPermissionPojo.setPermcode("");
        if (saPermissionPojo.getPermname() == null) saPermissionPojo.setPermname("");
        if (saPermissionPojo.getCreateby() == null) saPermissionPojo.setCreateby("");
        if (saPermissionPojo.getCreatebyid() == null) saPermissionPojo.setCreatebyid("");
        if (saPermissionPojo.getCreatedate() == null) saPermissionPojo.setCreatedate(new Date());
        if (saPermissionPojo.getLister() == null) saPermissionPojo.setLister("");
        if (saPermissionPojo.getListerid() == null) saPermissionPojo.setListerid("");
        if (saPermissionPojo.getModifydate() == null) saPermissionPojo.setModifydate(new Date());
        if (saPermissionPojo.getTenantid() == null) saPermissionPojo.setTenantid("");
        if (saPermissionPojo.getTenantname() == null) saPermissionPojo.setTenantname("");
        if (saPermissionPojo.getRevision() == null) saPermissionPojo.setRevision(0);
    }

    @Override
    public SaPermissionPojo getEntity(String key) {
        return this.saPermissionMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaPermissionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaPermissionPojo> lst = saPermissionMapper.getPageList(queryParam);
            PageInfo<SaPermissionPojo> pageInfo = new PageInfo<SaPermissionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaPermissionPojo insert(SaPermissionPojo saPermissionPojo) {
        //初始化NULL字段
        cleanNull(saPermissionPojo);
        SaPermissionEntity saPermissionEntity = new SaPermissionEntity();
        BeanUtils.copyProperties(saPermissionPojo, saPermissionEntity);
        //生成雪花id
        saPermissionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saPermissionEntity.setRevision(1);  //乐观锁
        this.saPermissionMapper.insert(saPermissionEntity);
        return this.getEntity(saPermissionEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saPermissionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPermissionPojo update(SaPermissionPojo saPermissionPojo) {
        SaPermissionEntity saPermissionEntity = new SaPermissionEntity();
        BeanUtils.copyProperties(saPermissionPojo, saPermissionEntity);
        this.saPermissionMapper.update(saPermissionEntity);
        return this.getEntity(saPermissionEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saPermissionMapper.delete(key);
    }

    @Override
    public List<SaPermissionPojo> getListByRole(String key) {
        try {
            List<SaPermissionPojo> list = saPermissionMapper.getListByRole(key);
            return list;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaPermissionPojo> getUserAllPerm(String key) {
        return saPermissionMapper.getUserAllPerm(key);
    }
}
