package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 第三方登录(Sa_JustAuth)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-18 14:01:59
 */
@RestController
@RequestMapping("SaJustAuth")
@Api(tags = "通用:第三方登录")
public class A_SaJustauthController {
    @Resource
    private SaJustauthService saJustauthService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过第三方id获取实例", notes = "通过第三方id获取实例", produces = "application/json")
    @RequestMapping(value = "/getJustauthByUuid", method = RequestMethod.GET)
    public R<JustauthPojo> getJustauthByUuid(String key, String type, String tid) {
        try {
            if (tid == null || tid.isEmpty()) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            SaJustauthPojo SaJustauthPojo = this.saJustauthService.getJustauthByUuid(key, type, tid);
            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(SaJustauthPojo, justauthPojo);
            return R.ok(justauthPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过第三方Unionid获取列表", notes = "通过第三方id获取列表", produces = "application/json")
    @RequestMapping(value = "/getListByUnionid", method = RequestMethod.GET)
    public R<List<JustauthPojo>> getListByUnionid(String key) {
        try {
            return R.ok(this.saJustauthService.getListByUnionid(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过Userid获取实例", notes = "通过Userid获取实例", produces = "application/json")
    @RequestMapping(value = "/getJustauthByUserid", method = RequestMethod.GET)
    public R<JustauthPojo> getJustauthByUserid(String key, String type, String tid) {
        try {
            if (StringUtils.isBlank(tid)) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            SaJustauthPojo SaJustauthPojo = this.saJustauthService.getJustauthByUserid(key, type, tid);
            if (SaJustauthPojo != null) {
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(SaJustauthPojo, justauthPojo);
                return R.ok(justauthPojo);
            } else {
                return R.ok(null);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过Deptid获取主管实例", notes = "通过Userid获取主管实例", produces = "application/json")
    @RequestMapping(value = "/getAdminListByDeptid", method = RequestMethod.GET)
    public R<List<JustauthPojo>> getAdminListByDeptid(String key, String type) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJustauthService.getAdminListByDeptid(key, type, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取第三方登录详细信息", notes = "获取第三方登录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.List")
    public R<SaJustauthPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJustauthService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.List")
    public R<PageInfo<SaJustauthPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_JustAuth.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saJustauthService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增第三方登录", notes = "新增第三方登录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.Add")
    public R<SaJustauthPojo> create(@RequestBody String json) {
        try {
            SaJustauthPojo saJustauthPojo = JSONArray.parseObject(json, SaJustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saJustauthPojo.setCreateby(loginUser.getRealName());   // 创建者
            saJustauthPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saJustauthPojo.setCreatedate(new Date());   // 创建时间
            saJustauthPojo.setLister(loginUser.getRealname());   // 制表
            saJustauthPojo.setListerid(loginUser.getUserid());    // 制表id
            saJustauthPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saJustauthService.insert(saJustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改第三方登录", notes = "修改第三方登录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.Edit")
    public R<SaJustauthPojo> update(@RequestBody String json) {
        try {
            SaJustauthPojo saJustauthPojo = JSONArray.parseObject(json, SaJustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saJustauthPojo.setLister(loginUser.getRealname());   // 制表
            saJustauthPojo.setListerid(loginUser.getUserid());    // 制表id
            saJustauthPojo.setModifydate(new Date());   //修改时间
            //            saJustauthPojo.setAssessor(""); // 审核员
            //            saJustauthPojo.setAssessorid(""); // 审核员id
            //            saJustauthPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saJustauthService.update(saJustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除第三方登录", notes = "删除第三方登录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJustauthService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除第三方登录", notes = "删除第三方登录", produces = "application/json")
    @RequestMapping(value = "/deleteByAuthUuid", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.Delete")
    public R<Integer> deleteByAuthUuid(String authuuid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJustauthService.deleteByAuthUuid(authuuid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JustAuth.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaJustauthPojo saJustauthPojo = this.saJustauthService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saJustauthPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

