package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaValidatorPojo;
import inks.sa.common.core.domain.SaValidatorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 数据验证(SaValidator)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-18 14:23:32
 */
@Mapper
public interface SaValidatorMapper {


    SaValidatorPojo getEntity(@Param("key") String key);

    List<SaValidatorPojo> getPageList(QueryParam queryParam);

    int insert(SaValidatorEntity saValidatorEntity);

    int update(SaValidatorEntity saValidatorEntity);

    int delete(@Param("key") String key);

    List<SaValidatorPojo> getListByValicodeEnabled(String valicode, String tid);
}

