package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaScenefieldEntity;
import inks.sa.common.core.domain.pojo.SaScenefieldPojo;
import inks.sa.common.core.mapper.SaScenefieldMapper;
import inks.sa.common.core.service.SaScenefieldService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 场景字段(Sascenefield)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
@Service("sascenefieldService")
public class SaScenefieldServiceImpl implements SaScenefieldService {
    @Resource
    private SaScenefieldMapper sascenefieldMapper;


    @Override
    public SaScenefieldPojo getEntity(String key) {
        return this.sascenefieldMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaScenefieldPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaScenefieldPojo> lst = sascenefieldMapper.getPageList(queryParam);
            PageInfo<SaScenefieldPojo> pageInfo = new PageInfo<SaScenefieldPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param sascenefieldPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScenefieldPojo insert(SaScenefieldPojo sascenefieldPojo) {
        //初始化NULL字段
        if (sascenefieldPojo.getGengroupid() == null) sascenefieldPojo.setGengroupid("");
        if (sascenefieldPojo.getModulecode() == null) sascenefieldPojo.setModulecode("");
        if (sascenefieldPojo.getFieldcode() == null) sascenefieldPojo.setFieldcode("");
        if (sascenefieldPojo.getFieldname() == null) sascenefieldPojo.setFieldname("");
        if (sascenefieldPojo.getFieldtype() == null) sascenefieldPojo.setFieldtype(0);
        if (sascenefieldPojo.getSearchmark() == null) sascenefieldPojo.setSearchmark(0);
        if (sascenefieldPojo.getRownum() == null) sascenefieldPojo.setRownum(0);
        if (sascenefieldPojo.getRemark() == null) sascenefieldPojo.setRemark("");
        if (sascenefieldPojo.getCreateby() == null) sascenefieldPojo.setCreateby("");
        if (sascenefieldPojo.getCreatebyid() == null) sascenefieldPojo.setCreatebyid("");
        if (sascenefieldPojo.getCreatedate() == null) sascenefieldPojo.setCreatedate(new Date());
        if (sascenefieldPojo.getLister() == null) sascenefieldPojo.setLister("");
        if (sascenefieldPojo.getListerid() == null) sascenefieldPojo.setListerid("");
        if (sascenefieldPojo.getModifydate() == null) sascenefieldPojo.setModifydate(new Date());
        if (sascenefieldPojo.getCustom1() == null) sascenefieldPojo.setCustom1("");
        if (sascenefieldPojo.getCustom2() == null) sascenefieldPojo.setCustom2("");
        if (sascenefieldPojo.getCustom3() == null) sascenefieldPojo.setCustom3("");
        if (sascenefieldPojo.getCustom4() == null) sascenefieldPojo.setCustom4("");
        if (sascenefieldPojo.getCustom5() == null) sascenefieldPojo.setCustom5("");
        if (sascenefieldPojo.getRevision() == null) sascenefieldPojo.setRevision(0);
        SaScenefieldEntity sascenefieldEntity = new SaScenefieldEntity();
        BeanUtils.copyProperties(sascenefieldPojo, sascenefieldEntity);

        sascenefieldEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        sascenefieldEntity.setRevision(1);  //乐观锁
        this.sascenefieldMapper.insert(sascenefieldEntity);
        return this.getEntity(sascenefieldEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param sascenefieldPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScenefieldPojo update(SaScenefieldPojo sascenefieldPojo) {
        SaScenefieldEntity sascenefieldEntity = new SaScenefieldEntity();
        BeanUtils.copyProperties(sascenefieldPojo, sascenefieldEntity);
        this.sascenefieldMapper.update(sascenefieldEntity);
        return this.getEntity(sascenefieldEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.sascenefieldMapper.delete(key);
    }

    /**
     * 分页查询
     *
     * @param code 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaScenefieldPojo> getListByCode(String code) {
        return this.sascenefieldMapper.getListByCode(code);
    }

}
