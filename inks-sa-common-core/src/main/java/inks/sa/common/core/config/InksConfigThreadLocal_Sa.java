package inks.sa.common.core.config;

import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.exception.PreAuthorizeException;
import inks.sa.common.core.service.SaRedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static inks.sa.common.core.constant.MyConstant.CONFIG_REDISKEY;

/**
 * 配置ThreadLocal工具类，替代原有的InksConfigThreadLocal
 * 提供直接获取配置的方法，无需使用注解
 * 优化后：首次访问时，一次性加载所有配置到ThreadLocal，避免请求内重复查询
 */
@Component
@DependsOn("flywayMigrationCompleted") // 确保Flyway迁移完成后再初始化
public class InksConfigThreadLocal_Sa {

    private static final Logger logger = LoggerFactory.getLogger(InksConfigThreadLocal_Sa.class);

    // ThreadLocal存储配置，确保线程隔离
    private static final ThreadLocal<Map<String, String>> CONFIG_THREAD_LOCAL = new ThreadLocal<>();

    // 静态实例，用于静态方法调用非静态的Bean（如JdbcTemplate、SaRedisService）
    private static InksConfigThreadLocal_Sa instance;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 利用@PostConstruct注解，在Bean初始化时完成静态实例的赋值
     */
    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 确保配置已加载到当前线程的ThreadLocal中。
     * 这是一个内部核心方法，实现了懒加载（Lazy Load）逻辑。
     * 如果ThreadLocal中没有配置，则调用实例方法从Redis或数据库加载。
     */
    private static void ensureConfigLoaded() {
        if (CONFIG_THREAD_LOCAL.get() == null) {
            try {
                Map<String, String> allConfigs = instance.getConfigFromRedisOrDB();
                // 如果从数据源未获取到任何配置，则存入一个空Map，
                // 这样做可以防止后续重复触发加载，并避免空指针异常。
                CONFIG_THREAD_LOCAL.set(allConfigs != null ? allConfigs : new HashMap<>());
            } catch (Exception e) {
                logger.error("加载配置到ThreadLocal时发生错误", e);
                // 即使在加载过程中发生异常，也设置一个空Map，以保证后续调用此ThreadLocal的代码的健壮性。
                CONFIG_THREAD_LOCAL.set(new HashMap<>());
            }
        }
    }

    /**
     * 获取所有配置
     * @return 配置Map，可能为空Map，但不会为null
     */
    public static Map<String, String> getConfig() {
        ensureConfigLoaded();
        return CONFIG_THREAD_LOCAL.get();
    }

    /**
     * 获取指定的配置项
     * @param key 配置键
     * @return 配置值，如果不存在则返回null
     */
    public static String getConfig(String key) {
        ensureConfigLoaded();
        Map<String, String> config = CONFIG_THREAD_LOCAL.get();
        // 直接从已加载的Map中获取值
        return config.get(key);
    }

    /**
     * 清理ThreadLocal中的配置。
     * 应在请求结束时（例如在Filter的finally块中）调用，以防止内存泄漏。
     */
    public static void clear() {
        CONFIG_THREAD_LOCAL.remove();
    }

    /**
     * 从Redis或数据库获取所有配置。
     * 这个方法是实际的数据获取逻辑，不再需要处理按需获取的逻辑。
     * @return 包含所有配置的Map
     */
    private Map<String, String> getConfigFromRedisOrDB() {
        // 获取当前登录用户，作为获取配置的前提条件
        LoginUser loginUser = this.saRedisService.getLoginUser();
        if (loginUser == null) {
            throw new PreAuthorizeException("userinfo is null, cannot fetch configs");
        }

        // 优先从Redis获取
        Map<String, String> mapConfig = saRedisService.getCacheObject(CONFIG_REDISKEY, new TypeReference<Map<String, String>>() {});

        // 如果Redis中没有，则从数据库获取
        if (mapConfig == null) {
            logger.debug("缓存未命中，从数据库加载配置...");
            mapConfig = this.getMapByTenant();
            // 仅当从数据库成功获取到数据时才进行缓存
            if (mapConfig != null && !mapConfig.isEmpty()) {
                this.saRedisService.setCacheObject(CONFIG_REDISKEY, mapConfig, 30L, TimeUnit.DAYS);
                logger.debug("已将 {} 条配置加载到Redis缓存", mapConfig.size());
            }
        } else {
            logger.debug("从Redis缓存加载了 {} 条配置", mapConfig.size());
        }

        return mapConfig;
    }

    /**
     * 从数据库(Sa_Config)获取所有配置
     * @return 配置Map
     */
    private Map<String, String> getMapByTenant() {
        // SQL语句获取所有配置项
        String sql = "SELECT CfgKey, CfgValue FROM Sa_Config";
        List<Map<String, Object>> rows = this.jdbcTemplate.queryForList(sql);
        // 使用Stream API将查询结果转换为Map<String, String>
        return rows.stream()
                .collect(Collectors.toMap(
                        row -> (String) row.get("CfgKey"),
                        row -> (String) row.get("CfgValue"),
                        (v1, v2) -> v1 // 如果出现重复的CfgKey，保留第一个
                ));
    }
}