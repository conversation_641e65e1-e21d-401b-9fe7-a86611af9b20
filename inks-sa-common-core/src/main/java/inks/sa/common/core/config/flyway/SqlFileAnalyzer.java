package inks.sa.common.core.config.flyway;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL 文件分析器
 * 用于分析 SQL 文件中的表操作，统计总表数
 */
public class SqlFileAnalyzer {
    
    // 用于匹配不同类型SQL语句的正则表达式
    private static final Pattern CREATE_TABLE_PATTERN = Pattern.compile(
        "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern DROP_TABLE_PATTERN = Pattern.compile(
        "DROP\\s+TABLE\\s+(?:IF\\s+EXISTS\\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern INSERT_PATTERN = Pattern.compile(
        "INSERT\\s+INTO\\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern UPDATE_PATTERN = Pattern.compile(
        "UPDATE\\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern DELETE_PATTERN = Pattern.compile(
        "DELETE\\s+FROM\\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 分析 SQL 文件，返回分析结果
     */
    public static SqlAnalysisResult analyzeSqlFile(File sqlFile) {
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        if (sqlFile == null || !sqlFile.exists()) {
            return result;
        }
        
        try {
            System.out.println("开始分析SQL文件: " + sqlFile.getName());
            
            String content = new String(Files.readAllBytes(sqlFile.toPath()));
            String[] lines = content.split("\n");
            
            Set<String> createdTables = new HashSet<>(); // 只统计CREATE的表
            Set<String> allTables = new HashSet<>(); // 统计所有涉及的表

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("--") || line.startsWith("/*")) {
                    continue; // 跳过注释和空行
                }

                // 检查 CREATE TABLE
                Matcher createMatcher = CREATE_TABLE_PATTERN.matcher(line);
                if (createMatcher.find()) {
                    String tableName = createMatcher.group(1);
                    createdTables.add(tableName); // 只记录CREATE的表
                    allTables.add(tableName);
                    result.createTableCount++;
                }

                // 检查 DROP TABLE
                Matcher dropMatcher = DROP_TABLE_PATTERN.matcher(line);
                if (dropMatcher.find()) {
                    String tableName = dropMatcher.group(1);
                    allTables.add(tableName);
                    result.dropTableCount++;
                }

                // 检查 INSERT
                Matcher insertMatcher = INSERT_PATTERN.matcher(line);
                if (insertMatcher.find()) {
                    String tableName = insertMatcher.group(1);
                    allTables.add(tableName);
                    result.insertCount++;
                }

                // 检查 UPDATE
                Matcher updateMatcher = UPDATE_PATTERN.matcher(line);
                if (updateMatcher.find()) {
                    String tableName = updateMatcher.group(1);
                    allTables.add(tableName);
                    result.updateCount++;
                }

                // 检查 DELETE
                Matcher deleteMatcher = DELETE_PATTERN.matcher(line);
                if (deleteMatcher.find()) {
                    String tableName = deleteMatcher.group(1);
                    allTables.add(tableName);
                    result.deleteCount++;
                }
            }

            // 使用CREATE的表数量作为总表数
            result.totalTables = createdTables.size();
            // 只统计表结构相关的操作，不包括数据操作
            result.totalOperations = result.createTableCount + result.dropTableCount;
            
            System.out.println("SQL文件分析完成:");
            System.out.println("  - CREATE TABLE 表数量: " + result.totalTables + " (实际要创建的表)");
            System.out.println("  - CREATE TABLE 操作数: " + result.createTableCount);
            System.out.println("  - DROP TABLE 操作数: " + result.dropTableCount);
            System.out.println("  - INSERT 操作数: " + result.insertCount);
            System.out.println("  - UPDATE 操作数: " + result.updateCount);
            System.out.println("  - DELETE 操作数: " + result.deleteCount);
            System.out.println("  - 总表结构操作数: " + result.totalOperations);

            // 调试：输出前10个创建的表名
            if (createdTables.size() > 0) {
                System.out.println("  - 前10个创建的表: " +
                    createdTables.stream().limit(10).collect(java.util.stream.Collectors.joining(", ")));
            }
            
        } catch (IOException e) {
            System.err.println("分析SQL文件失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * SQL 分析结果
     */
    public static class SqlAnalysisResult {
        public int totalTables = 0;
        public int createTableCount = 0;
        public int dropTableCount = 0;
        public int insertCount = 0;
        public int updateCount = 0;
        public int deleteCount = 0;
        public int totalOperations = 0;
        
        /**
         * 获取预估的总表数（用于进度计算）
         */
        public int getEstimatedTables() {
            // 只返回实际的表数量，不包括数据操作
            return totalTables;
        }
    }
}
