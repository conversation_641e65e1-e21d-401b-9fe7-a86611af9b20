package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaMenuappPojo;
import inks.sa.common.core.mapper.SaRolemenuappMapper;
import inks.sa.common.core.service.SaMenuappService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * APP菜单(Sa_MenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */

@RestController
@RequestMapping("SaMenuApp")
@Api(tags = "通用:APP菜单")
public class A_SaMenuappController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaMenuappController.class);
    @Resource
    private SaMenuappService saMenuappService;
    @Resource
    private SaRolemenuappMapper saRolemenuappMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * @Description 将APP菜单列表List<PimenuappPojo>转为树形结构List<AppMenuPojo>
     */
    public static List<AppMenuPojo> buildAppMenuToChildren(List<SaMenuappPojo> lst) {
        List<AppMenuPojo> list;
        list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1")) {
                AppMenuPojo appMenuPojo = new AppMenuPojo();
                appMenuPojo.setName(lst.get(i).getNavname());
                appMenuPojo.setPath(lst.get(i).getMvcurl());
                appMenuPojo.setMeta(new AppMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss(), lst.get(i).getImagestyle()));
                List<AppMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                        AppMenuPojo appMenuPojo2 = new AppMenuPojo();
                        appMenuPojo2.setName(lst.get(j).getNavname());
                        appMenuPojo2.setPath(lst.get(j).getMvcurl());
                        appMenuPojo2.setMeta(new AppMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss(), lst.get(j).getImagestyle()));
                        List<AppMenuPojo> lstChil2 = new ArrayList<>();
                        for (SaMenuappPojo pimenuappPojo : lst) {
                            if (pimenuappPojo.getNavpid().equals(lst.get(j).getNavid())) {
                                AppMenuPojo appMenuPojo3 = new AppMenuPojo();
                                appMenuPojo3.setName(pimenuappPojo.getNavname());
                                appMenuPojo3.setPath(pimenuappPojo.getMvcurl());
                                appMenuPojo3.setMeta(new AppMetaPojo(pimenuappPojo.getNavname(), pimenuappPojo.getImagecss(), pimenuappPojo.getImagestyle()));
                                lstChil2.add(appMenuPojo3);
                            }
                        }
                        appMenuPojo2.setChildren(lstChil2);
                        lstChil.add(appMenuPojo2);
                    }
                }
                appMenuPojo.setChildren(lstChil);
                list.add(appMenuPojo);
            }

        }
        return list;
    }

    @ApiOperation(value = "项目全菜单 传root时为全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getAllListByPid", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<List<SaMenuappPojo>> getAllListByPid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuappPojo> lst = saMenuappService.getListByPid(key);
            // 向下递归获取子菜单
            return R.ok(flattenMenuList(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 抄接口/getMenuAppListBySelf,只不过把返回改为平行的List<SaMenuappPojo>了
    @ApiOperation(value = "项目全菜单 当前租户的", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByTen", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<List<SaMenuappPojo>> getMenuAppListByTen() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuappPojo> lst = saMenuappService.getListAll();
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目菜单(Children格式) 登录用户userid->roleids->navids->menus  admin拿全部菜单", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByLoginUser", method = RequestMethod.GET)
//-     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<List<AppMenuPojo>> getMenuAppListByLoginUser() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuappPojo> lst;
            if (Objects.equals(loginUser.getIsadmin(), 1) || Objects.equals(loginUser.getIsadmin(), 2)) {//admin拿全部菜单
                lst = saMenuappService.getListAll();
            } else {
                List<String> navids = saRolemenuappMapper.getNavidsByUserid(loginUser.getUserid());
                if (CollectionUtils.isEmpty(navids)) {
//                    throw new Exception("用户未关联菜单权限Navid");
                    return R.ok(new ArrayList<>());
                }
                lst = saMenuappService.getListByNavids(navids);
            }
            // 将Web菜单列表List 转为树形结构List
            return R.ok(buildAppMenuToChildren(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目菜单 roleid->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByRole", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<List<SaMenuappPojo>> getMenuAppListByRole(String roleid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> navids = saRolemenuappMapper.getNavidsByRoleid(roleid);
            if (CollectionUtils.isEmpty(navids)) {
//                throw new Exception("角色未关联菜单权限");
                return R.ok(new ArrayList<>());
            }
            List<SaMenuappPojo> lst = saMenuappService.getListByNavids(navids);
            // 不用向下递归获取子菜单
//            return R.ok(flattenMenuList(lst));
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private List<SaMenuappPojo> flattenMenuList(List<SaMenuappPojo> lst) {
        List<SaMenuappPojo> list = new ArrayList<>();
        for (SaMenuappPojo pimenuappPojo : lst) {
            SaMenuappPojo webMenuPojo = new SaMenuappPojo();
            BeanUtils.copyProperties(pimenuappPojo, webMenuPojo);
            list.add(webMenuPojo);
            List<SaMenuappPojo> lst2 = saMenuappService.getListByPid(pimenuappPojo.getNavid());
            for (SaMenuappPojo pojo : lst2) {
                SaMenuappPojo webMenuPojo2 = new SaMenuappPojo();
                BeanUtils.copyProperties(pojo, webMenuPojo2);
                list.add(webMenuPojo2);
                List<SaMenuappPojo> lst3 = saMenuappService.getListByPid(pojo.getNavid());
                for (SaMenuappPojo value : lst3) {
                    SaMenuappPojo webMenuPojo3 = new SaMenuappPojo();
                    BeanUtils.copyProperties(value, webMenuPojo3);
                    list.add(webMenuPojo3);
                }
            }
        }
        // 使用Stream API根据Navid去重 这种方法利用了Map键的唯一性特性来实现去重。
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        SaMenuappPojo::getNavid, // 使用Navid作为键
                        pojo -> pojo,            // 保留Pojo对象
                        (existing, replacement) -> existing // 如果有重复，保留现有的
                ))
                .values());
    }

//    ====================================代码生成=================================

    @ApiOperation(value = " 获取APP导航详细信息", notes = "获取APP导航详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<SaMenuappPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMenuappService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.List")
    public R<PageInfo<SaMenuappPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MenuApp.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMenuappService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增APP导航", notes = "新增APP导航", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.Add")
    public R<SaMenuappPojo> create(@RequestBody String json) {
        try {
            SaMenuappPojo saMenuappPojo = JSONArray.parseObject(json, SaMenuappPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMenuappPojo.setCreatedate(new Date());   // 创建时间
            saMenuappPojo.setLister(loginUser.getRealname());   // 制表
            saMenuappPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMenuappService.insert(saMenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改APP导航", notes = "修改APP导航", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.Edit")
    public R<SaMenuappPojo> update(@RequestBody String json) {
        try {
            SaMenuappPojo saMenuappPojo = JSONArray.parseObject(json, SaMenuappPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMenuappPojo.setLister(loginUser.getRealname());   // 制表
            saMenuappPojo.setModifydate(new Date());   //修改时间
            //            saMenuappPojo.setAssessor(""); // 审核员
            //            saMenuappPojo.setAssessorid(""); // 审核员id
            //            saMenuappPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMenuappService.update(saMenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除APP导航", notes = "删除APP导航", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMenuappService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuApp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMenuappPojo saMenuappPojo = this.saMenuappService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMenuappPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

