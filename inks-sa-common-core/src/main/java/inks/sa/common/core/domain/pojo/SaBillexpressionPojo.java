package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 单据公式(SaBillexpression)实体类
 *
 * <AUTHOR>
 * @since 2024-05-23 14:40:22
 */
public class SaBillexpressionPojo implements Serializable {
    private static final long serialVersionUID = 829171358707094114L;
     // id
    @Excel(name = "id") 
    private String id;
     // 功能编码
    @Excel(name = "功能编码") 
    private String modulecode;
     // 单据名称
    @Excel(name = "单据名称") 
    private String billname;
     // 源字段集
    @Excel(name = "源字段集") 
    private String orgcolumns;
     // 公式模板
    @Excel(name = "公式模板") 
    private String exprtemp;
     // 目标字段
    @Excel(name = "目标字段") 
    private String tgcolumn;
     // 小数位
    @Excel(name = "小数位") 
    private Integer decnum;
     // 目标字段类型：0数字,1字符串
    @Excel(name = "目标字段类型：0数字,1字符串") 
    private Integer returntype;
     // 有效性
    @Excel(name = "有效性") 
    private Integer enabledmark;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 功能编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
   // 单据名称
    public String getBillname() {
        return billname;
    }
    
    public void setBillname(String billname) {
        this.billname = billname;
    }
        
   // 源字段集
    public String getOrgcolumns() {
        return orgcolumns;
    }
    
    public void setOrgcolumns(String orgcolumns) {
        this.orgcolumns = orgcolumns;
    }
        
   // 公式模板
    public String getExprtemp() {
        return exprtemp;
    }
    
    public void setExprtemp(String exprtemp) {
        this.exprtemp = exprtemp;
    }
        
   // 目标字段
    public String getTgcolumn() {
        return tgcolumn;
    }
    
    public void setTgcolumn(String tgcolumn) {
        this.tgcolumn = tgcolumn;
    }
        
   // 小数位
    public Integer getDecnum() {
        return decnum;
    }
    
    public void setDecnum(Integer decnum) {
        this.decnum = decnum;
    }
        
   // 目标字段类型：0数字,1字符串
    public Integer getReturntype() {
        return returntype;
    }
    
    public void setReturntype(Integer returntype) {
        this.returntype = returntype;
    }
        
   // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

