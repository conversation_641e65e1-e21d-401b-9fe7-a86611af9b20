package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 数据验证(SaValidator)实体类
 *
 * <AUTHOR>
 * @since 2024-11-18 14:23:32
 */
@Data
public class SaValidatorPojo implements Serializable {
    private static final long serialVersionUID = -57123016359214019L;
     // id
    @Excel(name = "id") 
    private String id;
     // 验证编码
    @Excel(name = "验证编码") 
    private String valicode;
     // 标题
    @Excel(name = "标题") 
    private String valititle;
     // 是否SQL
    @Excel(name = "是否SQL") 
    private Integer sqlmark;
     // SQL语句
    @Excel(name = "SQL语句") 
    private String sqlstr;
     // 表达式
    @Excel(name = "表达式") 
    private String expression;
     // 提示语
    @Excel(name = "提示语") 
    private String tipmsg;
     // 提示语(英文)
    @Excel(name = "提示语(英文)") 
    private String tipmsgen;
     // 是否必要
    @Excel(name = "是否必要") 
    private Integer requiredmark;
     // 是否item循环
    @Excel(name = "是否item循环") 
    private Integer itemloopmark;
     // 有效标识
    @Excel(name = "有效标识") 
    private Integer enabledmark;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

