package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaIndeximgEntity;
import inks.sa.common.core.domain.pojo.SaIndeximgPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaIndeximg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
@Mapper
public interface SaIndeximgMapper {


    SaIndeximgPojo getEntity(@Param("key") String key);


    List<SaIndeximgPojo> getPageList(QueryParam queryParam);


    int insert(SaIndeximgEntity saIndeximgEntity);


    int update(SaIndeximgEntity saIndeximgEntity);


    int delete(@Param("key") String key);

}

