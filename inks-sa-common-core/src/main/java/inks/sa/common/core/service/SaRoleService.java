package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaRolePojo;

/**
 * 角色(SaRole)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:31
 */
public interface SaRoleService {


    SaRolePojo getEntity(String key);

    PageInfo<SaRolePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saRolePojo 实例对象
     * @return 实例对象
     */
    SaRolePojo insert(SaRolePojo saRolePojo);

    /**
     * 修改数据
     *
     * @param saRolepojo 实例对象
     * @return 实例对象
     */
    SaRolePojo update(SaRolePojo saRolepojo);

    int delete(String key);
}
