package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaBillcodeEntity;
import inks.sa.common.core.domain.pojo.SaBillcodePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 单据编码(SaBillcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:44
 */
@Mapper
public interface SaBillcodeMapper {


    SaBillcodePojo getEntity(@Param("key") String key, @Param("tid") String tid);


    List<SaBillcodePojo> getPageList(QueryParam queryParam);

    int insert(SaBillcodeEntity sabillcodeEntity);

    int update(SaBillcodeEntity sabillcodeEntity);


    int delete(@Param("key") String key, @Param("tid") String tid);

    SaBillcodeEntity getEntityByModuleCode(@Param("ModuleCode") String ModuleCode, @Param("tid") String tid);

    Map<String, Object> getSerialNo(@Param("entity") SaBillcodeEntity sabillcodeEntity, @Param("subindex") Integer subindex,
                                    @Param("tid") String tid, @Param("currentDate") Date currentDate);

    String getMaxCode(String tablename, String column);
}

