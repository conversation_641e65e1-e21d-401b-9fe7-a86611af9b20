package inks.sa.common.core.service;

import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.SaJustauthEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 第三方登录(SaJustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-18 14:02:00
 */
public interface SaJustauthService {


    SaJustauthPojo getEntity(String key);

    PageInfo<SaJustauthPojo> getPageList(QueryParam queryParam);

    SaJustauthPojo insert(SaJustauthPojo saJustauthPojo);

    SaJustauthPojo update(SaJustauthPojo saJustauthpojo);

    int delete(String key);

    int deleteByAuthUuid(String authuuid);

    SaJustauthPojo getJustauthByUuid(String key, String type, String tid);

    List<JustauthPojo> getListByUnionid(String key);

    SaJustauthPojo getJustauthByUserid(String key, String type, String tid);

    List<JustauthPojo> getAdminListByDeptid(String key, String type, String tenantid);
}
