package inks.sa.common.core.config.flyway;

import inks.sa.common.core.utils.PrintColor;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Flyway 日志拦截器
 * 通过拦截控制台输出来获取真实的执行进度
 */
public class FlywayLogInterceptor {
    
    private final ProgressPageManager progressManager;
    private final PrintStream originalOut;
    private final PrintStream originalErr;
    private final ByteArrayOutputStream capturedOut;
    private final ByteArrayOutputStream capturedErr;
    private final ScheduledExecutorService scheduler;
    private volatile boolean isIntercepting = false;
    
    // 用于匹配日志的正则表达式
    private static final Pattern CREATE_TABLE_PATTERN = Pattern.compile("CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?", Pattern.CASE_INSENSITIVE);
    private static final Pattern DROP_TABLE_PATTERN = Pattern.compile("DROP\\s+TABLE\\s+(?:IF\\s+EXISTS\\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?", Pattern.CASE_INSENSITIVE);
    private static final Pattern UNKNOWN_TABLE_PATTERN = Pattern.compile("Unknown table\\s+'[^']*\\.([^']+)'");
    private static final Pattern ROWS_AFFECTED_PATTERN = Pattern.compile("(\\d+)\\s+rows affected");

    // 新增：更多表操作模式
    private static final Pattern TABLE_OPERATION_PATTERN = Pattern.compile("(?:INSERT INTO|UPDATE|DELETE FROM|ALTER TABLE|TRUNCATE TABLE)\\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?", Pattern.CASE_INSENSITIVE);
    private static final Pattern FLYWAY_EXECUTING_PATTERN = Pattern.compile("Executing SQL statement:\\s*(?:CREATE|DROP|INSERT|UPDATE|DELETE|ALTER|TRUNCATE).*?\\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?", Pattern.CASE_INSENSITIVE);
    
    private int sqlStatementCount = 0;
    private String lastProcessedLine = ""; // 改为记录最后处理的日志行，避免重复处理同一行
    
    public FlywayLogInterceptor(ProgressPageManager progressManager) {
        this.progressManager = progressManager;
        this.originalOut = System.out;
        this.originalErr = System.err;
        this.capturedOut = new ByteArrayOutputStream();
        this.capturedErr = new ByteArrayOutputStream();
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
    }
    
    /**
     * 开始拦截日志
     */
    public void startIntercepting() {
        if (isIntercepting) {
            return;
        }

        isIntercepting = true;

        try {
            // 重定向标准输出和错误输出
            System.setOut(new PrintStream(new TeeOutputStream(originalOut, capturedOut)));
            System.setErr(new PrintStream(new TeeOutputStream(originalErr, capturedErr)));

            // 开始监控，但不添加到HTML日志
        PrintColor.red("开始监控 Flyway 执行日志...");

            // 每200毫秒检查一次捕获的日志（更频繁）
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    processCapuredLogs();
                } catch (Exception e) {
                    // 记录错误但继续执行
                    System.err.println("处理日志时发生错误: " + e.getMessage());
                }
            }, 200, 200, TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            progressManager.addLog("启动日志监控失败: " + e.getMessage());
            isIntercepting = false;
        }
    }
    
    /**
     * 停止拦截日志
     */
    public void stopIntercepting() {
        if (!isIntercepting) {
            return;
        }
        
        isIntercepting = false;
        
        // 恢复原始输出流
        System.setOut(originalOut);
        System.setErr(originalErr);
        
        // 停止调度器
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 处理最后的日志
        processCapuredLogs();
        
        PrintColor.red("停止监控 Flyway 执行日志");
    }
    
    /**
     * 处理捕获的日志
     */
    private void processCapuredLogs() {
        try {
            // 处理标准输出
            String outContent = capturedOut.toString();
            if (!outContent.isEmpty()) {
                processLogContent(outContent);
                capturedOut.reset();
            }

            // 处理错误输出
            String errContent = capturedErr.toString();
            if (!errContent.isEmpty()) {
                processLogContent(errContent);
                capturedErr.reset();
            }
        } catch (Exception e) {
            // 记录错误但继续执行
            progressManager.addLog("处理日志时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 处理日志内容
     */
    private void processLogContent(String content) {
        String[] lines = content.split("\n");

        for (String line : lines) {
            if (line.trim().isEmpty()) {
                continue;
            }

            // 移除调试信息，只关注表操作

            // 避免重复处理同一行日志
            if (line.equals(lastProcessedLine)) {
                return;
            }
            lastProcessedLine = line;

            // 检查是否包含表操作 - 只计算CREATE TABLE操作
            boolean tableOperationFound = false;

            if (line.contains("CREATE TABLE")) {
                Matcher matcher = CREATE_TABLE_PATTERN.matcher(line);
                if (matcher.find()) {
                    String tableName = matcher.group(1);
                    sqlStatementCount++;
                    progressManager.addTableLog(tableName, "创建表");
                    tableOperationFound = true;
                }
            } else if (line.contains("DROP TABLE")) {
                Matcher matcher = DROP_TABLE_PATTERN.matcher(line);
                if (matcher.find()) {
                    String tableName = matcher.group(1);
                    sqlStatementCount++;
                    // DROP TABLE 操作不计入表处理进度，只记录日志
                    progressManager.addLog("删除表: " + tableName + " (正常操作)");
                    tableOperationFound = true;
                }
            } else if (line.contains("Unknown table")) {
                Matcher matcher = UNKNOWN_TABLE_PATTERN.matcher(line);
                if (matcher.find()) {
                    String tableName = matcher.group(1);
                    sqlStatementCount++;
                    // Unknown table 通常意味着正在创建表（先DROP再CREATE），计入创建表进度
                    progressManager.addTableLog(tableName, "正常操作");
                    tableOperationFound = true;
                }
            }

            // 如果没有匹配到具体的表操作，尝试通用的表操作模式
            if (!tableOperationFound) {
                // 检查Flyway执行日志
                Matcher flywayMatcher = FLYWAY_EXECUTING_PATTERN.matcher(line);
                if (flywayMatcher.find()) {
                    String tableName = flywayMatcher.group(1);
                    sqlStatementCount++;
                    progressManager.addTableLog(tableName, "执行操作");
                    tableOperationFound = true;
                }

                // 检查其他表操作
                if (!tableOperationFound) {
                    Matcher tableMatcher = TABLE_OPERATION_PATTERN.matcher(line);
                    if (tableMatcher.find()) {
                        String tableName = tableMatcher.group(1);
                        sqlStatementCount++;
                        progressManager.addTableLog(tableName, "表操作");
                        tableOperationFound = true;
                    }
                }
            }
            // 移除其他冗余日志，只保留表处理信息
        }
    }
    
    /**
     * 更新进度
     */
    private void updateProgress() {
        // 基于已处理的SQL语句数量更新进度
        // 假设大部分进度在75-95%之间
        int progress = Math.min(95, 75 + (sqlStatementCount / 10));
        progressManager.updateProgress("正在执行数据库迁移... (已处理 " + sqlStatementCount + " 个操作)", progress);
    }
    
    /**
     * 双向输出流，同时写入两个目标
     */
    private static class TeeOutputStream extends java.io.OutputStream {
        private final java.io.OutputStream out1;
        private final java.io.OutputStream out2;
        
        public TeeOutputStream(java.io.OutputStream out1, java.io.OutputStream out2) {
            this.out1 = out1;
            this.out2 = out2;
        }
        
        @Override
        public void write(int b) throws java.io.IOException {
            out1.write(b);
            out2.write(b);
        }
        
        @Override
        public void write(byte[] b) throws java.io.IOException {
            out1.write(b);
            out2.write(b);
        }
        
        @Override
        public void write(byte[] b, int off, int len) throws java.io.IOException {
            out1.write(b, off, len);
            out2.write(b, off, len);
        }
        
        @Override
        public void flush() throws java.io.IOException {
            out1.flush();
            out2.flush();
        }
        
        @Override
        public void close() throws java.io.IOException {
            out1.close();
            out2.close();
        }
    }
}
