//package inks.sa.common.core.config.oss.service;
//
//
//import com.aliyun.oss.HttpMethod;
//import com.aliyun.oss.OSS;
//import com.aliyun.oss.model.GeneratePresignedUrlRequest;
//import com.aliyun.oss.model.OSSObject;
//import com.aliyun.oss.model.ResponseHeaderOverrides;
//import inks.common.core.domain.R;
//import inks.sa.common.core.config.StorageException;
//import inks.sa.common.core.config.oss.utils.StorageType;
//import inks.sa.common.core.service.SaRedisService;
//import inks.sa.common.core.utils.PrintColor;
//import io.minio.GetObjectArgs;
//import io.minio.GetPresignedObjectUrlArgs;
//import io.minio.MinioClient;
//import io.minio.http.Method;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import org.apache.commons.io.FilenameUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.servlet.HandlerMapping;
//import org.springframework.web.servlet.view.RedirectView;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.InputStream;
//import java.io.OutputStream;
//import java.io.UnsupportedEncodingException;
//import java.net.URI;
//import java.net.URLEncoder;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
///**
// * @Description 文件上传下载控制器
// * <AUTHOR>
// * @CreateTime 2021/5/31 10:28
// */
////@RestController
////@RequestMapping("/File")
////@Api(tags = "File:文件中心Controller层")
//public class FileOrgController extends File_VideoController{
//    // 最大图片上传大小10M
//    public static final long MAX_IMAGE_SIZE = 10 * 1024 * 1024;
//    private static final Logger log = LoggerFactory.getLogger(FileOrgController.class);
//    //    @RequestMapping("/redirect")
////    public RedirectView redirect(){
////        RedirectView redirectView = new RedirectView();
////        redirectView.setUrl("https://www.example.com");
////        return redirectView;
////    }
//    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
//    @Autowired
//    @Qualifier("fileInfoAliYunServiceImpl")
//    private OssService fileInfoAliYunService; //aliyun上传
//    @Autowired
//    @Qualifier("fileInfoMinioServiceImpl")
//    private OssService fileInfoMinioService; //minio上传
//    @Autowired
//    private OSSConfigManager configManager;
//
//
//    @Autowired
//    private OSS ossClient; //AliYunClient
//    @Resource
//    private MinioClient minioClient;
//    @Resource
//    private SaRedisService saRedisService;
//
//
//    @ApiOperation(value = "刷新阿里云OSS全量配置(带连接测试结果) 读取Sa_Config中的oss.aliyun.*配置")
//    @PostMapping("/updateAliYunStorage")
//    public R<String> updateAliYunStorage() {
//        try {
//            // 强制刷新所有阿里云相关配置
//            configManager.refreshAliyunConfig();
//
//            // 获取最新配置
//            String endpoint = configManager.getAliyunEndpoint();
//            String accessKeyId = configManager.getAliyunAccessKeyId();
//            String accessKeySecret = configManager.getAliyunAccessKeySecret();
//
//            // 更新存储服务
//            fileInfoAliYunService.updateStorage(endpoint, accessKeyId, accessKeySecret);
//
//            return R.ok("阿里云配置刷新成功\n当前Endpoint: " + endpoint +
//                    "\n当前AccessKeyId: " + accessKeyId +
//                    "\n当前AccessKeySecret: " + accessKeySecret);
//        } catch (Exception e) {
//            PrintColor.red("阿里云配置刷新失败: " + e.getMessage());
//            return R.fail("配置刷新失败: " + e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "刷新MinIO全量配置(带连接测试结果) 读取Sa_Config中的oss.minio.*配置")
//    @PostMapping("/updateMinioStorage")
//    public R<String> updateMinioStorage() {
//        try {
//            // 强制刷新所有MinIO相关配置
//            configManager.refreshMinioConfig();
//
//            // 获取最新配置
//            String endpoint = configManager.getMinioEndpoint();
//            String accessKey = configManager.getMinioAccessKey();
//            String secretKey = configManager.getMinioSecretKey();
//
//            // 更新存储服务
//            fileInfoMinioService.updateStorage(endpoint, accessKey, secretKey);
//
//            return R.ok("MinIO配置刷新成功\n当前Endpoint: " + endpoint +
//                    "\n当前AccessKey: " + accessKey +
//                    "\n当前SecretKey: " + secretKey);
//        } catch (Exception e) {
//            PrintColor.red("MinIO配置刷新失败: " + e.getMessage());
//            return R.fail("配置刷新失败: " + e.getMessage());
//        }
//    }
//
//
//
//
//
//
//
//
//
////       ----------------------minio,aliyun预览下载文件接口----------------
//
//    private static final long URL_EXPIRATION_TIME = 7 * 24 * 3600 * 1000L; // 7天
//    private static final long CACHE_EXPIRATION_BUFFER = 300000L; // 5分钟
//    private final ConcurrentHashMap<String, SignedUrlCache> urlCache = new ConcurrentHashMap<>();
//
//    // 统一的预览接口 代理
//    @GetMapping(value = {"getAliOssUrl/**", "getMinioUrl/**", "getUrl/**"})
//    public RedirectView getStorageUrl(HttpServletRequest request) {
//        String objectName = getObjectNameFromRequest(request);
//        // 如果是"/getUrl"路径，使用YML配置的存储类型
//        StorageType storageType;
//        if (request.getRequestURI().contains("getUrl")) {
//            // 使用YML中配置的类型
//            storageType = StorageType.valueOf(configManager.getOssType().toUpperCase());
//        } else {
//            // 默认通过路径判断存储类型
//            storageType = request.getRequestURI().contains("AliOss") ?
//                    StorageType.ALIYUN : StorageType.MINIO;
//        }
//        // 通过缓存的文件签名读取/下载文件
//        String signedUrl = generateSignedUrl(objectName, false, storageType);
//
//        RedirectView redirectView = new RedirectView();
//        redirectView.setUrl(signedUrl);
//        return redirectView;
//    }
//
//    // 统一的下载接口
//    @GetMapping(value = {"downloadAliOssUrl/**", "downloadMinioUrl/**", "downloadUrl/**"})
//    public ResponseEntity<Object> downloadStorageUrl(HttpServletRequest request) {
//        try {
//            String objectName = getObjectNameFromRequest(request);
//            // 如果是"/downloadUrl"路径，使用YML配置的存储类型
//            StorageType storageType;
//            if (request.getRequestURI().contains("downloadUrl")) {
//                // 使用YML中配置的类型
//                storageType = StorageType.valueOf(configManager.getOssType().toUpperCase());
//            } else {
//                // 默认通过路径判断存储类型
//                storageType = request.getRequestURI().contains("AliOss") ?
//                        StorageType.ALIYUN : StorageType.MINIO;
//            }
//            // 通过缓存的文件签名读取/下载文件
//            String signedUrl = generateSignedUrl(objectName, true, storageType);
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.setLocation(URI.create(signedUrl));
//            return new ResponseEntity<>(headers, HttpStatus.FOUND);
//
//        } catch (StorageException e) {
//            log.error("Storage operation failed", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Storage Error: " + e.getMessage());
//        } catch (Exception e) {
//            log.error("Unexpected error", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body("Unexpected Error: " + e.getMessage());
//        }
//    }
//
//    // 缓存常用的Content-Type映射，提高性能
//    private static final Map<String, String> CONTENT_TYPE_MAP = new ConcurrentHashMap<>();
//
//    static {
//        CONTENT_TYPE_MAP.put("gif", "image/gif");
//        CONTENT_TYPE_MAP.put("jpg", "image/jpeg");
//        CONTENT_TYPE_MAP.put("jpeg", "image/jpeg");
//        CONTENT_TYPE_MAP.put("png", "image/png");
//        CONTENT_TYPE_MAP.put("pdf", "application/pdf");
//        CONTENT_TYPE_MAP.put("txt", "text/plain");
//        CONTENT_TYPE_MAP.put("html", "text/html");
//        CONTENT_TYPE_MAP.put("htm", "text/html");
//        CONTENT_TYPE_MAP.put("xml", "text/xml");
//        CONTENT_TYPE_MAP.put("json", "application/json");
//        CONTENT_TYPE_MAP.put("mp4", "video/mp4");
//        CONTENT_TYPE_MAP.put("mp3", "audio/mpeg");
//    }
//
//    // 当不明确ali还是minio时，调用/proxy 走Yml中的oss.type
//    @GetMapping(value = {"proxy/**"})
//    public void proxy(HttpServletRequest request, HttpServletResponse response, String bn) {
//        String objectName = null;
//        try {
//            objectName = getObjectNameFromRequest(request);
//            validateObjectName(objectName);  // 安全检查
//            // 如果是"/proxy"路径，使用YML配置的存储类型
//            StorageType storageType;
//            // 使用YML中配置的类型
//            storageType = StorageType.valueOf(configManager.getOssType().toUpperCase());
//            // 设置响应头
//            configureResponseHeaders(response, objectName);
//            // 获取并传输文件流
//            transferFileStream(objectName, storageType, response);
//        } catch (IllegalArgumentException e) {
//            handleError(response, HttpServletResponse.SC_BAD_REQUEST,
//                    "无效的文件名: " + objectName, e);
//        } catch (SecurityException e) {
//            handleError(response, HttpServletResponse.SC_FORBIDDEN,
//                    "访问被拒绝: " + objectName, e);
//        } catch (Exception e) {
//            handleError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
//                    "处理文件时发生错误: " + objectName, e);
//        }
//    }
//
//
//    // 当不明确ali还是minio时，调用/proxy 走Yml中的oss.type
//    @GetMapping(value = {"getAliOssProxy/**", "getMinioProxy/**", "proxy/**"})
//    public void getStorageProxy(HttpServletRequest request, HttpServletResponse response, String bn) {
//        String objectName = null;
//        try {
//            objectName = getObjectNameFromRequest(request);
//            validateObjectName(objectName);  // 安全检查
//
//            // 如果是"/proxy"路径，使用YML配置的存储类型
//            StorageType storageType;
//            if (request.getRequestURI().contains("proxy")) {
//                // 使用YML中配置的类型
//                storageType = StorageType.valueOf(configManager.getOssType().toUpperCase());
//            } else {
//                // 默认通过路径判断存储类型
//                storageType = request.getRequestURI().contains("AliOss") ?
//                        StorageType.ALIYUN : StorageType.MINIO;
//            }
//
//            // 设置响应头
//            configureResponseHeaders(response, objectName);
//
//            // 获取并传输文件流
//            transferFileStream(objectName, storageType, response);
//
//        } catch (IllegalArgumentException e) {
//            handleError(response, HttpServletResponse.SC_BAD_REQUEST,
//                    "无效的文件名: " + objectName, e);
//        } catch (SecurityException e) {
//            handleError(response, HttpServletResponse.SC_FORBIDDEN,
//                    "访问被拒绝: " + objectName, e);
//        } catch (Exception e) {
//            handleError(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
//                    "处理文件时发生错误: " + objectName, e);
//        }
//    }
//
//    private void validateObjectName(String objectName) {
//        if (objectName == null || objectName.isEmpty()) {
//            throw new IllegalArgumentException("文件名不能为空");
//        }
//        // 防止目录遍历攻击
//        if (objectName.contains("..") || objectName.contains("//")) {
//            throw new SecurityException("检测到非法的文件路径");
//        }
//    }
//
//    private void configureResponseHeaders(HttpServletResponse response, String objectName) {
//        response.setContentType(determineContentType(objectName));
//        response.setHeader("Cache-Control", "max-age=604800"); // 7天缓存
//        response.setHeader("X-Content-Type-Options", "nosniff");
//        response.setHeader("X-XSS-Protection", "1; mode=block");
//    }
//
//    private void transferFileStream(String objectName, StorageType storageType,
//                                    HttpServletResponse response) throws Exception {
//        try (InputStream inputStream = getObjectStream(objectName, storageType);
//             OutputStream outputStream = response.getOutputStream()) {
//
//            byte[] buffer = new byte[8192];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, bytesRead);
//            }
//            outputStream.flush();
//        }
//    }
//
//    private InputStream getObjectStream(String objectName, StorageType storageType) throws Exception {
//        try {
//            if (storageType == StorageType.ALIYUN) {
//                return getAliOssStream(objectName);
//            } else {
//                return getMinioStream(objectName);
//            }
//        } catch (Exception e) {
//            log.error("获取文件流失败 - {}: {}", storageType, objectName, e);
//            throw new IOException("获取文件流失败", e);
//        }
//    }
//
//    private InputStream getAliOssStream(String objectName) {
//        OSSObject ossObject = ossClient.getObject(configManager.getAliyunBucket(), objectName);
//        return ossObject.getObjectContent();
//    }
//
//    private InputStream getMinioStream(String objectName) throws Exception {
//        return minioClient.getObject(
//                GetObjectArgs.builder()
//                        .bucket(configManager.getMinioBucket())
//                        .object(objectName)
//                        .build()
//        );
//    }
//
//    private String determineContentType(String objectName) {
//        String extension = FilenameUtils.getExtension(objectName).toLowerCase();
//        return CONTENT_TYPE_MAP.getOrDefault(extension, "application/octet-stream");
//    }
//
//    private void handleError(HttpServletResponse response, int status, String message, Exception e) {
//        log.error(message, e);
//        try {
//            response.sendError(status, message);
//        } catch (IOException ex) {
//            log.error("发送错误响应失败", ex);
//        }
//    }
//
//
//    @Scheduled(fixedRate = 3600000) // 每小时执行一次
//    public void cleanExpiredCache() {
//        int beforeSize = urlCache.size();
//        urlCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
//        int afterSize = urlCache.size();
//        log.info("清理过期缓存完成(minio,aliyun图片签名缓存)，清理前：{}，清理后：{}", beforeSize, afterSize);
//    }
//
//    @Data
//    @AllArgsConstructor
//    private static class SignedUrlCache {
//        private String url;
//        private Date expirationTime;
//        private boolean isDownload;
//
//        public boolean isExpired() {
//            return new Date().after(expirationTime);
//        }
//    }
//
//    // 统一的URL获取方法
//    private String getObjectNameFromRequest(HttpServletRequest request) {
//        String uri = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
//        String pattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
//        return antPathMatcher.extractPathWithinPattern(pattern, uri);
//    }
//
//    // 统一的签名URL生成方法
//    private String generateSignedUrl(String objectName, boolean isDownload, StorageType storageType) {
//        String cacheKey = getCacheKey(objectName, isDownload, storageType);
//        SignedUrlCache cachedUrl = urlCache.get(cacheKey);
//
//        if (cachedUrl != null && !cachedUrl.isExpired()) {
//            log.info("使用缓存中的{}签名，key: {}", storageType, cacheKey);
//            return cachedUrl.getUrl();
//        }
//
//        try {
//            String signedUrl = storageType == inks.sa.common.core.config.oss.utils.StorageType.ALIYUN ?
//                    generateAliOssSignedUrl(objectName, isDownload) :
//                    generateMinioSignedUrl(objectName, isDownload);
//
//            // 存入缓存
//            cacheSignedUrl(cacheKey, signedUrl, isDownload);
//            return signedUrl;
//
//        } catch (Exception e) {
//            log.error("生成{}签名URL失败: ", storageType, e);
//            throw new StorageException("生成签名URL失败", e);
//        }
//    }
//
//    // AliOSS签名URL生成
//    private String generateAliOssSignedUrl(String objectName, boolean isDownload) throws UnsupportedEncodingException {
//        Date expiration = new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME);
//        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(configManager.getAliyunBucket(), objectName, HttpMethod.GET);
//        request.setExpiration(expiration);
//
//        if (isDownload) {
//            String fileName = getFileNameFromPath(objectName);
//            ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides();
//            responseHeaders.setContentDisposition("attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
//            request.setResponseHeaders(responseHeaders);
//        }
//
//        return ossClient.generatePresignedUrl(request).toString();
//    }
//
//    // MinIO签名URL生成
//    private String generateMinioSignedUrl(String objectName, boolean isDownload) throws Exception {
//        GetPresignedObjectUrlArgs.Builder builder = GetPresignedObjectUrlArgs.builder()
//                .method(Method.GET)
//                .bucket(configManager.getMinioBucket())
//                .object(objectName);
//
//        if (isDownload) {
//            String fileName = getFileNameFromPath(objectName);
//            Map<String, String> reqParams = new HashMap<>();
//            reqParams.put("response-content-disposition",
//                    "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
//            builder.extraQueryParams(reqParams);
//        }
//
//        return minioClient.getPresignedObjectUrl(builder.build());
//    }
//
//    // 缓存签名URL
//    private void cacheSignedUrl(String cacheKey, String signedUrl, boolean isDownload) {
//        SignedUrlCache newCache = new SignedUrlCache(
//                signedUrl,
//                new Date(System.currentTimeMillis() + URL_EXPIRATION_TIME - CACHE_EXPIRATION_BUFFER),
//                isDownload
//        );
//        urlCache.put(cacheKey, newCache);
//        log.info("新的签名URL已缓存，key: {}", cacheKey);
//    }
//
//    private String getCacheKey(String objectName, boolean isDownload, StorageType storageType) {
//        return String.format("%s:%s:%s", storageType, objectName, isDownload);
//    }
//
//    private String getFileNameFromPath(String objectName) {
//        return objectName.substring(objectName.lastIndexOf("/") + 1);
//    }
//
//
//}
