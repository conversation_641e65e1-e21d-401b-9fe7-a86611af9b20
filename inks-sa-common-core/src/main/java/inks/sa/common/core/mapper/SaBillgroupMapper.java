package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaBillgroupEntity;
import inks.sa.common.core.domain.pojo.SaBillgroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通用分组(SaBillgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-02 14:47:57
 */
@Mapper
public interface SaBillgroupMapper {


    SaBillgroupPojo getEntity(@Param("key") String key);


    List<SaBillgroupPojo> getPageList(QueryParam queryParam);


    int insert(SaBillgroupEntity saBillgroupEntity);

    int update(SaBillgroupEntity saBillgroupEntity);


    int delete(@Param("key") String key);

    List<SaBillgroupPojo> getListByModuleCode(String moduleCode);
}

