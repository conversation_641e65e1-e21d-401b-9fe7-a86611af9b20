package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaLoginlogPojo;
import inks.sa.common.core.domain.SaLoginlogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 登录日志(SaLoginlog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-24 10:10:27
 */
public interface SaLoginlogService {


    SaLoginlogPojo getEntity(String key);

    PageInfo<SaLoginlogPojo> getPageList(QueryParam queryParam);

    SaLoginlogPojo insert(SaLoginlogPojo saLoginlogPojo);

    SaLoginlogPojo update(SaLoginlogPojo saLoginlogpojo);

    int delete(String key);
}
