package inks.sa.common.core.utils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式工具类，用于评估表达式并返回布尔值 包括数学表达式、字符串表达式、逻辑运算表达式
 * <AUTHOR>
 */
public class ExpressionUtils {
    // 初始化 JavaScript 引擎
    private static final ScriptEngine engine;

    static {
        // 创建脚本引擎管理器并初始化 JavaScript 引擎
        ScriptEngineManager manager = new ScriptEngineManager();
        engine = manager.getEngineByName("JavaScript");
    }


    // 示例调用
    public static void main(String[] args) {
        // 测试数学表达式
        System.out.println(evaluateExpression("5 + 2 * (3 - 1) == 9")); // true
        System.out.println(evaluateExpression("3 * 3 > 9")); // false
        System.out.println(evaluateExpression("5 < 10")); // true
        System.out.println(evaluateExpression("7 >= 7")); // true
        System.out.println(evaluateExpression("10 != 5 + 5")); // false

        // 测试字符串表达式
        System.out.println(evaluateExpression("\"Hello World\".contains(\"World\")"));     // true
        System.out.println(evaluateExpression("\"Hello\".equals(\"hello\")"));           // false
        System.out.println(evaluateExpression("\"Hello\".equalsIgnoreCase(\"hello\")"));  // true
        System.out.println(evaluateExpression("\"<EMAIL>\".matches(\".*@.*\")"));  // true
    }


    /**
     * 评估给定的表达式
     * @param expression 需要评估的表达式
     * @return 返回布尔值，表示表达式是否成立
     */
    public static boolean evaluateExpression(String expression) {
        try {
            // 如果表达式包含字符串函数调用，则使用字符串表达式评估
            if (expression.contains("\".")) {
                return evaluateStringExpression(expression);
            }

            // 否则处理数学表达式和比较表达式
            return evaluateMathExpression(expression);
        } catch (Exception e) {
            // 捕获异常并抛出运行时异常
            throw new RuntimeException("Expression evaluation failed: " + e.getMessage(), e);
        }
    }

    /**
     * 处理字符串函数调用的表达式，如 "Hello".contains("Hell")
     * @param expression 字符串表达式
     * @return 返回布尔值，表示字符串表达式的结果
     */
    private static boolean evaluateStringExpression(String expression) {
        // 匹配类似 "string".method("argument") 的表达式
        Pattern pattern = Pattern.compile("\"([^\"]*)\"\\.(\\w+)\\(\"([^\"]*)\"\\)");
        Matcher matcher = pattern.matcher(expression);

        // 如果匹配到表达式
        if (matcher.find()) {
            String sourceStr = matcher.group(1);  // 提取字符串
            String method = matcher.group(2);     // 提取方法名
            String arg = matcher.group(3);        // 提取方法参数
            // 根据方法名进行对应的字符串操作
            switch (method.toLowerCase()) {
                case "contains":
                    return sourceStr.contains(arg);  // 检查字符串是否包含指定子串
                case "startswith":
                    return sourceStr.startsWith(arg);  // 检查字符串是否以指定子串开始
                case "endswith":
                    return sourceStr.endsWith(arg);  // 检查字符串是否以指定子串结束
                case "equals":
                    return sourceStr.equals(arg);  // 检查字符串是否相等
                case "equalsignorecase":
                    return sourceStr.equalsIgnoreCase(arg);  // 检查字符串是否忽略大小写相等
                case "matches":
                    return sourceStr.matches(arg);  // 检查字符串是否符合正则表达式
                case "length":
                    return sourceStr.length() == Integer.parseInt(arg);  // 检查字符串的长度是否等于给定值
                case "indexof":
                    return sourceStr.indexOf(arg) >= 0;  // 检查指定子串是否存在于字符串中
                case "toLowerCase":
                    return sourceStr.toLowerCase().equals(arg.toLowerCase());  // 比较忽略大小写的字符串
                case "toUpperCase":
                    return sourceStr.toUpperCase().equals(arg.toUpperCase());  // 比较忽略大小写的字符串
                case "trim":
                    return sourceStr.trim().equals(arg);  // 比较去除前后空格后的字符串
                case "isEmpty":
                    return sourceStr.isEmpty();  // 检查字符串是否为空
                case "isBlank":
                    return sourceStr.trim().isEmpty();  // 检查字符串是否仅包含空格或为空
                case "startsWithignorecase":
                    return sourceStr.toLowerCase().startsWith(arg.toLowerCase());  // 忽略大小写判断开头
                case "endsWithignorecase":
                    return sourceStr.toLowerCase().endsWith(arg.toLowerCase());  // 忽略大小写判断结尾
                default:
                    // 如果方法不在支持范围内，抛出异常
                    throw new IllegalArgumentException("Unsupported string method: " + method);
            }
        }

        // 如果表达式格式不正确，抛出异常
        throw new IllegalArgumentException("Invalid string expression format");
    }

    /**
     * 处理数学和逻辑运算表达式
     * @param expression 数学表达式
     * @return 返回布尔值，表示表达式的结果
     * @throws ScriptException 如果表达式执行失败
     */
    private static boolean evaluateMathExpression(String expression) throws ScriptException {
        // 将 Java 风格的运算符替换为 JavaScript 风格的运算符
        expression = expression.replace("&&", "&amp;&amp;")
                .replace("||", "||");

        // 使用 JavaScript 引擎评估表达式
        Object result = engine.eval(expression);

        // 确保结果是布尔值
        if (result instanceof Boolean) {
            return (Boolean) result;
        } else {
            // 如果结果不是布尔值，则抛出异常
            throw new IllegalArgumentException("Expression does not evaluate to a boolean");
        }
    }


}
