package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.sa.common.core.domain.pojo.SadgformatPojo;
import inks.sa.common.core.domain.pojo.SadgformatitemPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SadgformatService;
import inks.sa.common.core.service.SadgformatitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 列表格式(Sadgformat)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-20 12:43:04
 */

@RestController
@RequestMapping("SaDgFormat")
@Api(tags = "通用:列表格式")
public class A_SadgformatController {

    @Resource
    private SadgformatService sadgformatService;

    /**
     * 服务对象Item
     */
    @Resource
    private SadgformatitemService sadgformatitemService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "修改列表格式", notes = "修改列表格式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SadgformatPojo> update(@RequestBody String json) {
        try {
            SadgformatPojo sadgformatPojo = JSONArray.parseObject(json, SadgformatPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            SadgformatPojo billEntityByCode = this.sadgformatService.getBillEntityByCode(sadgformatPojo.getFormcode(), loginUser.getUserid(), loginUser.getTenantid());
            // 是默认数据
            // 当前操作是否为管理员
            if (billEntityByCode == null) {
                // 租户在新建
                sadgformatPojo.setCreateby(loginUser.getRealName());   // 创建者
                sadgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                sadgformatPojo.setCreatedate(new Date());   // 创建时间
                sadgformatPojo.setLister(loginUser.getRealname());   // 制表
                sadgformatPojo.setListerid(loginUser.getUserid());    // 制表id
                sadgformatPojo.setModifydate(new Date());   //修改时间
                sadgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
                sadgformatPojo.setTenantname(loginUser.getTenantinfo().getTenantname());   //租户id
                sadgformatPojo.setDefmark(0);
                return R.ok(this.sadgformatService.insert(sadgformatPojo));
            } else {
                // 管理员在修改
                sadgformatPojo.setLister(loginUser.getRealname());   // 制表
                sadgformatPojo.setListerid(loginUser.getUserid());    // 制表id
                sadgformatPojo.setModifydate(new Date());   //修改时间
                sadgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
                return R.ok(this.sadgformatService.update(sadgformatPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param code 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取(个人)列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByCode", method = RequestMethod.GET)
    public R<SadgformatPojo> getBillEntityByCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatService.getBillEntityByCode(code, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取(默认的)列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getTenBillEntityByCode", method = RequestMethod.GET)
    public R<SadgformatPojo> getTenBillEntityByCode(String code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatService.getTenBillEntityByCode(code, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员保存(默认的)列表格式,无则插入;有则修改", notes = "修改列表格式", produces = "application/json")
    @RequestMapping(value = "/updateTen", method = RequestMethod.POST)
    public R<SadgformatPojo> updateTen(@RequestBody String json) {
        try {
            SadgformatPojo sadgformatPojo = JSONArray.parseObject(json, SadgformatPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            if (loginUser.getIsadmin() != 1) {
                return R.fail("非管理员禁止保存");
            }
            SadgformatPojo tenBillEntityByCode = this.sadgformatService.getTenBillEntityByCode(sadgformatPojo.getFormcode(), loginUser.getTenantid());
            if (tenBillEntityByCode != null) {
                sadgformatService.delete(tenBillEntityByCode.getId(), loginUser.getTenantid());
            }
            sadgformatPojo.setLister(loginUser.getRealname());   // 制表
            sadgformatPojo.setListerid(loginUser.getUserid());    // 制表id
            sadgformatPojo.setModifydate(new Date());   //修改时间
            sadgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            sadgformatPojo.setCreateby(loginUser.getRealname());   // 创建者
            sadgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            sadgformatPojo.setCreatedate(new Date());   // 创建时间
            sadgformatPojo.setDefmark(1);
            return R.ok(this.sadgformatService.insert(sadgformatPojo), "插入成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.List")
    public R<SadgformatPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.List")
    public R<SadgformatPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.List")
    public R<PageInfo<SadgformatPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DgFormat.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.sadgformatService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.List")
    public R<PageInfo<SadgformatPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DgFormat.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.sadgformatService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增列表格式", notes = "新增列表格式", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SadgformatPojo> create(@RequestBody String json) {
        try {
            SadgformatPojo sadgformatPojo = JSONArray.parseObject(json, SadgformatPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sadgformatPojo.setCreateby(loginUser.getRealname());   // 创建者
            sadgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            sadgformatPojo.setCreatedate(new Date());   // 创建时间
            sadgformatPojo.setLister(loginUser.getRealname());   // 制表
            sadgformatPojo.setListerid(loginUser.getUserid());    // 制表id            
            sadgformatPojo.setModifydate(new Date());   //修改时间
            sadgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.sadgformatService.insert(sadgformatPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "修改列表格式", notes = "修改列表格式", produces = "application/json")
//    @RequestMapping(value = "/update", method = RequestMethod.POST)
//     //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.Edit")
//    public R<SadgformatPojo> update(@RequestBody String json) {
//        try {
//            SadgformatPojo sadgformatPojo = JSONArray.parseObject(json, SadgformatPojo.class);
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser();
//            sadgformatPojo.setLister(loginUser.getRealname());   // 制表
//            sadgformatPojo.setListerid(loginUser.getUserid());    // 制表id
//            sadgformatPojo.setModifydate(new Date());   //修改时间
//            sadgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
//            return R.ok(this.sadgformatService.update(sadgformatPojo));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


    @ApiOperation(value = "删除列表格式", notes = "删除列表格式", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增列表格式Item", notes = "新增列表格式Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.Add")
    public R<SadgformatitemPojo> createItem(@RequestBody String json) {
        try {
            SadgformatitemPojo sadgformatitemPojo = JSONArray.parseObject(json, SadgformatitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            sadgformatitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.sadgformatitemService.insert(sadgformatitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除列表格式Item", notes = "删除列表格式Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "SaDgFormat.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.sadgformatitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

