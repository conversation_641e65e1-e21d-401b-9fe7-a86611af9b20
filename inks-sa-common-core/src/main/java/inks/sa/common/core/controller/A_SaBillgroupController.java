package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaBillgroupPojo;
import inks.sa.common.core.service.SaBillgroupService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通用分组(Sa_BillGroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-02 14:47:57
 */
@RestController
@RequestMapping("/SaBillGroup")
@Api(tags = "通用:分组")
public class A_SaBillgroupController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaBillgroupController.class);


    @Resource
    private SaBillgroupService saBillgroupService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "按模块编码查询分组", notes = "按模块编码查询分组", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<SaBillgroupPojo>> getListByModuleCode(String Code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            List<SaBillgroupPojo> list = this.saBillgroupService.getListByModuleCode(Code);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param Code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询Def分组", notes = "按模块编码查询Def分组", produces = "application/json")
    @RequestMapping(value = "/getDefListByModuleCode", method = RequestMethod.GET)
    public R<List<SaBillgroupPojo>> getDefListByModuleCode(String Code) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            List<SaBillgroupPojo> list = this.saBillgroupService.getListByModuleCode(Code);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取通用分组详细信息", notes = "获取通用分组详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillGroup.List")
    public R<SaBillgroupPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saBillgroupService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaBillgroupPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_BillGroup.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saBillgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增通用分组", notes = "新增通用分组", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillGroup.Add")
    public R<SaBillgroupPojo> create(@RequestBody String json) {
        try {
            SaBillgroupPojo saBillgroupPojo = JSONArray.parseObject(json, SaBillgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saBillgroupPojo.setCreatedate(new Date());   // 创建时间
            saBillgroupPojo.setLister(loginUser.getRealname());   // 制表
            saBillgroupPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saBillgroupService.insert(saBillgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改通用分组", notes = "修改通用分组", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillGroup.Edit")
    public R<SaBillgroupPojo> update(@RequestBody String json) {
        try {
            SaBillgroupPojo saBillgroupPojo = JSONArray.parseObject(json, SaBillgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saBillgroupPojo.setLister(loginUser.getRealname());   // 制表
            saBillgroupPojo.setModifydate(new Date());   //修改时间
//            saBillgroupPojo.setAssessor(""); // 审核员
//            saBillgroupPojo.setAssessorid(""); // 审核员id
//            saBillgroupPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saBillgroupService.update(saBillgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除通用分组", notes = "删除通用分组", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillGroup.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saBillgroupService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_BillGroup.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaBillgroupPojo saBillgroupPojo = this.saBillgroupService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saBillgroupPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

