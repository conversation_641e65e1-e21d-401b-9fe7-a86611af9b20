package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaMenuwebPojo;
import inks.sa.common.core.mapper.SaRolemenuwebMapper;
import inks.sa.common.core.service.SaMenuwebService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Web菜单(Sa_MenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@RestController
@RequestMapping("SaMenuWeb")
@Api(tags = "通用:Web菜单")
public class A_SaMenuwebController {
    @Resource
    private SaMenuwebService saMenuwebService;
    @Resource
    private SaRolemenuwebMapper saRolemenuwebMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * @Description 将Web菜单列表List<SaMenuwebPojo>转为树形结构List<WebMenuPojo>
     */
    public static List<WebMenuPojo> buildWebMenuToChildren(List<SaMenuwebPojo> lst) {
        List<WebMenuPojo> list;
        list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1")) {
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(lst.get(i).getNavname());
                webMenuPojo.setPath(lst.get(i).getMvcurl());
                webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                        WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                        webMenuPojo2.setName(lst.get(j).getNavname());
                        webMenuPojo2.setPath(lst.get(j).getMvcurl());
                        webMenuPojo2.setMeta(new WebMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss()));
                        List<WebMenuPojo> lstChil2 = new ArrayList<>();
                        for (SaMenuwebPojo pimenuwebPojo : lst) {
                            if (pimenuwebPojo.getNavpid().equals(lst.get(j).getNavid())) {
                                WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                                webMenuPojo3.setName(pimenuwebPojo.getNavname());
                                webMenuPojo3.setPath(pimenuwebPojo.getMvcurl());
                                webMenuPojo3.setMeta(new WebMetaPojo(pimenuwebPojo.getNavname(), pimenuwebPojo.getImagecss()));
                                lstChil2.add(webMenuPojo3);
                            }
                        }
                        webMenuPojo2.setChildren(lstChil2);
                        lstChil.add(webMenuPojo2);
                    }
                }
                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
        }
        return list;
    }

    @ApiOperation(value = "项目全菜单 传root时为全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getAllListByPid", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<List<SaMenuwebPojo>> getAllListByPid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuwebPojo> lst = this.saMenuwebService.getListByPid(key);
            return R.ok(flattenMenuList(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 抄接口/getMenuWebListBySelf,只不过把返回改为平行的List<SaMenuwebPojo>了
    @ApiOperation(value = "项目全菜单 当前租户的", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByTen", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<List<SaMenuwebPojo>> getMenuWebListByTen() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuwebPojo> lst = saMenuwebService.getListAll();
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目菜单(Children格式) 登录用户userid->roleids->navids->menus  admin拿全部菜单", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByLoginUser", method = RequestMethod.GET)
//-     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<List<WebMenuPojo>> getMenuWebListByLoginUser() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMenuwebPojo> lst;
            if (Objects.equals(loginUser.getIsadmin(), 1) || Objects.equals(loginUser.getIsadmin(), 2)) {//admin拿全部菜单
                lst = saMenuwebService.getListAll();
            } else {
                List<String> navids = saRolemenuwebMapper.getNavidsByUserid(loginUser.getUserid(), loginUser.getTenantid());
                if (CollectionUtils.isEmpty(navids)) {
//                    throw new Exception("用户未关联菜单权限Navid");
                    return R.ok(new ArrayList<>());
                }
                lst = saMenuwebService.getListByNavids(navids);
            }
            // 将Web菜单列表List 转为树形结构List
            return R.ok(buildWebMenuToChildren(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目菜单 roleid->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByRole", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<List<SaMenuwebPojo>> getMenuAppListByRole(String roleid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> navids = saRolemenuwebMapper.getNavidsByRoleid(roleid, loginUser.getTenantid());
            if (CollectionUtils.isEmpty(navids)) {
//                throw new Exception("角色未关联菜单权限");
                return R.ok(new ArrayList<>());
            }
            List<SaMenuwebPojo> lst = saMenuwebService.getListByNavids(navids);
            // 不用向下递归获取子菜单
//            return R.ok(flattenMenuList(lst));
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private List<SaMenuwebPojo> flattenMenuList(List<SaMenuwebPojo> lst) {
        List<SaMenuwebPojo> list = new ArrayList<>();
        for (SaMenuwebPojo pimenuwebPojo : lst) {
            SaMenuwebPojo webMenuPojo = new SaMenuwebPojo();
            BeanUtils.copyProperties(pimenuwebPojo, webMenuPojo);
            list.add(webMenuPojo);
            List<SaMenuwebPojo> lst2 = saMenuwebService.getListByPid(pimenuwebPojo.getNavid());
            for (SaMenuwebPojo pojo : lst2) {
                SaMenuwebPojo webMenuPojo2 = new SaMenuwebPojo();
                BeanUtils.copyProperties(pojo, webMenuPojo2);
                list.add(webMenuPojo2);
                List<SaMenuwebPojo> lst3 = saMenuwebService.getListByPid(pojo.getNavid());
                for (SaMenuwebPojo value : lst3) {
                    SaMenuwebPojo webMenuPojo3 = new SaMenuwebPojo();
                    BeanUtils.copyProperties(value, webMenuPojo3);
                    list.add(webMenuPojo3);
                }
            }
        }
        // 使用Stream API根据Navid去重 这种方法利用了Map键的唯一性特性来实现去重。
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        SaMenuwebPojo::getNavid, // 使用Navid作为键
                        pojo -> pojo,            // 保留Pojo对象
                        (existing, replacement) -> existing // 如果有重复，保留现有的
                ))
                .values());
    }

//    ====================================代码生成=================================

    @ApiOperation(value = " 获取后台导航详细信息", notes = "获取后台导航详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<SaMenuwebPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMenuwebService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.List")
    public R<PageInfo<SaMenuwebPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MenuWeb.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMenuwebService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增后台导航", notes = "新增后台导航", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.Add")
    public R<SaMenuwebPojo> create(@RequestBody String json) {
        try {
            SaMenuwebPojo saMenuwebPojo = JSONArray.parseObject(json, SaMenuwebPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMenuwebPojo.setCreatedate(new Date());   // 创建时间
            saMenuwebPojo.setLister(loginUser.getRealname());   // 制表
            saMenuwebPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMenuwebService.insert(saMenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改后台导航", notes = "修改后台导航", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.Edit")
    public R<SaMenuwebPojo> update(@RequestBody String json) {
        try {
            SaMenuwebPojo saMenuwebPojo = JSONArray.parseObject(json, SaMenuwebPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMenuwebPojo.setLister(loginUser.getRealname());   // 制表
            saMenuwebPojo.setModifydate(new Date());   //修改时间
            //            saMenuwebPojo.setAssessor(""); // 审核员
            //            saMenuwebPojo.setAssessorid(""); // 审核员id
            //            saMenuwebPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMenuwebService.update(saMenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除后台导航", notes = "删除后台导航", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMenuwebService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_MenuWeb.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMenuwebPojo saMenuwebPojo = this.saMenuwebService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMenuwebPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

