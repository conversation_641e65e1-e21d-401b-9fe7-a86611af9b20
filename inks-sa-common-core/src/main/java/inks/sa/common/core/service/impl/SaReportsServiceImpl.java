package inks.sa.common.core.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaReportsEntity;
import inks.sa.common.core.domain.pojo.SaReportsPojo;
import inks.sa.common.core.mapper.SaReportsMapper;
import inks.sa.common.core.service.SaReportsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报表中心(SaReports)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
@Service("saReportsService")
public class SaReportsServiceImpl implements SaReportsService {
    @Resource
    private SaReportsMapper saReportsMapper;


    @Override
    public SaReportsPojo getEntity(String key) {
        return this.saReportsMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaReportsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportsPojo> lst = saReportsMapper.getPageList(queryParam);
            PageInfo<SaReportsPojo> pageInfo = new PageInfo<SaReportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportsPojo insert(SaReportsPojo saReportsPojo) {
        //初始化NULL字段
        if (saReportsPojo.getGengroupid() == null) saReportsPojo.setGengroupid("");
        if(saReportsPojo.getModulecode()==null) saReportsPojo.setModulecode("");
        if (saReportsPojo.getRpttype() == null) saReportsPojo.setRpttype("");
        if (saReportsPojo.getRptname() == null) saReportsPojo.setRptname("");
        if (saReportsPojo.getRptdata() == null) saReportsPojo.setRptdata("");
        if (saReportsPojo.getPagerow() == null) saReportsPojo.setPagerow(0);
        if (saReportsPojo.getTempurl() == null) saReportsPojo.setTempurl("");
        if (saReportsPojo.getFilename() == null) saReportsPojo.setFilename("");
        if (saReportsPojo.getPrintersn() == null) saReportsPojo.setPrintersn("");
        if (saReportsPojo.getRownum() == null) saReportsPojo.setRownum(0);
        if (saReportsPojo.getEnabledmark() == null) saReportsPojo.setEnabledmark(0);
        if (saReportsPojo.getGrfdata() == null) saReportsPojo.setGrfdata("");
        if (saReportsPojo.getPaperlength() == null) saReportsPojo.setPaperlength(0D);
        if (saReportsPojo.getPaperwidth() == null) saReportsPojo.setPaperwidth(0D);
        if (saReportsPojo.getRemark() == null) saReportsPojo.setRemark("");
        if (saReportsPojo.getCreateby() == null) saReportsPojo.setCreateby("");
        if (saReportsPojo.getCreatebyid() == null) saReportsPojo.setCreatebyid("");
        if (saReportsPojo.getCreatedate() == null) saReportsPojo.setCreatedate(new Date());
        if (saReportsPojo.getLister() == null) saReportsPojo.setLister("");
        if (saReportsPojo.getListerid() == null) saReportsPojo.setListerid("");
        if (saReportsPojo.getModifydate() == null) saReportsPojo.setModifydate(new Date());
        if (saReportsPojo.getCustom1() == null) saReportsPojo.setCustom1("");
        if (saReportsPojo.getCustom2() == null) saReportsPojo.setCustom2("");
        if (saReportsPojo.getCustom3() == null) saReportsPojo.setCustom3("");
        if (saReportsPojo.getCustom4() == null) saReportsPojo.setCustom4("");
        if (saReportsPojo.getCustom5() == null) saReportsPojo.setCustom5("");
        if (saReportsPojo.getTenantid() == null) saReportsPojo.setTenantid("");
        if (saReportsPojo.getTenantname() == null) saReportsPojo.setTenantname("");
        if (saReportsPojo.getRevision() == null) saReportsPojo.setRevision(0);
        SaReportsEntity saReportsEntity = new SaReportsEntity();
        BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
        if (saReportsEntity.getRptdata() != null) {
            //将前端传来的BASE64转换成XML
            saReportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getRptdata()), StandardCharsets.UTF_8));
        }
        if (saReportsEntity.getGrfdata() != null) {
            //将前端传来的BASE64转换成XML
            saReportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getGrfdata()), StandardCharsets.UTF_8));
        }
        saReportsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReportsEntity.setRevision(1);  //乐观锁
        this.saReportsMapper.insert(saReportsEntity);
        return this.getEntity(saReportsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportsPojo update(SaReportsPojo saReportsPojo) {
        SaReportsEntity saReportsEntity = new SaReportsEntity();
        BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
        if (saReportsEntity.getRptdata() != null) {
            //将前端传来的BASE64转换成XML
            saReportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getRptdata()), StandardCharsets.UTF_8));
        }
        if (saReportsEntity.getGrfdata() != null) {
            //将前端传来的BASE64转换成XML
            saReportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getGrfdata()), StandardCharsets.UTF_8));
        }
        this.saReportsMapper.update(saReportsEntity);
        return this.getEntity(saReportsEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saReportsMapper.delete(key);
    }


    @Override
    public List<SaReportsPojo> pullDefault(String moduleCode, LoginUser loginUser) {
        try {
            //搜索默认科目模板
            List<SaReportsPojo> lstnew = new ArrayList<>();
            List<SaReportsPojo> lstdef = this.saReportsMapper.getListByDef(moduleCode);
            for (SaReportsPojo item : lstdef) {
                SaReportsPojo dbpojo = saReportsMapper.getEntityByNameCode(item.getRptname(), item.getModulecode());
                if (dbpojo == null) {
                    SaReportsPojo saReportsPojo = new SaReportsPojo();
                    BeanUtils.copyProperties(item, saReportsPojo);
                    saReportsPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//                    saReportsPojo.setTenantid(loginUser.getTenantid());
//                    saReportsPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                    saReportsPojo.setCreatebyid(loginUser.getUserid());
                    saReportsPojo.setCreateby(loginUser.getUsername());
                    saReportsPojo.setCreatedate(new Date());
                    saReportsPojo.setListerid(loginUser.getUserid());
                    saReportsPojo.setLister(loginUser.getUsername());
                    saReportsPojo.setModifydate(new Date());
                    SaReportsEntity saReportsEntity = new SaReportsEntity();
                    BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
                    this.saReportsMapper.insert(saReportsEntity);
                    lstnew.add(saReportsPojo);
                }
            }
            return lstnew;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaReportsPojo> getPageListAll(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportsPojo> lst = saReportsMapper.getPageListAll(queryParam);
            PageInfo<SaReportsPojo> pageInfo = new PageInfo<SaReportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<SaReportsPojo> getListByModuleCode(String moduleCode) {
        try {
            //自定义报表
            List<SaReportsPojo> lst = saReportsMapper.getListByModuleCode(moduleCode);
            //默认格式
            // List<CireportsPojo> lstdef = cireportsMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}
