package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import inks.sa.common.core.domain.SaConfigEntity;
import inks.sa.common.core.domain.pojo.SaConfigPojo;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static inks.sa.common.core.constant.MyConstant.CONFIG_REDISKEY;

/**
 * (SaConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-30 16:36:58
 */
@Service("saConfigService")
public class SaConfigServiceImpl implements SaConfigService {
    private static final Logger logger = LoggerFactory.getLogger(SaConfigServiceImpl.class);
    @Resource
    private SaConfigMapper saConfigMapper;
    @Resource
    private SaRedisService saRedisService;


    @Override
    public SaConfigPojo getEntity(String key) {
        return this.saConfigMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaConfigPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaConfigPojo> lst = saConfigMapper.getPageList(queryParam);
            PageInfo<SaConfigPojo> pageInfo = new PageInfo<SaConfigPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saConfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaConfigPojo insert(SaConfigPojo saConfigPojo) {
        //初始化NULL字段
        cleanNull(saConfigPojo);
        SaConfigEntity saConfigEntity = new SaConfigEntity();
        BeanUtils.copyProperties(saConfigPojo, saConfigEntity);

        saConfigEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saConfigEntity.setRevision(1);  //乐观锁
        this.saConfigMapper.insert(saConfigEntity);
        clearConfigCache(); // 清理缓存
        return this.getEntity(saConfigEntity.getId());

    }

    public static void cleanNull(SaConfigPojo saConfigPojo) {
        if (saConfigPojo.getParentid() == null) saConfigPojo.setParentid("");
        if (saConfigPojo.getCfgname() == null) saConfigPojo.setCfgname("");
        if (saConfigPojo.getCfgkey() == null) saConfigPojo.setCfgkey("");
        if (saConfigPojo.getCfgvalue() == null) saConfigPojo.setCfgvalue("");
        if (saConfigPojo.getCfgtype() == null) saConfigPojo.setCfgtype(0);
        if (saConfigPojo.getCfglevel() == null) saConfigPojo.setCfglevel(0);
        if (saConfigPojo.getCtrltype() == null) saConfigPojo.setCtrltype(0);
        if (saConfigPojo.getCfgoption() == null) saConfigPojo.setCfgoption("");
        if (saConfigPojo.getCfgicon() == null) saConfigPojo.setCfgicon("");
        if (saConfigPojo.getAllowui() == null) saConfigPojo.setAllowui(0);
        if (saConfigPojo.getRownum() == null) saConfigPojo.setRownum(0);
        if (saConfigPojo.getEnabledmark() == null) saConfigPojo.setEnabledmark(0);
        if (saConfigPojo.getAllowdelete() == null) saConfigPojo.setAllowdelete(0);
        if (saConfigPojo.getRemark() == null) saConfigPojo.setRemark("");
        if (saConfigPojo.getCreateby() == null) saConfigPojo.setCreateby("");
        if (saConfigPojo.getCreatebyid() == null) saConfigPojo.setCreatebyid("");
        if (saConfigPojo.getCreatedate() == null) saConfigPojo.setCreatedate(new Date());
        if (saConfigPojo.getLister() == null) saConfigPojo.setLister("");
        if (saConfigPojo.getListerid() == null) saConfigPojo.setListerid("");
        if (saConfigPojo.getModifydate() == null) saConfigPojo.setModifydate(new Date());
        if (saConfigPojo.getCustom1() == null) saConfigPojo.setCustom1("");
        if (saConfigPojo.getCustom2() == null) saConfigPojo.setCustom2("");
        if (saConfigPojo.getCustom3() == null) saConfigPojo.setCustom3("");
        if (saConfigPojo.getCustom4() == null) saConfigPojo.setCustom4("");
        if (saConfigPojo.getCustom5() == null) saConfigPojo.setCustom5("");
        if (saConfigPojo.getTenantid() == null) saConfigPojo.setTenantid("");
        if (saConfigPojo.getUserid() == null) saConfigPojo.setUserid("");
        if (saConfigPojo.getRevision() == null) saConfigPojo.setRevision(0);
        if (saConfigPojo.getCustom6() == null) saConfigPojo.setCustom6("");
        if (saConfigPojo.getCustom7() == null) saConfigPojo.setCustom7("");
        if (saConfigPojo.getCustom8() == null) saConfigPojo.setCustom8("");
        if (saConfigPojo.getCustom9() == null) saConfigPojo.setCustom9("");
        if (saConfigPojo.getCustom10() == null) saConfigPojo.setCustom10("");
    }

    /**
     * 修改数据
     *
     * @param saConfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaConfigPojo update(SaConfigPojo saConfigPojo) {
        SaConfigEntity saConfigEntity = new SaConfigEntity();
        BeanUtils.copyProperties(saConfigPojo, saConfigEntity);
        this.saConfigMapper.update(saConfigEntity);
        clearConfigCache(); // 清理系统参数缓存
        return this.getEntity(saConfigEntity.getId());
    }


    @Override
    public SaConfigPojo getEntityByCfgKey(String cfgkey) {
        return this.saConfigMapper.getEntityByCfgKey(cfgkey);
    }

    @Override
    public int delete(String key) {
        int result = this.saConfigMapper.delete(key);
        clearConfigCache(); // 清理系统参数缓存
        return result;
    }


    @Override
    public String getConfigValue(String key, String userid) {
        //用户级参数
        SaConfigPojo saConfigPojo = null;
        if (StringUtils.isNotBlank(userid)) {
            saConfigPojo = this.saConfigMapper.getEntityByKeyUser(key, userid);
        }
        if (saConfigPojo == null) {
            //租户级参数
            saConfigPojo = this.saConfigMapper.getEntityByKey(key);
        }
        if (saConfigPojo == null) {
            //默认级参数
//            saConfigPojo = this.saConfigMapper.getEntityByKey(key, InksConstants.DEFAULT_TENANT);
            saConfigPojo = this.saConfigMapper.getEntityByKey(key);
        }
        if (saConfigPojo == null) {
            return null;
        } else {
            return saConfigPojo.getCfgvalue();
        }
    }

    @Override
    public SaConfigPojo getEntityByKeyUser(String key, String userid) {
        return this.saConfigMapper.getEntityByKeyUser(key, userid);
    }

    @Override
    public Map<String, String> getConfigAll() {
        // 只要参数的键值对CfgKey-CfgValue
        return this.saConfigMapper.getAllList().stream()
                .collect(Collectors.toMap(SaConfigPojo::getCfgkey, SaConfigPojo::getCfgvalue));
    }

    /**
     * 清理系统参数相关缓存
     */
    private void clearConfigCache() {
        try {
            // 1. 清理Redis缓存中系统参数配置
            // 注意：这里简单处理，直接删除所有可能的key。更优化的方式是只删除当前租户的key。
            // 但由于修改配置的操作不频繁，且可能影响所有租户，暂时采用全量删除策略。
            saRedisService.deleteObject(CONFIG_REDISKEY);
            logger.info("========= [Cache Evict] 清理Redis缓存中系统参数配置 'tenant_config:' =========");

            // 2. 清理ThreadLocal系统参数线程缓存
            InksConfigThreadLocal_Sa.clear();
            logger.info("========= [Cache Evict] 清理ThreadLocal系统参数线程缓存. =========");

        } catch (Exception e) {
            logger.error("Error during cache eviction after config change", e);
        }
    }

}
