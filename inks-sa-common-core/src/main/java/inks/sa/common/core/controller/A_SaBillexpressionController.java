package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.domain.pojo.SaBillexpressionPojo;
import inks.sa.common.core.service.SaBillexpressionService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 单据公式(Sa_BillExpression)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:40:21
 */
@RestController
@RequestMapping("SaBillExpression")
@Api(tags = "通用:单据公式")
public class A_SaBillexpressionController {
    @Resource
    private SaBillexpressionService saBillexpressionService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "通过ModuleCode获取单据公式详细信息", notes = "获取单据公式详细信息", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "CiBillExpression.List")
    public R<List<SaBillexpressionPojo>> getListByCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saBillexpressionService.getListByCode(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取单据公式详细信息", notes = "获取单据公式详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.List")
    public R<SaBillexpressionPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saBillexpressionService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.List")
    public R<PageInfo<SaBillexpressionPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_BillExpression.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saBillexpressionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增单据公式", notes = "新增单据公式", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.Add")
    public R<SaBillexpressionPojo> create(@RequestBody String json) {
        try {
            SaBillexpressionPojo saBillexpressionPojo = JSONArray.parseObject(json, SaBillexpressionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saBillexpressionPojo.setCreateby(loginUser.getRealName());   // 创建者
            saBillexpressionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saBillexpressionPojo.setCreatedate(new Date());   // 创建时间
            saBillexpressionPojo.setLister(loginUser.getRealname());   // 制表
            saBillexpressionPojo.setListerid(loginUser.getUserid());    // 制表id
            saBillexpressionPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saBillexpressionService.insert(saBillexpressionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改单据公式", notes = "修改单据公式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.Edit")
    public R<SaBillexpressionPojo> update(@RequestBody String json) {
        try {
            SaBillexpressionPojo saBillexpressionPojo = JSONArray.parseObject(json, SaBillexpressionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saBillexpressionPojo.setLister(loginUser.getRealname());   // 制表
            saBillexpressionPojo.setListerid(loginUser.getUserid());    // 制表id
            saBillexpressionPojo.setModifydate(new Date());   //修改时间
            //            saBillexpressionPojo.setAssessor(""); // 审核员
            //            saBillexpressionPojo.setAssessorid(""); // 审核员id
            //            saBillexpressionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saBillexpressionService.update(saBillexpressionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除单据公式", notes = "删除单据公式", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saBillexpressionService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
     //@PreAuthorize(hasPermi = "Sa_BillExpression.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaBillexpressionPojo saBillexpressionPojo = this.saBillexpressionService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saBillexpressionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

