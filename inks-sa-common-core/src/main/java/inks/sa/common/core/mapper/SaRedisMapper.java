package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaRedisEntity;
import inks.sa.common.core.domain.pojo.SaRedisPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MySQL暂替Redis(SaRedis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-16 15:13:02
 */
@Mapper
public interface SaRedisMapper {


    SaRedisPojo getEntity(@Param("key") String key);


    List<SaRedisPojo> getPageList(QueryParam queryParam);


    int insert(SaRedisEntity saRedisEntity);

    int update(SaRedisEntity saRedisEntity);


    int delete(@Param("key") String key);

    String getValue(String redisKey);

    int cleanSa_Redis();

    SaRedisPojo getEntityByRedisKeyAndHKey(@Param("redisKey") String redisKey, @Param("hKey") String hKey);
}

