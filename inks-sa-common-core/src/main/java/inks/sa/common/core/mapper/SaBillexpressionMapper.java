package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaBillexpressionPojo;
import inks.sa.common.core.domain.SaBillexpressionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 单据公式(SaBillexpression)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 14:40:22
 */
@Mapper
public interface SaBillexpressionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaBillexpressionPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaBillexpressionPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saBillexpressionEntity 实例对象
     * @return 影响行数
     */
    int insert(SaBillexpressionEntity saBillexpressionEntity);

    
    /**
     * 修改数据
     *
     * @param saBillexpressionEntity 实例对象
     * @return 影响行数
     */
    int update(SaBillexpressionEntity saBillexpressionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaBillexpressionPojo> getListByCode(String key);
}

