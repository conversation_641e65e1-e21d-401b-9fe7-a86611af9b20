package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaBillcodePojo;

/**
 * 单据编码(SaBillcode)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:45
 */
public interface SaBillcodeService {


    SaBillcodePojo getEntity(String key, String tid);

    PageInfo<SaBillcodePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param sabillcodePojo 实例对象
     * @return 实例对象
     */
    SaBillcodePojo insert(SaBillcodePojo sabillcodePojo);

    /**
     * 修改数据
     *
     * @param sabillcodepojo 实例对象
     * @return 实例对象
     */
    SaBillcodePojo update(SaBillcodePojo sabillcodepojo);

    int delete(String key, String tid);

    /**
     * 通过功能code生成序号
     *
     * @param ModuleCode 主键
     * @return 是否成功
     */

    String getSerialNo(String ModuleCode, String tid);
    String getSerialNo(String ModuleCode);
    String getSerialNo(String ModuleCode,String tid, String tablename, String prefix);
    String getSerialNo(String ModuleCode,String tid, String tablename);

    String getNextCode(String tablename, String column);
}
