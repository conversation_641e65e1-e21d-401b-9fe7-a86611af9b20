package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaIndeximgPojo;

/**
 * (SaIndeximg)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
public interface SaIndeximgService {


    SaIndeximgPojo getEntity(String key);

    PageInfo<SaIndeximgPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saIndeximgPojo 实例对象
     * @return 实例对象
     */
    SaIndeximgPojo insert(SaIndeximgPojo saIndeximgPojo);

    /**
     * 修改数据
     *
     * @param saIndeximgpojo 实例对象
     * @return 实例对象
     */
    SaIndeximgPojo update(SaIndeximgPojo saIndeximgpojo);

    int delete(String key);
}
