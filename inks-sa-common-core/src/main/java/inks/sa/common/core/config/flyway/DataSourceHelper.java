package inks.sa.common.core.config.flyway;

import inks.sa.common.core.utils.PrintColor;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * springboot结合flyway自动创建数据库及数据表
 */
@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE) // 确保这个配置类优先执行
public class DataSourceHelper {

    // 静态变量记录数据库初始化状态
    private static volatile String initializationStatus = "未知";
    private static volatile String databaseName = "";
    private static volatile long initializationTime = 0;
    private static volatile int createdTables = 0;

    @Value("${spring.datasource.url:}")
    private String dbUrl;

    @Value("${spring.datasource.username:}")
    private String dbUsername;

    @Value("${spring.datasource.password:}")
    private String dbPassword;

    @Value("${spring.datasource.driver-class-name:}")
    private String dbDriverClassName;

    // Flyway配置直接在代码中定义，不再依赖yml配置
    private final boolean autoCreateDbEnabled = true;

    @Value("${spring.flyway.initsql:}")
    private String remoteSqlUrl;

    private ProgressPageManager progressManager;

    @PostConstruct
    public void init() {
        // Only proceed if auto create database is enabled
        if (!autoCreateDbEnabled) {
            return;
        }

        try {
            Class.forName(dbDriverClassName);
            URI uri = new URI(dbUrl.replace("jdbc:", ""));
            String host = uri.getHost();
            int port = uri.getPort();
            String path = uri.getPath();
            String query = uri.getQuery();
            String databaseName = path.replace("/", "");

            // 记录数据库名称
            DataSourceHelper.databaseName = databaseName;

            // 检查数据库是否存在
            boolean databaseExists = checkDatabaseExists(host, port, query, databaseName);

            if (!databaseExists) {
                long startTime = System.currentTimeMillis();
                PrintColor.red("数据库 [" + databaseName + "] 不存在，开始创建数据库并执行迁移...");

                // 记录首次建库状态
                DataSourceHelper.initializationStatus = "首次建库";
                DataSourceHelper.initializationTime = startTime;

                // 创建实时进度页面（包含数据库连接信息）
                progressManager = new ProgressPageManager(databaseName, dbUrl, dbUsername, dbPassword);
                progressManager.createAndOpenProgressPage();
                progressManager.updateProgress("开始数据库初始化...", 0);

                // 创建数据库
                progressManager.updateProgress("正在创建数据库...", 1);
                if ("com.mysql.cj.jdbc.Driver".equals(dbDriverClassName)) {
                    Connection connection = DriverManager.getConnection("jdbc:mysql://" + host + ":" + port + "?" + query, dbUsername, dbPassword);
                    Statement statement = connection.createStatement();
                    statement.executeUpdate("CREATE DATABASE IF NOT EXISTS `" + databaseName + "` DEFAULT CHARACTER SET = "
                            + "`utf8mb4` COLLATE `utf8mb4_bin`;");
                    statement.close();
                    connection.close();
                    progressManager.addLog("数据库创建完成");
                }

                // 创建数据库后立即执行Flyway迁移
                progressManager.updateProgress("开始执行数据库迁移...", 2);
                executeFlyway();

                long endTime = System.currentTimeMillis();
                long elapsedTime = endTime - startTime;
                String completeMessage = "数据库 [" + databaseName + "] 创建完成，远程SQL文件执行完毕";

                // 更新完成状态
                DataSourceHelper.initializationTime = elapsedTime;

                // 标记完成
                progressManager.markComplete(completeMessage, elapsedTime);

                // 立即停止HTTP服务器，释放8080端口给Spring Boot
                progressManager.stopHttpServer();
            } else {
                String skipMessage = "数据库 [" + databaseName + "] 已存在，跳过初始化数据表步骤";
                PrintColor.red(skipMessage);

                // 记录已有数据库状态
                DataSourceHelper.initializationStatus = "已有数据库";
                DataSourceHelper.initializationTime = 0;

                // 数据库已存在，不需要启动HTTP服务器，直接让Spring Boot使用8080端口
                PrintColor.red("数据库已存在，跳过Flyway进度服务器启动，8080端口直接供Spring Boot使用");
            }

        } catch (URISyntaxException | ClassNotFoundException | SQLException e) {
            if (progressManager != null) {
                progressManager.markError("数据库初始化失败: " + e.getMessage());
            }
            e.printStackTrace();
            System.exit(0);
        }
    }


    /**
     * 检查数据库是否存在
     */
    private boolean checkDatabaseExists(String host, int port, String query, String databaseName) {
        try {
            // 连接到MySQL服务器（不指定数据库）
            Connection connection = DriverManager.getConnection("jdbc:mysql://" + host + ":" + port + "?" + query, dbUsername, dbPassword);
            Statement statement = connection.createStatement();

            // 查询数据库是否存在
            String sql = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" + databaseName + "'";
            java.sql.ResultSet resultSet = statement.executeQuery(sql);
            boolean exists = resultSet.next();

            resultSet.close();
            statement.close();
            connection.close();

            return exists;
        } catch (SQLException e) {
            System.err.println("检查数据库存在性时发生错误: " + e.getMessage());
            return false; // 如果检查失败，假设数据库不存在，继续创建流程
        }
    }

    /**
     * 执行Flyway迁移
     * 禁用占位符替换以忽略SQL中的${...}格式
     * 从远程URL下载SQL文件并执行
     */
    private void executeFlyway() {
        try {
            // 检查是否配置了远程SQL URL
            if (remoteSqlUrl == null || remoteSqlUrl.trim().isEmpty()) {
                PrintColor.red("未配置远程SQL URL，跳过数据库初始化");
                return;
            }

            if (progressManager != null) {
                progressManager.updateProgress("正在下载远程SQL文件:" + remoteSqlUrl, 3);
            }

            // 创建临时目录和符合Flyway命名规范的SQL文件
            File tempDir = new File(System.getProperty("java.io.tmpdir"), "flyway_remote_" + System.currentTimeMillis());
            tempDir.mkdirs();

            // Flyway要求文件名格式为 V{version}__{description}.sql
            File tempSqlFile = new File(tempDir, "V1.0__remote_init.sql");

            // 下载远程SQL文件
            downloadFile(remoteSqlUrl, tempSqlFile.getAbsolutePath());

            // 打印下载的SQL文件大小
            printFileSize(tempSqlFile);

            // 分析SQL文件，获取表信息
            SqlFileAnalyzer.SqlAnalysisResult analysisResult = SqlFileAnalyzer.analyzeSqlFile(tempSqlFile);
            if (progressManager != null) {
                progressManager.setTotalTables(analysisResult.getEstimatedTables());
                progressManager.updateProgress("SQL文件下载完成，开始执行迁移...", 5);
            }

            // 创建数据源
            DataSource dataSource = DataSourceBuilder.create()
                    .url(dbUrl)
                    .username(dbUsername)
                    .password(dbPassword)
                    .build();

            // 创建日志拦截器（主要方案）
            FlywayLogInterceptor logInterceptor = new FlywayLogInterceptor(progressManager);

            // 尝试创建 Flyway 进度回调（备用方案）
            Flyway flyway;
            try {
                FlywayProgressCallback progressCallback = new FlywayProgressCallback(progressManager);
                flyway = Flyway.configure()
                        .dataSource(dataSource)
                        .locations("filesystem:" + tempDir.getAbsolutePath())
                        .baselineOnMigrate(true)
                        .baselineVersion("0")
                        .outOfOrder(true)
                        .validateOnMigrate(true)
                        .placeholderReplacement(false)
                        .callbacks(progressCallback) // 尝试添加回调
                        .load();
                progressManager.addLog("Flyway 回调机制已启用");
            } catch (Exception e) {
                // 如果回调失败，使用基础配置
                flyway = Flyway.configure()
                        .dataSource(dataSource)
                        .locations("filesystem:" + tempDir.getAbsolutePath())
                        .baselineOnMigrate(true)
                        .baselineVersion("0")
                        .outOfOrder(true)
                        .validateOnMigrate(true)
                        .placeholderReplacement(false)
                        .load();
                progressManager.addLog("使用基础 Flyway 配置（回调不可用）");
            }

            // 执行迁移
            if (progressManager != null) {
                progressManager.updateProgress("正在执行数据库迁移，请稍候...", 10);
            }

            // 获取待执行的迁移信息（如果可能）
            try {
                org.flywaydb.core.api.MigrationInfo[] pendingMigrations = flyway.info().pending();
                progressManager.addLog("发现 " + pendingMigrations.length + " 个迁移脚本需要执行");
            } catch (Exception e) {
                // 如果获取迁移信息失败，继续执行
                progressManager.addLog("无法获取迁移信息，继续执行...");
            }

            // 开始拦截日志以获取真实的执行进度
            logInterceptor.startIntercepting();

            try {
                // 执行 Flyway 迁移（日志拦截器会自动处理进度）
                flyway.migrate();
            } finally {
                // 停止日志拦截
                logInterceptor.stopIntercepting();
            }

            if (progressManager != null) {
                progressManager.updateProgress("数据库迁移执行完成", 90);
            }

            // 删除临时文件和目录
            if (progressManager != null) {
                progressManager.addLog("开始清理临时文件...");
            }
            if (tempSqlFile.exists()) {
                boolean fileDeleted = tempSqlFile.delete();
                String deleteMsg = "临时SQL文件删除" + (fileDeleted ? "成功" : "失败");
                if (progressManager != null) {
                    progressManager.addLog(deleteMsg);
                }
            }
            if (tempDir.exists()) {
                boolean dirDeleted = tempDir.delete();
                String deleteDirMsg = "临时目录删除" + (dirDeleted ? "成功" : "失败");
                if (progressManager != null) {
                    progressManager.addLog(deleteDirMsg);
                }
            }
            if (progressManager != null) {
                progressManager.addLog("临时文件清理完成");
            }

        } catch (Exception e) {
            String errorMsg = "执行Flyway迁移失败: " + e.getMessage();
            if (progressManager != null) {
                progressManager.markError(errorMsg);
            }
            e.printStackTrace();
        }
    }

    /**
     * 从远程URL下载文件到本地
     */
    private void downloadFile(String fileUrl, String destinationPath) throws Exception {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        // 设置超时
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(60000);

        int responseCode = connection.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            throw new Exception("下载SQL文件失败，HTTP错误码: " + responseCode);
        }

        // 读取远程文件并写入本地
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
             FileWriter writer = new FileWriter(destinationPath)) {

            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line + "\n");
            }
        }
    }

    /**
     * 打印文件大小信息
     */
    private void printFileSize(File file) {
        if (file.exists()) {
            long fileSizeBytes = file.length();
            String formattedSize = formatFileSize(fileSizeBytes);

            PrintColor.red("SQL文件下载完成:");
            PrintColor.red("  - 文件路径: " + file.getAbsolutePath());
            PrintColor.red("  - 文件大小: " + formattedSize + " (" + fileSizeBytes + " 字节)");

            // 同时添加到进度管理器的日志中
            if (progressManager != null) {
                progressManager.addLog("SQL文件下载完成，大小: " + formattedSize);
            }
        } else {
            PrintColor.red("警告: SQL文件不存在，无法获取文件大小");
        }
    }

    /**
     * 格式化文件大小显示
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 声明一个Bean来标记Flyway迁移已完成
     */
    @Bean
    public FlywayMigrationCompleted flywayMigrationCompleted() {
        return new FlywayMigrationCompleted();
    }

    /**
     * 用于标记Flyway迁移已完成的标记类
     */
    public static class FlywayMigrationCompleted {
        // 这是一个标记类，用于依赖注入时确认Flyway迁移已完成
    }

    /**
     * 获取数据库初始化状态
     */
    public static String getInitializationStatus() {
        return initializationStatus;
    }

    /**
     * 获取数据库名称
     */
    public static String getDatabaseName() {
        return databaseName;
    }

    /**
     * 获取初始化耗时（毫秒）
     */
    public static long getInitializationTime() {
        return initializationTime;
    }

    /**
     * 获取创建的表数量
     */
    public static int getCreatedTables() {
        return createdTables;
    }
}