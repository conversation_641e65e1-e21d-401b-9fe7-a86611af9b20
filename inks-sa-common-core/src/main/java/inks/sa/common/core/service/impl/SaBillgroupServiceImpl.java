package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaBillgroupEntity;
import inks.sa.common.core.domain.pojo.SaBillgroupPojo;
import inks.sa.common.core.mapper.SaBillgroupMapper;
import inks.sa.common.core.service.SaBillgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 通用分组(SaBillgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-02 14:47:57
 */
@Service("saBillgroupService")
public class SaBillgroupServiceImpl implements SaBillgroupService {
    @Resource
    private SaBillgroupMapper saBillgroupMapper;


    @Override
    public SaBillgroupPojo getEntity(String key) {
        return this.saBillgroupMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaBillgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBillgroupPojo> lst = saBillgroupMapper.getPageList(queryParam);
            PageInfo<SaBillgroupPojo> pageInfo = new PageInfo<SaBillgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saBillgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaBillgroupPojo insert(SaBillgroupPojo saBillgroupPojo) {
        //初始化NULL字段
        if (saBillgroupPojo.getParentid() == null) saBillgroupPojo.setParentid("");
        if (saBillgroupPojo.getModulecode() == null) saBillgroupPojo.setModulecode("");
        if (saBillgroupPojo.getGroupcode() == null) saBillgroupPojo.setGroupcode("");
        if (saBillgroupPojo.getGroupname() == null) saBillgroupPojo.setGroupname("");
        if (saBillgroupPojo.getEnabledmark() == null) saBillgroupPojo.setEnabledmark(0);
        if (saBillgroupPojo.getRownum() == null) saBillgroupPojo.setRownum(0);
        if (saBillgroupPojo.getRemark() == null) saBillgroupPojo.setRemark("");
        if (saBillgroupPojo.getLister() == null) saBillgroupPojo.setLister("");
        if (saBillgroupPojo.getCreatedate() == null) saBillgroupPojo.setCreatedate(new Date());
        if (saBillgroupPojo.getModifydate() == null) saBillgroupPojo.setModifydate(new Date());
        if (saBillgroupPojo.getTenantid() == null) saBillgroupPojo.setTenantid("");
        SaBillgroupEntity saBillgroupEntity = new SaBillgroupEntity();
        BeanUtils.copyProperties(saBillgroupPojo, saBillgroupEntity);

        saBillgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saBillgroupMapper.insert(saBillgroupEntity);
        return this.getEntity(saBillgroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saBillgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaBillgroupPojo update(SaBillgroupPojo saBillgroupPojo) {
        SaBillgroupEntity saBillgroupEntity = new SaBillgroupEntity();
        BeanUtils.copyProperties(saBillgroupPojo, saBillgroupEntity);
        this.saBillgroupMapper.update(saBillgroupEntity);
        return this.getEntity(saBillgroupEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saBillgroupMapper.delete(key);
    }


    /**
     * 通过功能号获得分组
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<SaBillgroupPojo> getListByModuleCode(String moduleCode) {
        try {
            //自定义报表
            List<SaBillgroupPojo> lst = saBillgroupMapper.getListByModuleCode(moduleCode);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
