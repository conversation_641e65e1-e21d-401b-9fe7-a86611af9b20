package inks.sa.common.core.service;

import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDeptPojo;
import inks.sa.common.core.domain.SaDeptEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 用户组织架构(SaDept)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-05 12:36:30
 */
public interface SaDeptService {


    SaDeptPojo getEntity(String key);

    PageInfo<SaDeptPojo> getPageList(QueryParam queryParam);

    SaDeptPojo insert(SaDeptPojo saDeptPojo);

    SaDeptPojo update(SaDeptPojo saDeptpojo);

    int delete(String key);

    List<SaDeptPojo> getListByParentid(String key);

    void getSubinfoAll(List<DeptinfoPojo> lst);

    List<DeptinfoPojo> getDeptinfoList(String key);

    List<DeptinfoPojo> getDeptinfolistByUser(String userid);
}
