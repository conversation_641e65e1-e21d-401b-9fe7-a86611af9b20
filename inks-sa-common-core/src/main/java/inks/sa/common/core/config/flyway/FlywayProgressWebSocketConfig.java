package inks.sa.common.core.config.flyway;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * Flyway进度显示WebSocket配置
 */
@Configuration
@EnableWebSocket
public class FlywayProgressWebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(flywayProgressWebSocketHandler(), "/flyway-progress-ws")
                .setAllowedOrigins("*"); // 允许所有来源，生产环境建议限制
    }

    @Bean
    public FlywayProgressWebSocketHandler flywayProgressWebSocketHandler() {
        return new FlywayProgressWebSocketHandler();
    }
}
