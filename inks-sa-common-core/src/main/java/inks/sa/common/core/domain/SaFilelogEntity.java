package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 文件上传/下载日志表(SaFilelog)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 14:22:54
 */
@Data
public class SaFilelogEntity implements Serializable {
    private static final long serialVersionUID = -40981541782650150L;
     // ID
    private String id;
     // 操作类型(0=上传,1=下载)
    private Integer optype;
     // 业务使用标识
    private Integer usedmark;
     // 原文件名
    private String fileoriname;
     // 文件桶
    private String bucketname;
     // 目录
    private String dirname;
     // 存储文件名
    private String filename;
     // OSS url
    private String fileurl;
     // 功能编码
    private String modulecode;
     // 模块
    private String module;
     // 文件大小(Byte)
    private Long filesize;
     // 文件格式（MIME）
    private String contenttype;
     // 文件后缀
    private String filesuffix;
     // 存储方式
    private String storage;
     // 排序码
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户ID
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

