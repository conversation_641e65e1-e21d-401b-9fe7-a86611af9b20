package inks.sa.common.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 配置过滤器，用于在请求结束时清理ThreadLocal
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 100)
public class InksConfigFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(InksConfigFilter.class);

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } finally {
            // 请求结束后清理ThreadLocal，防止内存泄漏 每一个请求都必定会触发，无论是否使用InksConfigThreadLocal.getConfig
            // 通过在过滤器中进行无条件清理，我们建立了一道最终防线。无论开发人员在业务代码中是否忘记了清理（或者因为异常导致没有清理），InksConfigFilter 都会保证资源被释放
            // 如果当前线程的 ThreadLocalMap 中没有这个 ThreadLocal 变量的条目（即这个请求从未调用过 getConfig()），那么 remove() 操作几乎是零成本的，不会产生任何性能损耗。
            InksConfigThreadLocal_Sa.clear();
            logger.debug("已清理InksConfigThreadLocal，请求路径: {}", ((HttpServletRequest) request).getRequestURI());
            //System.out.println("已清理InksConfigThreadLocal，请求路径: " + ((HttpServletRequest) request).getRequestURI());
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("InksConfigFilter初始化完成");
    }

    @Override
    public void destroy() {
        logger.info("InksConfigFilter销毁完成");
    }
} 