package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaAttachmentPojo;
import inks.sa.common.core.domain.SaAttachmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 附件中心(SaAttachment)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-29 13:26:54
 */
@Mapper
public interface SaAttachmentMapper {


    SaAttachmentPojo getEntity(@Param("key") String key);

    List<SaAttachmentPojo> getPageList(QueryParam queryParam);

    int insert(SaAttachmentEntity saAttachmentEntity);

    int update(SaAttachmentEntity saAttachmentEntity);

    int delete(@Param("key") String key);
    
}

