package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDeptuserPojo;
import inks.sa.common.core.domain.SaDeptuserEntity;

import com.github.pagehelper.PageInfo;

/**
 * 组织用户表(SaDeptuser)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-05 13:00:44
 */
public interface SaDeptuserService {


    SaDeptuserPojo getEntity(String key);

    PageInfo<SaDeptuserPojo> getPageList(QueryParam queryParam);

    SaDeptuserPojo insert(SaDeptuserPojo saDeptuserPojo);

    SaDeptuserPojo update(SaDeptuserPojo saDeptuserpojo);

    int delete(String key);
}
