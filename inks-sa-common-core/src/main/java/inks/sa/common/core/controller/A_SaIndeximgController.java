package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaIndeximgPojo;
import inks.sa.common.core.service.SaIndeximgService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * (Sa_IndexImg)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
@RestController
@RequestMapping("/SaIndexImg")
@Api(tags = "通用:轮播图管理")
public class A_SaIndeximgController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaIndeximgController.class);

    @Resource
    private SaIndeximgService saIndeximgService;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取详细信息", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.List")
    public R<SaIndeximgPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saIndeximgService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.List")
    public R<PageInfo<SaIndeximgPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_IndexImg.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saIndeximgService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增", notes = "新增", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.Add")
    public R<SaIndeximgPojo> create(@RequestBody String json) {
        try {
            SaIndeximgPojo saIndeximgPojo = JSONArray.parseObject(json, SaIndeximgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saIndeximgPojo.setCreateby(loginUser.getRealName());   // 创建者
            saIndeximgPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saIndeximgPojo.setCreatedate(new Date());   // 创建时间
            saIndeximgPojo.setLister(loginUser.getRealname());   // 制表
            saIndeximgPojo.setListerid(loginUser.getUserid());    // 制表id  
            saIndeximgPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saIndeximgService.insert(saIndeximgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改", notes = "修改", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.Edit")
    public R<SaIndeximgPojo> update(@RequestBody String json) {
        try {
            SaIndeximgPojo saIndeximgPojo = JSONArray.parseObject(json, SaIndeximgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saIndeximgPojo.setLister(loginUser.getRealname());   // 制表
            saIndeximgPojo.setListerid(loginUser.getUserid());    // 制表id  
            saIndeximgPojo.setModifydate(new Date());   //修改时间
//            saIndeximgPojo.setAssessor(""); // 审核员
//            saIndeximgPojo.setAssessorid(""); // 审核员id
//            saIndeximgPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saIndeximgService.update(saIndeximgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除", notes = "删除", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saIndeximgService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_IndexImg.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaIndeximgPojo saIndeximgPojo = this.saIndeximgService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saIndeximgPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

