//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package inks.sa.common.core.annotation.aspect;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import javax.annotation.Resource;

import inks.sa.common.core.annotation.FieldFilter_Sa;
import inks.sa.common.core.service.SaRedisService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class Sa_FieldFilterAspect {
    @Resource
    private SaRedisService saRedisService;
    private static final Logger logger = LoggerFactory.getLogger(Sa_FieldFilterAspect.class);

    public Sa_FieldFilterAspect() {
    }

    @Around("@annotation(fieldFilterSa)")
    public Object around(ProceedingJoinPoint point, FieldFilter_Sa fieldFilterSa) throws Throwable {
        logger.info("=========进入FieldFilterAspect环绕通知=========");
        Object result = point.proceed();
        if (result instanceof R) {
            R<?> r = (R)result;
            Object data = r.getData();
            if (data != null) {
                LoginUser loginUser = this.saRedisService.getLoginUser();
                Sa_FieldFilterService.filterAmount(data, loginUser, fieldFilterSa.permission());
            }
        }

        return result;
    }
}
