package inks.sa.common.core.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限关系(SaPermission)实体类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:32
 */
public class SaPermissionPojo implements Serializable {
    private static final long serialVersionUID = -14065594717330710L;
    // id
    @Excel(name = "id")
    private String id;
    // 对象类别
    @Excel(name = "对象类别")
    private String resourcetype;
    // 对象id
    @Excel(name = "对象id")
    private String resourceid;
    // 权限id
    @Excel(name = "权限id")
    private String permid;
    // 权限编码
    @Excel(name = "权限编码")
    private String permcode;
    // 权限名称
    @Excel(name = "权限名称")
    private String permname;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 父级主键

    private String parentid;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 对象类别
    public String getResourcetype() {
        return resourcetype;
    }

    public void setResourcetype(String resourcetype) {
        this.resourcetype = resourcetype;
    }

    // 对象id
    public String getResourceid() {
        return resourceid;
    }

    public void setResourceid(String resourceid) {
        this.resourceid = resourceid;
    }

    // 权限id
    public String getPermid() {
        return permid;
    }

    public void setPermid(String permid) {
        this.permid = permid;
    }

    // 权限编码
    public String getPermcode() {
        return permcode;
    }

    public void setPermcode(String permcode) {
        this.permcode = permcode;
    }

    // 权限名称
    public String getPermname() {
        return permname;
    }

    public void setPermname(String permname) {
        this.permname = permname;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

