package inks.sa.common.core.config;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

//@RestControllerAdvice
//public class GlobalExceptionHandler {
//    @ExceptionHandler(StorageException.class)
//    public ResponseEntity<String> handleStorageException(StorageException e) {
//        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//            .body("Storage operation failed: " + e.getMessage());
//    }
//}