package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaUserroleEntity;
import inks.sa.common.core.domain.pojo.SaConfigPojo;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import inks.sa.common.core.domain.pojo.SaUserrolePojo;
import inks.sa.common.core.mapper.SaUserroleMapper;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaUserroleService;
import inks.sa.common.core.utils.PrintColor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户角色关联(SaUserrole)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:28
 */
@Service("saUserroleService")
public class SaUserroleServiceImpl implements SaUserroleService {
    @Resource
    private SaUserroleMapper saUserroleMapper;
    @Resource
    private SaRedisService saRedisService;

    private static void cleanNull(SaUserrolePojo saUserrolePojo) {
        if (saUserrolePojo.getId() == null) saUserrolePojo.setId("");
        if (saUserrolePojo.getRoleid() == null) saUserrolePojo.setRoleid("");
        if (saUserrolePojo.getUserid() == null) saUserrolePojo.setUserid("");
        if (saUserrolePojo.getRownum() == null) saUserrolePojo.setRownum(0);
        if (saUserrolePojo.getCreateby() == null) saUserrolePojo.setCreateby("");
        if (saUserrolePojo.getCreatebyid() == null) saUserrolePojo.setCreatebyid("");
        if (saUserrolePojo.getCreatedate() == null) saUserrolePojo.setCreatedate(new Date());
        if (saUserrolePojo.getLister() == null) saUserrolePojo.setLister("");
        if (saUserrolePojo.getListerid() == null) saUserrolePojo.setListerid("");
        if (saUserrolePojo.getModifydate() == null) saUserrolePojo.setModifydate(new Date());
        if (saUserrolePojo.getTenantid() == null) saUserrolePojo.setTenantid("");
        if (saUserrolePojo.getTenantname() == null) saUserrolePojo.setTenantname("");
        if (saUserrolePojo.getRevision() == null) saUserrolePojo.setRevision(0);
    }

    @Override
    public SaUserrolePojo getEntity(String key) {
        return this.saUserroleMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaUserrolePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUserrolePojo> lst = saUserroleMapper.getPageList(queryParam);
            PageInfo<SaUserrolePojo> pageInfo = new PageInfo<SaUserrolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaUserrolePojo insert(SaUserrolePojo saUserrolePojo) {
        //初始化NULL字段
        cleanNull(saUserrolePojo);
        SaUserroleEntity saUserroleEntity = new SaUserroleEntity();
        BeanUtils.copyProperties(saUserrolePojo, saUserroleEntity);
        //生成雪花id
        saUserroleEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saUserroleEntity.setRevision(1);  //乐观锁
        this.saUserroleMapper.insert(saUserroleEntity);
        return this.getEntity(saUserroleEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saUserrolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserrolePojo update(SaUserrolePojo saUserrolePojo) {
        SaUserroleEntity saUserroleEntity = new SaUserroleEntity();
        BeanUtils.copyProperties(saUserrolePojo, saUserroleEntity);
        this.saUserroleMapper.update(saUserroleEntity);
        return this.getEntity(saUserroleEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saUserroleMapper.delete(key);
    }

    @Override
    public List<SaUserrolePojo> getListByRole(String key) {
        return this.saUserroleMapper.getListByRole(key);
    }

    @Override
    public List<SaUserrolePojo> getListByUser(String key) {
        return this.saUserroleMapper.getListByUser(key);
    }

    @Override
    public List<SaPermcodePojo> getPermByUser(String key) {
        return this.saUserroleMapper.getPermByUser(key);
    }

    @Override
    public HashSet<String> getPermSetByUserNoRedis(String key) {//key是userid
        //从redis中获取Reprot内容
//        HashSet<String> setPerm = this.saRedisService.getCacheObject("user_permset:" + tid + "-" + key, HashSet.class);
//        if (setPerm == null) {
        HashSet<String> setPerm = new HashSet<>();
        List<SaPermcodePojo> lst = this.getPermByUser(key);
        for (SaPermcodePojo saPermcodePojo : lst) {
            if (saPermcodePojo.getPermtype() != null) {
                if (saPermcodePojo.getPermtype().equals("3")) setPerm.add(saPermcodePojo.getPermcode());  //按键作用
            }
        }
//        this.saRedisService.setCacheObject("user_permset:" + tid + "-" + key, setPerm, 30, TimeUnit.DAYS);
//        }
        return setPerm;
    }

    @Override
    public Map<String, String> getConfigMapByTenUiNoRedis() {
//        //从redis中获取Reprot内容
//        Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.TENANT_CONFIG_KEY + "_UI_" + tid);
//        if (mapConfig == null || db == 1) {
        Map<String, String> mapConfig = new HashMap<>();
        List<SaConfigPojo> lst = this.saUserroleMapper.getConfigListAll();
        for (SaConfigPojo pojo : lst) {
            mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
        }
//            this.redisService.setCacheObject(CacheConstants.TENANT_CONFIG_KEY + "_UI_" + tid, mapConfig, (long) (30), TimeUnit.DAYS);
//        }
        return mapConfig;
    }

    @Override
    public Map<String, String> getConfigMapByUserUiNoRedis(String userid) {
//        //从redis中获取Reprot内容
//        Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.USER_CONFIG_KEY + "_UI_" + tid + "_" + userid);
//        if (mapConfig == null) {
        Map<String, String> mapConfig = new HashMap<>();
        List<SaConfigPojo> lst = this.saUserroleMapper.getConfigListByUserid(userid);
        for (SaConfigPojo pojo : lst) {
            mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
        }
//            this.redisService.setCacheObject(CacheConstants.USER_CONFIG_KEY + "_UI_" + tid + "_" + userid, mapConfig, (long) (30), TimeUnit.DAYS);
//        }
        return mapConfig;
    }

    // 仅在LoginUser中加入权限和参数    Controller层的/SaUserRole/updateToken方法会额外操作覆盖原Sa_Redis中的token
    @Override
    public LoginUser updateTokenPerm(LoginUser loginUser) {
        // 获得用户所有权限(不查redis,直接查DB)
        HashSet<String> set = this.getPermSetByUserNoRedis(loginUser.getUserid());
        loginUser.setPermissions(set);
        // 获取config内容 (不查redis,直接查DB)
        Map<String, String> mapConfig = this.getConfigMapByTenUiNoRedis();
        // 用户参数  (不查redis,直接查DB)
        Map<String, String> mapUserCfg = this.getConfigMapByUserUiNoRedis(loginUser.getUserid());
        for (String mapkey : mapUserCfg.keySet()) {
            mapConfig.put(mapkey, mapUserCfg.get(mapkey));
        }
        PrintColor.zi(loginUser.getUsername() + "获得 " + set.size() + " 条权限 和 " + mapConfig.size() + " 条参数 ");
        loginUser.setConfigs(mapConfig);
//            this.saRedisService.deleteObject(MyConstant.LOGIN_TOKEN_KEY + token);
//            Map<String, Object> map = this.saRedisService.createToken(loginUser);
//        // 加入权限和参数后直接覆盖掉原token
//        this.saRedisService.setCacheObject(MyConstant.LOGIN_TOKEN_KEY + token, loginUser, 43200);
        return loginUser;
    }
}
