////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//package inks.sa.common.core.config.flyway;
//
//import inks.sa.common.core.utils.PrintColor;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.sql.Statement;
//import javax.annotation.PostConstruct;
//import javax.sql.DataSource;
//import org.flywaydb.core.Flyway;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.jdbc.DataSourceBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.Order;
//
//
//
////对应的yml只要配置一个关闭自带flyway，走次类的逻辑：
//// spring:
////   flyway:
////    enabled: false
//@Configuration
//@Order(Integer.MIN_VALUE)
//public class DataSourceHelper_db下sql初始化 {
//    @Value("${spring.datasource.url:}")
//    private String dbUrl;
//    @Value("${spring.datasource.username:}")
//    private String dbUsername;
//    @Value("${spring.datasource.password:}")
//    private String dbPassword;
//    @Value("${spring.datasource.driver-class-name:}")
//    private String dbDriverClassName;
//    private final String flywayLocations = "classpath:db/migration";
//    private final boolean autoCreateDbEnabled = true;
//
//    @PostConstruct
//    public void init() {
//        try {
//            Class.forName(this.dbDriverClassName);
//            URI uri = new URI(this.dbUrl.replace("jdbc:", ""));
//            String host = uri.getHost();
//            int port = uri.getPort();
//            String path = uri.getPath();
//            String query = uri.getQuery();
//            String databaseName = path.replace("/", "");
//            boolean databaseExists = this.checkDatabaseExists(host, port, query, databaseName);
//            if (!databaseExists) {
//                long startTime = System.currentTimeMillis();
//                PrintColor.red("数据库 [" + databaseName + "] 不存在，开始创建数据库并执行迁移...");
//                if ("com.mysql.cj.jdbc.Driver".equals(this.dbDriverClassName)) {
//                    Connection connection = DriverManager.getConnection("jdbc:mysql://" + host + ":" + port + "?" + query, this.dbUsername, this.dbPassword);
//                    Statement statement = connection.createStatement();
//                    statement.executeUpdate("CREATE DATABASE IF NOT EXISTS `" + databaseName + "` DEFAULT CHARACTER SET = `utf8mb4` COLLATE `utf8mb4_bin`;");
//                    statement.close();
//                    connection.close();
//                }
//
//                this.executeFlyway();
//                long endTime = System.currentTimeMillis();
//                long elapsedTime = endTime - startTime;
//                PrintColor.red("数据库 [" + databaseName + "] 创建完成，Flyway初始化数据表执行完毕。耗时：" + elapsedTime + " 毫秒");
//            } else {
//                PrintColor.red("数据库 [" + databaseName + "] 已存在，跳过初始化数据表步骤。");
//            }
//        } catch (ClassNotFoundException | SQLException | URISyntaxException e) {
//            ((Exception)e).printStackTrace();
//            System.exit(0);
//        }
//
//    }
//
//    private boolean checkDatabaseExists(String host, int port, String query, String databaseName) {
//        try {
//            Connection connection = DriverManager.getConnection("jdbc:mysql://" + host + ":" + port + "?" + query, this.dbUsername, this.dbPassword);
//            Statement statement = connection.createStatement();
//            String sql = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" + databaseName + "'";
//            ResultSet resultSet = statement.executeQuery(sql);
//            boolean exists = resultSet.next();
//            resultSet.close();
//            statement.close();
//            connection.close();
//            return exists;
//        } catch (SQLException e) {
//            System.err.println("检查数据库存在性时发生错误: " + e.getMessage());
//            return false;
//        }
//    }
//
//    private void executeFlyway() {
//        DataSource dataSource = DataSourceBuilder.create().url(this.dbUrl).username(this.dbUsername).password(this.dbPassword).build();
//        Flyway flyway = Flyway.configure().dataSource(dataSource).locations("classpath:db/migration".split(",")).baselineOnMigrate(true).baselineVersion("0").outOfOrder(true).validateOnMigrate(true).placeholderReplacement(false).load();
//        flyway.migrate();
//    }
//
//    @Bean
//    public FlywayMigrationCompleted flywayMigrationCompleted() {
//        return new FlywayMigrationCompleted();
//    }
//
//    public static class FlywayMigrationCompleted {
//    }
//}
