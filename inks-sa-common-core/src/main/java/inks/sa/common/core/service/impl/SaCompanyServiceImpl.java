package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaCompanyEntity;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;
import inks.sa.common.core.mapper.SaCompanyMapper;
import inks.sa.common.core.service.SaCompanyService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 公司信息表(SaCompany)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-22 12:48:49
 */
@Service("saCompanyService")
public class SaCompanyServiceImpl implements SaCompanyService {
    @Resource
    private SaCompanyMapper saCompanyMapper;


    @Override
    public SaCompanyPojo getEntity(String key) {
        return this.saCompanyMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaCompanyPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCompanyPojo> lst = saCompanyMapper.getPageList(queryParam);
            PageInfo<SaCompanyPojo> pageInfo = new PageInfo<SaCompanyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saCompanyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCompanyPojo insert(SaCompanyPojo saCompanyPojo) {
        //初始化NULL字段
        if (saCompanyPojo.getName() == null) saCompanyPojo.setName("");
        if (saCompanyPojo.getEnglishname() == null) saCompanyPojo.setEnglishname("");
        if (saCompanyPojo.getCreditcode() == null) saCompanyPojo.setCreditcode("");
        if (saCompanyPojo.getAddress() == null) saCompanyPojo.setAddress("");
        if (saCompanyPojo.getBankaccount() == null) saCompanyPojo.setBankaccount("");
        if (saCompanyPojo.getBankofdeposit() == null) saCompanyPojo.setBankofdeposit("");
        if (saCompanyPojo.getContactperson() == null) saCompanyPojo.setContactperson("");
        if (saCompanyPojo.getTel() == null) saCompanyPojo.setTel("");
        if (saCompanyPojo.getRemark() == null) saCompanyPojo.setRemark("");
        if (saCompanyPojo.getCreateby() == null) saCompanyPojo.setCreateby("");
        if (saCompanyPojo.getCreatebyid() == null) saCompanyPojo.setCreatebyid("");
        if (saCompanyPojo.getCreatedate() == null) saCompanyPojo.setCreatedate(new Date());
        if (saCompanyPojo.getLister() == null) saCompanyPojo.setLister("");
        if (saCompanyPojo.getListerid() == null) saCompanyPojo.setListerid("");
        if (saCompanyPojo.getModifydate() == null) saCompanyPojo.setModifydate(new Date());
        if (saCompanyPojo.getTenantid() == null) saCompanyPojo.setTenantid("");
        if (saCompanyPojo.getTenantname() == null) saCompanyPojo.setTenantname("");
        if (saCompanyPojo.getRevision() == null) saCompanyPojo.setRevision(0);
        SaCompanyEntity saCompanyEntity = new SaCompanyEntity();
        BeanUtils.copyProperties(saCompanyPojo, saCompanyEntity);
        //生成雪花id
        saCompanyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saCompanyEntity.setRevision(1);  //乐观锁
        this.saCompanyMapper.insert(saCompanyEntity);
        return this.getEntity(saCompanyEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saCompanyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCompanyPojo update(SaCompanyPojo saCompanyPojo) {
        SaCompanyEntity saCompanyEntity = new SaCompanyEntity();
        BeanUtils.copyProperties(saCompanyPojo, saCompanyEntity);
        this.saCompanyMapper.update(saCompanyEntity);
        return this.getEntity(saCompanyEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saCompanyMapper.delete(key);
    }

    @Override
    public SaCompanyPojo getCompanyInfo() {
        return saCompanyMapper.getEntity("1");
    }
}
