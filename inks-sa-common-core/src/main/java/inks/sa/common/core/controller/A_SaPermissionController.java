package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaPermissionPojo;
import inks.sa.common.core.service.SaPermissionService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 权限关系(Sa_Permission)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:31
 */
@RestController
@RequestMapping("/SaPermission")
@Api(tags = "通用:权限关系")
public class A_SaPermissionController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaPermissionController.class);
    @Resource
    private SaPermissionService saPermissionService;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按角色查询权限", notes = "按角色查询权限", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    public R<List<SaPermissionPojo>> getListByRole(String key) {
        try {
            return R.ok(this.saPermissionService.getListByRole(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取一个用户的所有权限", notes = "获取一个用户的所有权限", produces = "application/json")
    @RequestMapping(value = "/getUserAllPerm", method = RequestMethod.GET)
    public R<List<SaPermissionPojo>> getUserAllPerm(String key) {
        try {
            return R.ok(this.saPermissionService.getUserAllPerm(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取权限关系详细信息", notes = "获取权限关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Permission.List")
    public R<SaPermissionPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saPermissionService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Permission.List")
    public R<PageInfo<SaPermissionPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Permission.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saPermissionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增权限关系", notes = "新增权限关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Permission.Add")
    public R<SaPermissionPojo> create(@RequestBody String json) {
        try {
            SaPermissionPojo saPermissionPojo = JSONArray.parseObject(json, SaPermissionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPermissionPojo.setCreateby(loginUser.getRealName());   // 创建者
            saPermissionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saPermissionPojo.setCreatedate(new Date());   // 创建时间
            saPermissionPojo.setLister(loginUser.getRealname());   // 制表
            saPermissionPojo.setListerid(loginUser.getUserid());    // 制表id
            saPermissionPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saPermissionService.insert(saPermissionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改权限关系", notes = "修改权限关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Permission.Edit")
    public R<SaPermissionPojo> update(@RequestBody String json) {
        try {
            SaPermissionPojo saPermissionPojo = JSONArray.parseObject(json, SaPermissionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saPermissionPojo.setLister(loginUser.getRealname());   // 制表
            saPermissionPojo.setListerid(loginUser.getUserid());    // 制表id
            saPermissionPojo.setModifydate(new Date());   //修改时间
            //            saPermissionPojo.setAssessor(""); // 审核员
            //            saPermissionPojo.setAssessorid(""); // 审核员id
            //            saPermissionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saPermissionService.update(saPermissionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除权限关系", notes = "删除权限关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Permission.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saPermissionService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Permission.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaPermissionPojo saPermissionPojo = this.saPermissionService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saPermissionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

