package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDictitemPojo;

import java.util.List;

/**
 * 字典子表(SaDictitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:35
 */
public interface SaDictitemService {


    SaDictitemPojo getEntity(String key);

    PageInfo<SaDictitemPojo> getPageList(QueryParam queryParam);


    List<SaDictitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saDictitemPojo 实例对象
     * @return 实例对象
     */
    SaDictitemPojo insert(SaDictitemPojo saDictitemPojo);

    /**
     * 修改数据
     *
     * @param saDictitempojo 实例对象
     * @return 实例对象
     */
    SaDictitemPojo update(SaDictitemPojo saDictitempojo);

    int delete(String key);

    /**
     * 修改数据
     *
     * @param saDictitempojo 实例对象
     * @return 实例对象
     */
    SaDictitemPojo clearNull(SaDictitemPojo saDictitempojo);
}
