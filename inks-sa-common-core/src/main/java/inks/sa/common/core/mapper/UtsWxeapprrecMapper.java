package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.config.oa.vo.UtsWxeapprPojo;
import inks.sa.common.core.config.oa.vo.UtsWxeapprrecEntity;
import inks.sa.common.core.config.oa.vo.UtsWxeapprrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信审批记录(UtsWxeapprrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Mapper
public interface UtsWxeapprrecMapper {


    UtsWxeapprrecPojo getEntity(@Param("key") String key);

    List<UtsWxeapprrecPojo> getPageList(QueryParam queryParam);

    int insert(UtsWxeapprrecEntity utsWxeapprrecEntity);

    int update(UtsWxeapprrecEntity utsWxeapprrecEntity);

    int delete(@Param("key") String key);

    UtsWxeapprrecPojo getEntityBySpno(@Param("key") String key, @Param("tid") String tid);

    List<UtsWxeapprrecPojo> getOnlineByBillid(@Param("key") String key, @Param("tid") String tid);

    List<UtsWxeapprPojo> getListByModuleCode(String code, String tid);
}

