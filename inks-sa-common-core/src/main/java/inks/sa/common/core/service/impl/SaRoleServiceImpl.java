package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaRoleEntity;
import inks.sa.common.core.domain.pojo.SaRolePojo;
import inks.sa.common.core.domain.pojo.SaUserrolePojo;
import inks.sa.common.core.mapper.SaRoleMapper;
import inks.sa.common.core.service.SaRoleService;
import inks.sa.common.core.service.SaUserroleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 角色(SaRole)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:31
 */
@Service("saRoleService")
public class SaRoleServiceImpl implements SaRoleService {
    @Resource
    private SaRoleMapper saRoleMapper;
    @Resource
    private SaUserroleService saUserroleService;

    private static void cleanNull(SaRolePojo saRolePojo) {
        if (saRolePojo.getRoleid() == null) saRolePojo.setRoleid("");
        if (saRolePojo.getRolecode() == null) saRolePojo.setRolecode("");
        if (saRolePojo.getRolename() == null) saRolePojo.setRolename("");
        if (saRolePojo.getFunctionid() == null) saRolePojo.setFunctionid("");
        if (saRolePojo.getFunctioncode() == null) saRolePojo.setFunctioncode("");
        if (saRolePojo.getFunctionname() == null) saRolePojo.setFunctionname("");
        if (saRolePojo.getEnabledmark() == null) saRolePojo.setEnabledmark(0);
        if (saRolePojo.getRownum() == null) saRolePojo.setRownum(0);
        if (saRolePojo.getRemark() == null) saRolePojo.setRemark("");
        if (saRolePojo.getCreateby() == null) saRolePojo.setCreateby("");
        if (saRolePojo.getCreatebyid() == null) saRolePojo.setCreatebyid("");
        if (saRolePojo.getCreatedate() == null) saRolePojo.setCreatedate(new Date());
        if (saRolePojo.getLister() == null) saRolePojo.setLister("");
        if (saRolePojo.getListerid() == null) saRolePojo.setListerid("");
        if (saRolePojo.getModifydate() == null) saRolePojo.setModifydate(new Date());
        if (saRolePojo.getTenantid() == null) saRolePojo.setTenantid("");
        if (saRolePojo.getTenantname() == null) saRolePojo.setTenantname("");
        if (saRolePojo.getRevision() == null) saRolePojo.setRevision(0);
    }

    @Override
    public SaRolePojo getEntity(String key) {
        return this.saRoleMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaRolePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRolePojo> lst = saRoleMapper.getPageList(queryParam);
            PageInfo<SaRolePojo> pageInfo = new PageInfo<SaRolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaRolePojo insert(SaRolePojo saRolePojo) {
        //初始化NULL字段
        cleanNull(saRolePojo);
        SaRoleEntity saRoleEntity = new SaRoleEntity();
        BeanUtils.copyProperties(saRolePojo, saRoleEntity);
        //生成雪花id
        saRoleEntity.setRoleid(inksSnowflake.getSnowflake().nextIdStr());
        saRoleEntity.setRevision(1);  //乐观锁
        this.saRoleMapper.insert(saRoleEntity);
        return this.getEntity(saRoleEntity.getRoleid());

    }

    /**
     * 修改数据
     *
     * @param saRolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRolePojo update(SaRolePojo saRolePojo) {
        SaRoleEntity saRoleEntity = new SaRoleEntity();
        BeanUtils.copyProperties(saRolePojo, saRoleEntity);
        this.saRoleMapper.update(saRoleEntity);
        return this.getEntity(saRoleEntity.getRoleid());
    }

    @Override
    public int delete(String key) {
        // 删除角色需检查是否被用户关联
        List<SaUserrolePojo> listUserByRole = this.saUserroleService.getListByRole(key);//根据角色id获取关系List
        if (CollectionUtils.isNotEmpty(listUserByRole)) {
            throw new BaseBusinessException("该角色下存在关联用户,禁止删除");
        }
        return this.saRoleMapper.delete(key);
    }

}
