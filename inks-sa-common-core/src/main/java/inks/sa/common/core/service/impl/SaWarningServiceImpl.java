package inks.sa.common.core.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaWarningPojo;
import inks.sa.common.core.domain.SaWarningEntity;
import inks.sa.common.core.mapper.SaWarningMapper;
import inks.sa.common.core.service.SaWarningService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 预警(SaWarning)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Service("saWarningService")
public class SaWarningServiceImpl implements SaWarningService {
    @Resource
    private SaWarningMapper saWarningMapper;

    @Override
    public SaWarningPojo getEntity(String key) {
        return this.saWarningMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaWarningPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWarningPojo> lst = saWarningMapper.getPageList(queryParam);
            PageInfo<SaWarningPojo> pageInfo = new PageInfo<SaWarningPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaWarningPojo insert(SaWarningPojo saWarningPojo) {
        //初始化NULL字段
        cleanNull(saWarningPojo);
        SaWarningEntity saWarningEntity = new SaWarningEntity(); 
        BeanUtils.copyProperties(saWarningPojo,saWarningEntity);
        //生成雪花id
          saWarningEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saWarningEntity.setRevision(1);  //乐观锁
          this.saWarningMapper.insert(saWarningEntity);
        return this.getEntity(saWarningEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saWarningPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWarningPojo update(SaWarningPojo saWarningPojo) {
        SaWarningEntity saWarningEntity = new SaWarningEntity(); 
        BeanUtils.copyProperties(saWarningPojo,saWarningEntity);
        this.saWarningMapper.update(saWarningEntity);
        return this.getEntity(saWarningEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saWarningMapper.delete(key) ;
    }
    

    private static void cleanNull(SaWarningPojo saWarningPojo) {
        if(saWarningPojo.getGengroupid()==null) saWarningPojo.setGengroupid("");
        if(saWarningPojo.getModulecode()==null) saWarningPojo.setModulecode("");
        if(saWarningPojo.getWarncode()==null) saWarningPojo.setWarncode("");
        if(saWarningPojo.getWarnname()==null) saWarningPojo.setWarnname("");
        if(saWarningPojo.getWarnfield()==null) saWarningPojo.setWarnfield("");
        if(saWarningPojo.getSvccode()==null) saWarningPojo.setSvccode("");
        if(saWarningPojo.getWarnapi()==null) saWarningPojo.setWarnapi("");
        if(saWarningPojo.getWebpath()==null) saWarningPojo.setWebpath("");
        if(saWarningPojo.getImagecss()==null) saWarningPojo.setImagecss("");
        if(saWarningPojo.getTagtitle()==null) saWarningPojo.setTagtitle("");
        if(saWarningPojo.getPermcode()==null) saWarningPojo.setPermcode("");
        if(saWarningPojo.getRownum()==null) saWarningPojo.setRownum(0);
        if(saWarningPojo.getEnabledmark()==null) saWarningPojo.setEnabledmark(0);
        if(saWarningPojo.getRemark()==null) saWarningPojo.setRemark("");
        if(saWarningPojo.getCreateby()==null) saWarningPojo.setCreateby("");
        if(saWarningPojo.getCreatebyid()==null) saWarningPojo.setCreatebyid("");
        if(saWarningPojo.getCreatedate()==null) saWarningPojo.setCreatedate(new Date());
        if(saWarningPojo.getLister()==null) saWarningPojo.setLister("");
        if(saWarningPojo.getListerid()==null) saWarningPojo.setListerid("");
        if(saWarningPojo.getModifydate()==null) saWarningPojo.setModifydate(new Date());
        if(saWarningPojo.getRevision()==null) saWarningPojo.setRevision(0);
   }

}
