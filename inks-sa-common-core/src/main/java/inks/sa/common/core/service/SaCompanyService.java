package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;

/**
 * 公司信息表(SaCompany)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-22 12:48:49
 */
public interface SaCompanyService {


    SaCompanyPojo getEntity(String key);

    PageInfo<SaCompanyPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saCompanyPojo 实例对象
     * @return 实例对象
     */
    SaCompanyPojo insert(SaCompanyPojo saCompanyPojo);

    /**
     * 修改数据
     *
     * @param saCompanypojo 实例对象
     * @return 实例对象
     */
    SaCompanyPojo update(SaCompanyPojo saCompanypojo);

    int delete(String key);

    SaCompanyPojo getCompanyInfo();
}
