package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaPermcodeEntity;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import inks.sa.common.core.mapper.SaPermcodeMapper;
import inks.sa.common.core.service.SaPermcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 权限编码(SaPermcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:36
 */
@Service("saPermcodeService")
public class SaPermcodeServiceImpl implements SaPermcodeService {
    @Resource
    private SaPermcodeMapper saPermcodeMapper;

    private static void cleanNull(SaPermcodePojo saPermcodePojo) {
        if (saPermcodePojo.getPermid() == null) saPermcodePojo.setPermid("");
        if (saPermcodePojo.getParentid() == null) saPermcodePojo.setParentid("");
        if (saPermcodePojo.getPermtype() == null) saPermcodePojo.setPermtype("");
        if (saPermcodePojo.getPermcode() == null) saPermcodePojo.setPermcode("");
        if (saPermcodePojo.getPermname() == null) saPermcodePojo.setPermname("");
        if (saPermcodePojo.getRownum() == null) saPermcodePojo.setRownum(0);
        if (saPermcodePojo.getIspublic() == null) saPermcodePojo.setIspublic(0);
        if (saPermcodePojo.getEnabledmark() == null) saPermcodePojo.setEnabledmark(0);
        if (saPermcodePojo.getAllowdelete() == null) saPermcodePojo.setAllowdelete(0);
        if (saPermcodePojo.getRemark() == null) saPermcodePojo.setRemark("");
        if (saPermcodePojo.getCreateby() == null) saPermcodePojo.setCreateby("");
        if (saPermcodePojo.getCreatebyid() == null) saPermcodePojo.setCreatebyid("");
        if (saPermcodePojo.getCreatedate() == null) saPermcodePojo.setCreatedate(new Date());
        if (saPermcodePojo.getLister() == null) saPermcodePojo.setLister("");
        if (saPermcodePojo.getListerid() == null) saPermcodePojo.setListerid("");
        if (saPermcodePojo.getModifydate() == null) saPermcodePojo.setModifydate(new Date());
        if (saPermcodePojo.getRevision() == null) saPermcodePojo.setRevision(0);
    }

    @Override
    public SaPermcodePojo getEntity(String key) {
        return this.saPermcodeMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaPermcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaPermcodePojo> lst = saPermcodeMapper.getPageList(queryParam);
            PageInfo<SaPermcodePojo> pageInfo = new PageInfo<SaPermcodePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaPermcodePojo insert(SaPermcodePojo saPermcodePojo) {
        //初始化NULL字段
        cleanNull(saPermcodePojo);
        SaPermcodeEntity saPermcodeEntity = new SaPermcodeEntity();
        BeanUtils.copyProperties(saPermcodePojo, saPermcodeEntity);
        //生成雪花id
        saPermcodeEntity.setPermid(inksSnowflake.getSnowflake().nextIdStr());
        saPermcodeEntity.setRevision(1);  //乐观锁
        this.saPermcodeMapper.insert(saPermcodeEntity);
        return this.getEntity(saPermcodeEntity.getPermid());

    }

    /**
     * 修改数据
     *
     * @param saPermcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaPermcodePojo update(SaPermcodePojo saPermcodePojo) {
        SaPermcodeEntity saPermcodeEntity = new SaPermcodeEntity();
        BeanUtils.copyProperties(saPermcodePojo, saPermcodeEntity);
        this.saPermcodeMapper.update(saPermcodeEntity);
        return this.getEntity(saPermcodeEntity.getPermid());
    }

    @Override
    public int delete(String key) {
        return this.saPermcodeMapper.delete(key);
    }

}
