package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDirrulePojo;
import inks.sa.common.core.domain.SaDirruleEntity;

import com.github.pagehelper.PageInfo;

/**
 * 目录规则配置表(SaDirrule)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 14:21:06
 */
public interface SaDirruleService {


    SaDirrulePojo getEntity(String key);

    PageInfo<SaDirrulePojo> getPageList(QueryParam queryParam);

    SaDirrulePojo insert(SaDirrulePojo saDirrulePojo);

    SaDirrulePojo update(SaDirrulePojo saDirrulepojo);

    int delete(String key);
}
