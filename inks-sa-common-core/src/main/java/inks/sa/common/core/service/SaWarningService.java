package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaWarningPojo;
import inks.sa.common.core.domain.SaWarningEntity;

import com.github.pagehelper.PageInfo;

/**
 * 预警(SaWarning)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
public interface SaWarningService {


    SaWarningPojo getEntity(String key);

    PageInfo<SaWarningPojo> getPageList(QueryParam queryParam);

    SaWarningPojo insert(SaWarningPojo saWarningPojo);

    SaWarningPojo update(SaWarningPojo saWarningpojo);

    int delete(String key);
}
