package inks.sa.common.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * UTS服务反向代理控制器
 * <p>
 * 功能：
 * 1. 将/uts/**路径的请求代理到配置的后端服务
 * 2. 过滤敏感请求头（如Cookie、Host）
 * 3. 自动处理Location重定向头的路径转换
 * 4. 追加X-Forwarded-For请求头记录客户端IP
 */
@RestController
@RequestMapping("/uts/**")
public class UtsProxyController {
    // 日志记录器
    private static final Logger log = LoggerFactory.getLogger(UtsProxyController.class);
    // 正则表达式：匹配连续多个斜杠（用于路径清理）
    private static final Pattern MULTIPLE_SLASHES = Pattern.compile("/{2,}");

    // 后端UTS服务的基础URL（从配置文件中注入）
    @Value("${inks.feign.UtsUrl:http://localhost:10684}")
    private String utsBaseUrl;

    // 需要过滤的敏感请求头（逗号分隔，例如：Host,Cookie,X-Forwarded-For,X-Forwarded-Proto,X-Forwarded-Port）
    @Value("${uts.filter-headers:}")
    private String filterHeadersString;

    // 应用的上下文路径（如部署在Tomcat的/app路径下）
    @Value("${server.servlet.context-path:}")
    private String contextPath;

    // 过滤头的列表形式（根据filterHeadersString转换）
    private List<String> filteredHeaders;

    // REST请求模板（用于转发请求）
    @Resource
    private RestTemplate restTemplate;

    /**
     * 初始化方法（在Bean创建后自动执行）
     * 1. 解析过滤头配置
     * 2. 确保utsBaseUrl以斜杠结尾
     */
    @PostConstruct
    public void init() {
        // 将逗号分隔的过滤头转换为列表
        filteredHeaders = Arrays.stream(filterHeadersString.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        // 保证基础URL以斜杠结尾，避免路径拼接错误
        if (!utsBaseUrl.endsWith("/")) {
            utsBaseUrl += "/";
        }
    }

    @RequestMapping
    public ResponseEntity<byte[]> proxyAll(HttpServletRequest request, @RequestBody(required = false) byte[] body) {
        try {
            URI targetUri = buildTargetUri(request);
            log.info("原始请求URL: {}", request.getRequestURL().toString());
            log.info("转发目标URL: {}", targetUri);

            HttpHeaders headers = buildRequestHeaders(request);

            log.debug("发送到UTS的请求头: {}", headers);

            ResponseEntity<byte[]> response = restTemplate.exchange(
                    targetUri,
                    HttpMethod.valueOf(request.getMethod()),
                    new HttpEntity<>(body, headers),
                    byte[].class
            );

            log.info("从UTS收到响应: 状态码 {}", response.getStatusCode());
            log.debug("从UTS收到的响应头: {}", response.getHeaders());
            if (response.getBody() == null || response.getBody().length == 0) {
                log.warn("UTS响应体为空!");
            } else {
                log.info("UTS响应体大小: {} 字节", response.getBody().length);
            }

            // 智能响应头处理 - 解决K8s环境兼容性问题
            HttpHeaders responseHeaders = processResponseHeaders(response.getHeaders());

            // 验证响应完整性
            validateResponseIntegrity(response, responseHeaders);

            return ResponseEntity.status(response.getStatusCode())
                    .headers(responseHeaders)
                    .body(response.getBody());

        } catch (HttpClientErrorException e) {
            log.warn("后端服务(UTS)错误: {} {}", e.getStatusCode(), e.getStatusText(), e);
            return ResponseEntity.status(e.getStatusCode())
                    .headers(e.getResponseHeaders())
                    .body(e.getResponseBodyAsByteArray());
        } catch (ResourceAccessException e) {
            log.error("访问UTS服务超时或网络错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.GATEWAY_TIMEOUT).build();
        } catch (Exception e) {
            log.error("代理服务严重错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 构建目标服务URI
     *
     * @param request HTTP请求对象
     * @return 完整的目标服务URI
     */
    private URI buildTargetUri(HttpServletRequest request) {
        // 获取请求URI并去除上下文路径
        String requestUri = request.getRequestURI();
        String path = StringUtils.trimLeadingCharacter(requestUri.substring(contextPath.length()), '/');

        // 去除代理前缀/uts
        path = path.replaceFirst("^uts/", "");

        // 构建URI并处理特殊符号（如../）
        return UriComponentsBuilder.fromHttpUrl(utsBaseUrl + path)
                .query(request.getQueryString())  // 保留原始查询参数
                .build()
                .normalize()  // 路径标准化（处理./和../）
                .toUri();
    }

    /**
     * 构建转发请求头
     *
     * @param request HTTP请求对象
     * @return 过滤后的请求头
     */
    private HttpHeaders buildRequestHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = request.getHeaderNames();

        // 复制非敏感请求头
        Collections.list(headerNames).stream()
                .filter(name -> !isSensitiveHeader(name))
                .forEach(name -> headers.add(name, request.getHeader(name)));

        // 记录客户端IP到X-Forwarded-For
        String clientIp = request.getRemoteAddr();
        String existingXff = headers.getFirst("X-Forwarded-For");
        headers.set("X-Forwarded-For", existingXff != null ?
                existingXff + ", " + clientIp :  // 追加IP
                clientIp);  // 初始IP

        return headers;
    }

    /**
     * 处理响应头中的Location重定向
     *
     * @param request  原始请求对象
     * @param response 代理响应
     * @return 修改后的响应实体
     */
    private ResponseEntity<byte[]> processResponseHeaders(HttpServletRequest request, ResponseEntity<byte[]> response) {
        // 无Location头直接返回
        if (!response.getHeaders().containsKey(HttpHeaders.LOCATION)) {
            return response;
        }

        // 创建新响应头（排除原始Location）
        HttpHeaders newHeaders = new HttpHeaders();
        response.getHeaders().forEach((key, values) -> {
            if (!HttpHeaders.LOCATION.equalsIgnoreCase(key)) {
                newHeaders.addAll(key, values);
            }
        });

        // 处理Location头
        String location = response.getHeaders().getFirst(HttpHeaders.LOCATION);
        if (location != null) {
            try {
                URI locationUri = URI.create(location);
                if (!locationUri.isAbsolute()) {
                    // 相对路径转换为绝对路径
                    URI requestUri = URI.create(request.getRequestURL().toString());
                    String newLocation = buildProxyLocation(requestUri, locationUri);
                    newHeaders.set(HttpHeaders.LOCATION, newLocation);
                } else if (locationUri.toString().startsWith(utsBaseUrl)) {
                    // 替换后端绝对路径为代理路径
                    String proxyPath = contextPath + "/uts" + locationUri.getPath().substring(utsBaseUrl.length());
                    newHeaders.set(HttpHeaders.LOCATION, proxyPath);
                }
            } catch (IllegalArgumentException e) {
                log.warn("Invalid Location header: {}", location);
            }
        }

        // 返回修改后的响应
        return new ResponseEntity<>(response.getBody(), newHeaders, response.getStatusCode());
    }

    /**
     * 构建代理路径（处理上下文路径和路径规范）
     *
     * @param requestUri  原始请求URI
     * @param locationUri 后端返回的Location URI
     * @return 客户端可访问的代理路径
     */
    private String buildProxyLocation(URI requestUri, URI locationUri) {
        String path = locationUri.getPath();
        // 处理相对路径
        if (path.startsWith("/")) {
            path = contextPath + "/uts" + path;
        } else {
            path = contextPath + "/uts/" + path;
        }
        // 清理多余斜杠（如//）
        path = MULTIPLE_SLASHES.matcher(path).replaceAll("/");
        // 组合完整URL
        return requestUri.getScheme() + "://" + requestUri.getAuthority() + path;
    }

    /**
     * 判断是否为敏感请求头
     *
     * @param headerName 请求头名称
     * @return 是否需过滤
     */
    private boolean isSensitiveHeader(String headerName) {
        return filteredHeaders.stream()
                .anyMatch(filter -> filter.equalsIgnoreCase(headerName));
    }

    /**
     * 智能响应头处理 - 解决K8s环境兼容性问题
     *
     * @param headers 原始响应头
     * @return 处理后的响应头
     */
    private HttpHeaders processResponseHeaders(HttpHeaders headers) {
        HttpHeaders responseHeaders = new HttpHeaders();

        // 定义需要排除的敏感或可能导致问题的响应头
        List<String> excludedHeaders = Arrays.asList(
                "connection", "proxy-connection", "upgrade", // Transport related
                "set-cookie", "set-cookie2", // Cookie related
                "server", "x-powered-by", // Server info (optional)
                "transfer-encoding" // 强制移除chunked编码，统一使用Content-Length
        );

        // 检查Content-Length与Transfer-Encoding冲突
        boolean hasContentLength = headers.containsKey(HttpHeaders.CONTENT_LENGTH);
        boolean hasTransferEncoding = headers.containsKey(HttpHeaders.TRANSFER_ENCODING);

        if (hasContentLength && hasTransferEncoding) {
            log.warn("检测到Content-Length和Transfer-Encoding冲突，统一使用Content-Length以兼容K8s环境");
        }

        if (hasTransferEncoding) {
            log.info("检测到Transfer-Encoding响应，强制转换为Content-Length模式");
        }

        // 复制安全的响应头
        final List<String> finalExcludedHeaders = excludedHeaders;
        headers.forEach((key, value) -> {
            if (value != null && !finalExcludedHeaders.contains(key.toLowerCase())) {
                responseHeaders.addAll(key, value);
            } else {
                log.debug("排除响应头: {} = {}", key, value);
            }
        });

        // 确保关键头部的正确性
        ensureProxyHeaders(responseHeaders);

        return responseHeaders;
    }

    /**
     * 确保代理必要头部的正确性
     *
     * @param headers 响应头
     */
    private void ensureProxyHeaders(HttpHeaders headers) {
        // 添加CORS支持
        headers.set("Access-Control-Allow-Origin", "*");
        headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");

        // 确保Connection头正确设置为close（避免连接复用问题）
        headers.set(HttpHeaders.CONNECTION, "close");

        // 移除可能存在的Transfer-Encoding相关头部
        headers.remove(HttpHeaders.TRANSFER_ENCODING);
        headers.remove("Transfer-Encoding");

        log.debug("已设置代理必要头部: Connection=close, CORS=*, 移除Transfer-Encoding");
    }

    /**
     * 验证响应完整性
     *
     * @param response 响应实体
     * @param headers  处理后的响应头
     */
    private void validateResponseIntegrity(ResponseEntity<byte[]> response, HttpHeaders headers) {
        byte[] body = response.getBody();
        if (body == null) {
            log.debug("响应体为空");
            return;
        }

        // 验证Content-Length与实际body大小的一致性
        if (headers.containsKey(HttpHeaders.CONTENT_LENGTH)) {
            try {
                long declaredLength = Long.parseLong(headers.getFirst(HttpHeaders.CONTENT_LENGTH));
                if (declaredLength != body.length) {
                    log.warn("Content-Length不匹配: 声明长度={}, 实际长度={}", declaredLength, body.length);
                    // 修正Content-Length
                    headers.set(HttpHeaders.CONTENT_LENGTH, String.valueOf(body.length));
                }
            } catch (NumberFormatException e) {
                log.warn("无效的Content-Length头: {}", headers.getFirst(HttpHeaders.CONTENT_LENGTH));
                headers.remove(HttpHeaders.CONTENT_LENGTH);
            }
        }

        log.debug("响应完整性验证: body大小={}, 有Content-Length={}, 有Transfer-Encoding={}",
                body.length,
                headers.containsKey(HttpHeaders.CONTENT_LENGTH),
                headers.containsKey(HttpHeaders.TRANSFER_ENCODING));
    }
}