package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDirrulePojo;
import inks.sa.common.core.domain.SaDirruleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 目录规则配置表(SaDirrule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 14:21:06
 */
@Mapper
public interface SaDirruleMapper {


    SaDirrulePojo getEntity(@Param("key") String key);

    List<SaDirrulePojo> getPageList(QueryParam queryParam);

    int insert(SaDirruleEntity saDirruleEntity);

    int update(SaDirruleEntity saDirruleEntity);

    int delete(@Param("key") String key);

    List<SaDirrulePojo> getList();
}

