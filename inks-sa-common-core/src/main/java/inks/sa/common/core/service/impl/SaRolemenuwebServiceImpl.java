package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaRolemenuwebEntity;
import inks.sa.common.core.domain.pojo.SaRolemenuwebPojo;
import inks.sa.common.core.mapper.SaRolemenuwebMapper;
import inks.sa.common.core.service.SaRolemenuwebService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 角色菜单Web(SaRolemenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Service("saRolemenuwebService")
public class SaRolemenuwebServiceImpl implements SaRolemenuwebService {
    @Resource
    private SaRolemenuwebMapper saRolemenuwebMapper;

    @Override
    public SaRolemenuwebPojo getEntity(String key) {
        return this.saRolemenuwebMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaRolemenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRolemenuwebPojo> lst = saRolemenuwebMapper.getPageList(queryParam);
            PageInfo<SaRolemenuwebPojo> pageInfo = new PageInfo<SaRolemenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaRolemenuwebPojo insert(SaRolemenuwebPojo saRolemenuwebPojo) {
        //初始化NULL字段
        cleanNull(saRolemenuwebPojo);
        SaRolemenuwebEntity saRolemenuwebEntity = new SaRolemenuwebEntity(); 
        BeanUtils.copyProperties(saRolemenuwebPojo,saRolemenuwebEntity);
        //生成雪花id
          saRolemenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saRolemenuwebEntity.setRevision(1);  //乐观锁
          this.saRolemenuwebMapper.insert(saRolemenuwebEntity);
        return this.getEntity(saRolemenuwebEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saRolemenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRolemenuwebPojo update(SaRolemenuwebPojo saRolemenuwebPojo) {
        SaRolemenuwebEntity saRolemenuwebEntity = new SaRolemenuwebEntity(); 
        BeanUtils.copyProperties(saRolemenuwebPojo,saRolemenuwebEntity);
        this.saRolemenuwebMapper.update(saRolemenuwebEntity);
        return this.getEntity(saRolemenuwebEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saRolemenuwebMapper.delete(key) ;
    }

    @Override
    public Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String realname = loginUser.getRealname();
        String userid = loginUser.getUserid();
        int affectedRows = 0;
        if (CollectionUtils.isNotEmpty(deleteNavids)) {
            Integer i = this.saRolemenuwebMapper.batchDelete(roleid, deleteNavids);
            affectedRows += i==null?0:i;
        }

        if (CollectionUtils.isNotEmpty(createNavids)) {
            //insert into PiRoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
            List<SaRolemenuwebPojo> rolemenuwebPojoList = new ArrayList<>();
            for (String navid : createNavids) {
                SaRolemenuwebPojo saRolemenuwebPojo = new SaRolemenuwebPojo();
                saRolemenuwebPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                saRolemenuwebPojo.setRoleid(roleid);
                saRolemenuwebPojo.setNavid(navid);
                // pirolemenuwebPojo.setRownum(createNavids.indexOf(navid) + 1);
                saRolemenuwebPojo.setCreateby(realname); // 示例：设置创建者
                saRolemenuwebPojo.setCreatebyid(userid); // 示例：设置创建者ID
                saRolemenuwebPojo.setLister(realname); // 示例：设置列出者
                saRolemenuwebPojo.setListerid(userid); // 示例：设置列出者ID
//                saRolemenuwebPojo.setTenantid(tid); // 示例：设置租户ID
                cleanNull(saRolemenuwebPojo);
                rolemenuwebPojoList.add(saRolemenuwebPojo);
            }

            Integer i = this.saRolemenuwebMapper.batchInsert(rolemenuwebPojoList);
            affectedRows += i==null?0:i;
        }
        return affectedRows;
    }

    private static void cleanNull(SaRolemenuwebPojo saRolemenuwebPojo) {
        if(saRolemenuwebPojo.getRoleid()==null) saRolemenuwebPojo.setRoleid("");
        if(saRolemenuwebPojo.getNavid()==null) saRolemenuwebPojo.setNavid("");
        if(saRolemenuwebPojo.getRownum()==null) saRolemenuwebPojo.setRownum(0);
        if(saRolemenuwebPojo.getCreateby()==null) saRolemenuwebPojo.setCreateby("");
        if(saRolemenuwebPojo.getCreatebyid()==null) saRolemenuwebPojo.setCreatebyid("");
        if(saRolemenuwebPojo.getCreatedate()==null) saRolemenuwebPojo.setCreatedate(new Date());
        if(saRolemenuwebPojo.getLister()==null) saRolemenuwebPojo.setLister("");
        if(saRolemenuwebPojo.getListerid()==null) saRolemenuwebPojo.setListerid("");
        if(saRolemenuwebPojo.getModifydate()==null) saRolemenuwebPojo.setModifydate(new Date());
        if(saRolemenuwebPojo.getTenantid()==null) saRolemenuwebPojo.setTenantid("");
        if(saRolemenuwebPojo.getTenantname()==null) saRolemenuwebPojo.setTenantname("");
        if(saRolemenuwebPojo.getRevision()==null) saRolemenuwebPojo.setRevision(0);
   }

}
