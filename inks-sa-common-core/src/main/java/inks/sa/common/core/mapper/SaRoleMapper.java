package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaRoleEntity;
import inks.sa.common.core.domain.pojo.SaRolePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色(SaRole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:30
 */
@Mapper
public interface SaRoleMapper {


    SaRolePojo getEntity(@Param("key") String key);


    List<SaRolePojo> getPageList(QueryParam queryParam);

    int insert(SaRoleEntity saRoleEntity);

    int update(SaRoleEntity saRoleEntity);


    int delete(@Param("key") String key);

}

