package inks.sa.common.core.config.oss.service;

import inks.common.core.domain.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 对象存储服务接口
 * 提供统一的文件上传、下载和管理功能，支持多种存储实现（Minio、阿里云OSS等）
 *
 * @since 20250604
 */
public interface OssService {
    /**
     * 构建文件信息，用于上传后的返回体
     *
     * @param file    文件对象
     * @param dirname 存储目录名
     * @return 文件信息对象
     */
    FileInfo of(MultipartFile file, String dirname, String module);

    /**
     * 通用文件上传方法
     *
     * @param multipartFile 上传的文件对象
     * @param objectname    对象名称/存储路径
     * @return 文件信息对象，包含上传后的访问URL等信息
     */
    FileInfo upload(MultipartFile multipartFile, String objectname, String module);

    /**
     * 根据本地文件路径上传文件
     *
     * @param filePath   本地文件路径
     * @param objectName 对象存储中的目标路径/名称
     * @return 文件信息对象
     */
    FileInfo putFile(String filePath, String objectName, String module);

    /**
     * 根据对象路径删除文件
     *
     * @param bucketName 桶名称
     * @param objectname 对象名称/路径
     * @return 是否删除成功
     */
    boolean remove(String bucketName, String objectname);

    /**
     * 直接上传输入流到存储服务
     *
     * @param inputStream 输入流
     * @param bucketName  桶名称
     * @param objectName  对象名称/路径
     * @param size        文件大小
     * @param contentType 内容类型
     * @return 文件信息对象
     */
    FileInfo uploadStream(InputStream inputStream, String bucketName, String objectName, long size, String contentType,String module);


    //---------------------------------------------------以下是未使用接口：-------------------------------------------

    /**
     * 上传文件到指定桶和目录
     *
     * @param file     上传的文件对象
     * @param bucket   桶名称
     * @param dir      存储目录
     * @param filename 文件名
     * @return 文件信息对象
     */
    FileInfo putFile(MultipartFile file, String bucket, String dir, String filename, String module);

    /**
     * 动态更新存储配置
     * 在应用运行时修改OSS服务连接信息
     *
     * @param endpoint        OSS服务端点
     * @param accessKeyId     访问密钥ID
     * @param accessKeySecret 访问密钥密文
     */
    void updateStorage(String endpoint, String accessKeyId, String accessKeySecret);
}
