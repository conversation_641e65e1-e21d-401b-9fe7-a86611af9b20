package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaConfigPojo;

import java.util.Map;

/**
 * (SaConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-30 16:36:58
 */
public interface SaConfigService {


    SaConfigPojo getEntity(String key);

    PageInfo<SaConfigPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saConfigPojo 实例对象
     * @return 实例对象
     */
    SaConfigPojo insert(SaConfigPojo saConfigPojo);

    /**
     * 修改数据
     *
     * @param saConfigpojo 实例对象
     * @return 实例对象
     */
    SaConfigPojo update(SaConfigPojo saConfigpojo);

    int delete(String key);

    SaConfigPojo getEntityByCfgKey(String cfgkey);

    String getConfigValue(String key, String userid);

    SaConfigPojo getEntityByKeyUser(String key, String userid);

    Map<String, String> getConfigAll();
}
