package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 登录日志(SaLoginlog)实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 10:10:27
 */
@Data
public class SaLoginlogEntity implements Serializable {
    private static final long serialVersionUID = -23409487855804787L;
     // id
    private String id;
     // 用户ID
    private String userid;
     // 登录号
    private String username;
     // 中文名
    private String realname;
     // 主机IP
    private String ipaddr;
     // 主机地址
    private String loginlocation;
     // 浏览器名称
    private String browsername;
     // 操作系统
    private String hostsystem;
     // 登录/登出
    private String direction;
     // 登录状态0成功 1失败
    private Integer loginstatus;
     // 操作信息
    private String loginmsg;
     // 访问时间
    private Date logintime;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;



}

