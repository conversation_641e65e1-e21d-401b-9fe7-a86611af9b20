package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaMenuwebPojo;

import java.util.List;

/**
 * 后台导航(SaMenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
public interface SaMenuwebService {


    SaMenuwebPojo getEntity(String key);

    PageInfo<SaMenuwebPojo> getPageList(QueryParam queryParam);

    SaMenuwebPojo insert(SaMenuwebPojo saMenuwebPojo);

    SaMenuwebPojo update(SaMenuwebPojo saMenuwebpojo);

    int delete(String key);

    List<SaMenuwebPojo> getListByPid(String key);

    List<SaMenuwebPojo> getListAll();

    List<SaMenuwebPojo> getListByNavids(List<String> navids);
}
