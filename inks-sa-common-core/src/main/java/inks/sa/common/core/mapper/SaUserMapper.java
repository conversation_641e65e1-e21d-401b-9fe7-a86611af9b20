package inks.sa.common.core.mapper;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaUserEntity;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户(SaUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
@Mapper
public interface SaUserMapper {


    SaUserPojo getEntity(@Param("key") String key);


    List<SaUserPojo> getPageList(QueryParam queryParam);

    int insert(SaUserEntity saUserEntity);

    int update(SaUserEntity saUserEntity);


    int delete(@Param("key") String key, @Param("tid") String tid);

    SaUserPojo getEntityByOpenid(@Param("openid") String openid);

    SaUserPojo getUserInfo(@Param("id") String userid);

    SaUserPojo getEntityByUNameAndPass(@Param("username") String username, @Param("password") String encryptPass);


    SaUserPojo checkPasswordByUserid(@Param("userid") String userid, @Param("password") String encrypt);

    int countUser();

    List<String> getUserIds();

    int getAdminMarkByUserid(String userid);


    List<String> checkUseridUsed(@Param("userid") String userid);

    int updateAdminPassword(String encryptPassword);

    int checkUsername(@Param("username") String username, @Param("id") String id);

    SaUserPojo getEntityByDingUserid(String dingUserid);

    SaUserPojo getEntityByJustAuth(String authtype, String authuuid);

    LoginUser getLoginUserByJustAuth(String authtype, String authuuid);

    List<SaUserPojo> getUserInfosByUserids(@Param("useridList") List<String> useridList);
}

