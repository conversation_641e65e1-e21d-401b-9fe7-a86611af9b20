package inks.sa.common.core.config;

import inks.sa.common.core.utils.PrintColor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;


/*用于yml中通过${}获取这里设置的值.
此处向System.setProperty("database.name", dbName)向系统参数设置数据库名.
则可在其他地方读取:
   在yml中读取dbname: ${database.name}
   在其他地方通过System.getProperty("database.name")获取值,*/
@Configuration
public class YmlConfig {

    @Value("${spring.datasource.url:}")
    private String dbUrl;

    @Bean
    public static PropertySourcesPlaceholderConfigurer placeholderConfigurer() {
        return new PropertySourcesPlaceholderConfigurer();
    }


    // 获取dbUrl中的数据库名字
    @Bean
    public String databaseName() {
        String databaseName = "";
        // 获取数据库名字 格式为********************************************************* 或 **********************************************
        // 优化获取数据库名字，如果有？就截取？和前一个/之间的内容; 否则截取最后一个/之后的内容
        if (dbUrl.contains("?")) {
            int indexQuestionMark = dbUrl.indexOf('?');
            int indexLastSlash = dbUrl.lastIndexOf('/', indexQuestionMark);
            if (indexLastSlash != -1 && indexQuestionMark != -1) {
                databaseName = dbUrl.substring(indexLastSlash + 1, indexQuestionMark);
            }
        } else {
            int indexLastSlash = dbUrl.lastIndexOf('/');
            if (indexLastSlash != -1) {
                databaseName = dbUrl.substring(indexLastSlash + 1);
            }
        }
        System.setProperty("database.name", databaseName);
        PrintColor.red("System.setProperty: database.name=" + databaseName);
        return databaseName;//inkspms
    }
}
