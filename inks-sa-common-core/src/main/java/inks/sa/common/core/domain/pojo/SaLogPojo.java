package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 通用日志(SaLog)实体类
 *
 * <AUTHOR>
 * @since 2025-03-17 16:22:11
 */
@Data
public class SaLogPojo implements Serializable {
    private static final long serialVersionUID = -92533987001193916L;
     // id
    @Excel(name = "id") 
    private String id;
     // 日志类型
    @Excel(name = "日志类型") 
    private String logtype;
     // 日志级别（INFO/WARNING/ERROR/DEBUG等）
    @Excel(name = "日志级别（INFO/WARNING/ERROR/DEBUG等）") 
    private String loglevel;
     // 日志内容
    @Excel(name = "日志内容") 
    private String message;
     // 请求的URL地址
    @Excel(name = "请求的URL地址") 
    private String requesturl;
     // 客户端IP地址
    @Excel(name = "客户端IP地址") 
    private String ipaddress;
     // 所属模块
    @Excel(name = "所属模块") 
    private String module;
     // 操作类型（CREATE/UPDATE/DELETE/QUERY）
    @Excel(name = "操作类型（CREATE/UPDATE/DELETE/QUERY）") 
    private String operationtype;
     // 创建时间
    @Excel(name = "创建时间") 
    private Date createdate;
     // 操作用户ID
    @Excel(name = "操作用户ID") 
    private String userid;
     // 操作用户名
    @Excel(name = "操作用户名") 
    private String realname;
     // 客户端User-Agent
    @Excel(name = "客户端User-Agent") 
    private String useragent;
     // HTTP请求方法，如GET、POST
    @Excel(name = "HTTP请求方法，如GET、POST") 
    private String httpmethod;
     // 关联资源类型
    @Excel(name = "关联资源类型") 
    private String resourcetype;
     // 关联资源ID
    @Excel(name = "关联资源ID") 
    private String resourceid;
     // 操作结果（SUCCESS/FAILURE）
    @Excel(name = "操作结果（SUCCESS/FAILURE）") 
    private String result;
     // 错误信息（当Result为FAILURE时）
    @Excel(name = "错误信息（当Result为FAILURE时）") 
    private String errormessage;
     // 扩展字段，存储JSON格式的自定义数据
    @Excel(name = "扩展字段，存储JSON格式的自定义数据") 
    private String customdata;
     // 租户ID
    @Excel(name = "租户ID") 
    private String tenantid;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

