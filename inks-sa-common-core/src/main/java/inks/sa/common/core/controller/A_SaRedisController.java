package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaRedisPojo;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MySQL暂替Redis(Sa_Redis)表控制层
 *
 * <AUTHOR>
 * @since 2023-11-16 15:13:00
 */
@RestController
@RequestMapping("SaRedis")
@Api(tags = "通用:MySQL替代Redis")
public class A_SaRedisController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaRedisController.class);
    @Resource
    private SaRedisService saRedisService;

    @Scheduled(cron = "0 0 23 * * ?") //每天23点执行
    @ApiOperation(value = "每天23点清除Sa_Redis表的过期key sql: DELETE FROM Sa_Redis WHERE UNIX_TIMESTAMP(NOW()) * 1000 > expiretime AND expiretime != -1", notes = "", produces = "application/json")
    @RequestMapping(value = "/cleanSa_Redis", method = RequestMethod.GET)
    public R<String> cleanSa_Redis() {
        int i = saRedisService.cleanSa_Redis();
        return R.ok("删除了" + i + "条过期数据");
    }

    @ApiOperation(value = " 存储redis键值对到MySQL表,有则覆盖,无则新增(有效期单位为:秒,传-1表示永不过期)", notes = "", produces = "application/json")
    @RequestMapping(value = "/setKeyValue", method = RequestMethod.GET)
    public void setKeyValue(String redisKey, Object redisValue, long timeout) {
        saRedisService.setKeyValue(redisKey, redisValue, timeout, TimeUnit.SECONDS);
    }

    @ApiOperation(value = " 存储redis键值对到MySQL表,有则覆盖,无则新增(有效期单位为:秒,传-1表示永不过期),json格式为{\"key\":\"user:1234\",\"value\":{\"name\":\"John Doe\",\"age\":30},\"timeout\":300}", notes = "", produces = "application/json")
    @RequestMapping(value = "/setKeyValue", method = RequestMethod.POST)
    public void setKeyValue(@RequestBody String json) {
        JSONObject jsonObject = JSON.parseObject(json);
        String key = jsonObject.getString("key");
        Object value = jsonObject.get("value");
        long timeout = jsonObject.getLong("timeout");
        saRedisService.setKeyValue(key, value, timeout, TimeUnit.SECONDS);
    }

    @ApiOperation(value = " 存储redis键值对到MySQL表,有则覆盖,无则新增(永不过期)", notes = "", produces = "application/json")
    @RequestMapping(value = "/setKeyValueForever", method = RequestMethod.GET)
    public void setKeyValue(String redisKey, Object redisValue) {
        saRedisService.setKeyValue(redisKey, redisValue, -1L, TimeUnit.SECONDS);
    }

    @ApiOperation(value = " 存储redis键值对到MySQL表,有则覆盖,无则新增(永不过期),json格式为{\"key\":\"user:1234\",\"value\":{\"name\":\"John Doe\",\"age\":30},\"timeout\":300}", notes = "", produces = "application/json")
    @RequestMapping(value = "/setKeyValueForever", method = RequestMethod.POST)
    public void setKeyValueForever(@RequestBody String json) {
        JSONObject jsonObject = JSON.parseObject(json);
        String key = jsonObject.getString("key");
        Object value = jsonObject.get("value");
        long timeout = jsonObject.getLong("timeout");
        saRedisService.setKeyValue(key, value, -1, TimeUnit.SECONDS);
    }

    @ApiOperation(value = " 获取Sa_Redis.RedisValue", notes = "", produces = "application/json")
    @RequestMapping(value = "/getValue", method = RequestMethod.GET)
    public Object getValue(String key) {
//        PrintColor.red("===========================/getValue===================================");
        return saRedisService.getValue(key);
    }

    @GetMapping("/redisGet")
    @ApiOperation(value = " 读取RedisValueByKey,有R包裹", notes = "", produces = "application/json")
    public R<Object> redisGet(String key) {
        try {
            return R.ok(saRedisService.getValue(key));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "getLoginUser From Sa_Redis", notes = "", produces = "application/json")
    @RequestMapping(value = "/getLoginUser", method = RequestMethod.GET)
    public LoginUser getLoginUser() {
        return saRedisService.getLoginUser();
    }


    @ApiOperation(value = " 获取MySQL暂替Redis详细信息", notes = "获取MySQL暂替Redis详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaRedisPojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser();
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saRedisService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Redis.List")
    public R<PageInfo<SaRedisPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Redis.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saRedisService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MySQL暂替Redis", notes = "新增MySQL暂替Redis", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Redis.Add")
    public R<SaRedisPojo> create(@RequestBody String json) {
        try {
            SaRedisPojo saRedisPojo = JSONArray.parseObject(json, SaRedisPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saRedisService.insert(saRedisPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MySQL暂替Redis", notes = "修改MySQL暂替Redis", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Redis.Edit")
    public R<SaRedisPojo> update(@RequestBody String json) {
        try {
            SaRedisPojo saRedisPojo = JSONArray.parseObject(json, SaRedisPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saRedisService.update(saRedisPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MySQL暂替Redis", notes = "删除MySQL暂替Redis", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Redis.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saRedisService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPer(hasPermi = "Sa_Redis.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaRedisPojo saRedisPojo = this.saRedisService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saRedisPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

