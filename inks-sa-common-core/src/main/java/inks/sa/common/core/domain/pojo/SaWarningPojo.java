package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 预警(SaWarning)实体类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Data
public class SaWarningPojo implements Serializable {
    private static final long serialVersionUID = -13625105925622060L;
     // id
    @Excel(name = "id") 
    private String id;
     // 通用分组
    @Excel(name = "通用分组") 
    private String gengroupid;
     // 模块编码
    @Excel(name = "模块编码") 
    private String modulecode;
     // 预警编码
    @Excel(name = "预警编码") 
    private String warncode;
     // 预警名称
    @Excel(name = "预警名称") 
    private String warnname;
     // 预警字段
    @Excel(name = "预警字段") 
    private String warnfield;
     // 服务编码
    @Excel(name = "服务编码") 
    private String svccode;
     // 预警接口
    @Excel(name = "预警接口") 
    private String warnapi;
     // web文件
    @Excel(name = "web文件") 
    private String webpath;
     // Css图标
    @Excel(name = "Css图标") 
    private String imagecss;
     // 标签文本
    @Excel(name = "标签文本") 
    private String tagtitle;
     // 许可编码
    @Excel(name = "许可编码") 
    private String permcode;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 有效性1
    @Excel(name = "有效性1") 
    private Integer enabledmark;
     // 摘要
    @Excel(name = "摘要") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

