package inks.sa.common.core.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 登录日志(SaLoginlog)实体类
 *
 * <AUTHOR>
 * @since 2025-05-24 10:10:27
 */
@Data
public class SaLoginlogPojo implements Serializable {
    private static final long serialVersionUID = -61606284343268816L;
     // id
    @Excel(name = "id") 
    private String id;
     // 用户ID
    @Excel(name = "用户ID") 
    private String userid;
     // 登录号
    @Excel(name = "登录号") 
    private String username;
     // 中文名
    @Excel(name = "中文名") 
    private String realname;
     // 主机IP
    @Excel(name = "主机IP") 
    private String ipaddr;
     // 主机地址
    @Excel(name = "主机地址") 
    private String loginlocation;
     // 浏览器名称
    @Excel(name = "浏览器名称") 
    private String browsername;
     // 操作系统
    @Excel(name = "操作系统") 
    private String hostsystem;
     // 登录/登出
    @Excel(name = "登录/登出") 
    private String direction;
     // 登录状态0成功 1失败
    @Excel(name = "登录状态0成功 1失败") 
    private Integer loginstatus;
     // 操作信息
    @Excel(name = "操作信息") 
    private String loginmsg;
     // 访问时间
    @Excel(name = "访问时间") 
    private Date logintime;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;



}

