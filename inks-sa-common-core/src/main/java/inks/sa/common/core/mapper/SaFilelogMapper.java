package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.domain.SaFilelogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 文件上传/下载日志表(SaFilelog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 14:22:54
 */
@Mapper
public interface SaFilelogMapper {


    SaFilelogPojo getEntity(@Param("key") String key);

    List<SaFilelogPojo> getPageList(QueryParam queryParam);

    int insert(SaFilelogEntity saFilelogEntity);

    int update(SaFilelogEntity saFilelogEntity);

    int delete(@Param("key") String key);
    
}

