package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaPermissionEntity;
import inks.sa.common.core.domain.pojo.SaPermissionPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限关系(SaPermission)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:32
 */
@Mapper
public interface SaPermissionMapper {


    SaPermissionPojo getEntity(@Param("key") String key);


    List<SaPermissionPojo> getPageList(QueryParam queryParam);


    int insert(SaPermissionEntity saPermissionEntity);


    int update(SaPermissionEntity saPermissionEntity);


    int delete(@Param("key") String key);

    List<SaPermissionPojo> getListByRole(String key);

    List<SaPermissionPojo> getUserAllPerm(@Param("key") String key);
}

