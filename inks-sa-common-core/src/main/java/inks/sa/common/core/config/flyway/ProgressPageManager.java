package inks.sa.common.core.config.flyway;

import inks.sa.common.core.utils.PrintColor;

import java.awt.Desktop;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 实时进度页面管理器
 * 负责创建和更新本地HTML进度页面
 */
public class ProgressPageManager {
    
    private final String databaseName;
    private final List<String> logs = new ArrayList<>();
    private int currentProgress = 0;
    private String currentStatus = "准备中...";
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
    private int totalTables = 0;
    private int processedTables = 0;  // 所有表操作计数（包括CREATE、DROP等）
    private int createdTables = 0;    // 仅CREATE TABLE操作计数
    private int actualTables = -1;    // 实际数据库中的表数量（-1表示未查询）
    private long startTime = System.currentTimeMillis();
    private ProgressHttpServer httpServer;

    // 数据库连接信息（用于查询实际表数量）
    private String dbUrl;
    private String dbUsername;
    private String dbPassword;

    /**
     * 格式化时间显示
     */
    private String formatTime(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return minutes + "分" + remainingSeconds + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            long remainingSeconds = seconds % 60;
            return hours + "小时" + minutes + "分" + remainingSeconds + "秒";
        }
    }

    /**
     * 查找可用端口
     */
    private int findAvailablePort(int startPort) {
        for (int port = startPort; port <= startPort + 100; port++) {
            try (java.net.ServerSocket socket = new java.net.ServerSocket(port)) {
                return port;
            } catch (java.io.IOException e) {
                // 端口被占用，尝试下一个
            }
        }
        return startPort; // 如果都被占用，返回起始端口
    }
    
    public ProgressPageManager(String databaseName) {
        this.databaseName = databaseName;
    }

    /**
     * 构造函数，包含数据库连接信息
     */
    public ProgressPageManager(String databaseName, String dbUrl, String dbUsername, String dbPassword) {
        this.databaseName = databaseName;
        this.dbUrl = dbUrl;
        this.dbUsername = dbUsername;
        this.dbPassword = dbPassword;
    }
    
    /**
     * 创建并打开进度页面
     */
    public void createAndOpenProgressPage() {
        try {
            // 启动HTTP服务器（恢复为8080端口，初始化完成后会释放）
            int port = findAvailablePort(8080); // 恢复为8080端口
            httpServer = new ProgressHttpServer(port, this);
            httpServer.start();

            // 输出系统信息用于调试
            PrintColor.red("系统环境: " + BrowserLauncher.getSystemInfo());

            // 尝试打开HTTP页面
            String httpUrl = "http://localhost:" + port + "/flyway";
            boolean opened = BrowserLauncher.openUrl(httpUrl);
            if (opened) {
                PrintColor.red("已打开数据库初始化进度页面: " + httpUrl);
            } else {
                PrintColor.red("请手动访问进度页面: " + httpUrl);
            }

        } catch (Exception e) {
            PrintColor.red("创建进度页面失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新进度
     */
    public void updateProgress(String status, int progress) {
        // 添加调试信息
        PrintColor.red("进度更新: " + progress + "% - " + status);

        this.currentStatus = status;
        this.currentProgress = progress;
        addLog(status);
    }
    
    /**
     * 添加日志
     */
    public void addLog(String message) {
        String timestamp = timeFormat.format(new Date());
        logs.add("[" + timestamp + "] " + message);
        PrintColor.red(message); // 同时输出到控制台
    }
    
    /**
     * 设置总表数
     */
    public void setTotalTables(int total) {
        this.totalTables = total;
        if (total > 0) {
            // 只在控制台输出，不添加到HTML日志中
            PrintColor.red("预计需要处理 " + total + " 张数据表");
        }
    }

    /**
     * 添加表处理日志（只显示表处理进度）
     */
    public void addTableLog(String tableName, String operation) {
        processedTables++;

        // 在Flyway上下文中，所有表操作都意味着表正在被处理/创建
        // 包括：创建表、正常操作（Unknown table错误，通常是DROP+CREATE的正常流程）
        createdTables++;

        String message = "处理表: " + tableName + " (" + operation + ") " + createdTables + "/" + totalTables;

        // 只添加表处理日志，不添加其他冗余信息
        String timestamp = timeFormat.format(new Date());
        synchronized (logs) {
            logs.add("[" + timestamp + "] " + message);
        }
        PrintColor.red(message); // 同时输出到控制台

        // 根据CREATE TABLE进度更新总进度，但不更新状态文本
        if (totalTables > 0) {
            int tableProgress = (createdTables * 85) / totalTables; // 表创建占85%的进度(10%-95%)
            int newProgress = Math.min(95, 10 + tableProgress);

            // 添加调试信息
            PrintColor.red("表进度计算: " + createdTables + "/" + totalTables + " = " + newProgress + "%");

            // 只更新进度百分比，不更新状态文本
            this.currentProgress = newProgress;
        }

        // HTTP服务器会实时获取数据，无需文件更新
    }

    /**
     * 标记完成
     */
    public void markComplete(String message, long elapsedTime) {
        this.currentStatus = "初始化完成";
        this.currentProgress = 100;
        addLog(message);
        if (totalTables > 0) {
            addLog("共创建了 " + createdTables + " 张数据表");
        }

        // 查询实际数据库中的表数量
        queryActualTableCount();

        addLog("总耗时: " + elapsedTime + " 毫秒");

        // 30秒后停止HTTP服务器
        new Thread(() -> {
            try {
                Thread.sleep(30000);
                stopHttpServer();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * 停止HTTP服务器，释放端口供Spring Boot使用
     */
    public void stopHttpServer() {
        if (httpServer != null) {
            try {
                httpServer.stop();
                PrintColor.red("Flyway进度服务器已停止，8080端口已释放给Spring Boot应用");
            } catch (Exception e) {
                PrintColor.red("停止Flyway进度服务器时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 查询实际数据库中的表数量
     */
    public void queryActualTableCount() {
        if (dbUrl == null || dbUsername == null || dbPassword == null) {
            addLog("无法查询实际表数量：缺少数据库连接信息");
            return;
        }

        try {
            addLog("正在查询数据库中的实际表数量...");

            // 建立数据库连接
            Connection connection = DriverManager.getConnection(dbUrl, dbUsername, dbPassword);
            Statement statement = connection.createStatement();

            // 查询当前数据库中的表数量（排除系统表和视图）
            String sql = "SELECT COUNT(*) as table_count FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_type = 'BASE TABLE'";

            ResultSet resultSet = statement.executeQuery(sql);

            if (resultSet.next()) {
                actualTables = resultSet.getInt("table_count");
                addLog("经过数据库查询，本次实际生成了 " + actualTables + " 张表");
                PrintColor.red("数据库查询结果：实际生成了 " + actualTables + " 张表");
            }

            resultSet.close();
            statement.close();
            connection.close();

        } catch (Exception e) {
            addLog("查询实际表数量失败: " + e.getMessage());
            PrintColor.red("查询实际表数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取实际表数量
     */
    public int getActualTables() {
        return actualTables;
    }

    /**
     * 标记错误
     */
    public void markError(String errorMessage) {
        this.currentStatus = "初始化失败";
        addLog("错误: " + errorMessage);
    }
    

    
    /**
     * 生成进度页面HTML
     */
    public String generateProgressHtml() {
        StringBuilder logsHtml = new StringBuilder();

        // 计算执行时间
        long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
        String timeDisplay = formatTime(elapsedSeconds);

        // 安全地处理日志列表，避免并发修改异常
        try {
            synchronized (logs) {
                for (String log : logs) {
                    if (log != null && !log.trim().isEmpty()) {
                        // 转义HTML特殊字符
                        String escapedLog = log.replace("&", "&amp;")
                                              .replace("<", "&lt;")
                                              .replace(">", "&gt;")
                                              .replace("\"", "&quot;");
                        logsHtml.append("<div class='log-entry'>").append(escapedLog).append("</div>");
                    }
                }
            }
        } catch (Exception e) {
            logsHtml.append("<div class='log-entry'>日志显示异常: ").append(e.getMessage()).append("</div>");
        }
        
        return "<!DOCTYPE html>\n" +
                "<html lang='zh-CN'>\n" +
                "<head>\n" +
                "    <meta charset='UTF-8'>\n" +
                "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n" +
                "    <title>数据库初始化进度 - " + databaseName + "</title>\n" +
                "    <style>\n" +
                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; padding: 20px; }\n" +
                "        .container { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; max-width: 800px; margin: 0 auto; }\n" +
                "        .title { color: #333; font-size: 24px; margin-bottom: 20px; text-align: center; }\n" +
                "        .time-display { font-size: 16px; color: #2196F3; font-weight: normal; margin-left: 10px; }\n" +
                "        .status { font-size: 18px; color: #666; margin-bottom: 15px; text-align: center; }\n" +
                "        .table-progress { font-size: 16px; color: #2196F3; margin-bottom: 10px; text-align: center; font-weight: bold; }\n" +
                "        .actual-tables { font-size: 14px; color: #28a745; margin-bottom: 10px; text-align: center; font-weight: bold; }\n" +
                "        .progress-container { background: #f0f0f0; border-radius: 25px; height: 20px; margin: 20px 0; overflow: hidden; }\n" +
                "        .progress-bar { background: linear-gradient(90deg, #4CAF50, #45a049); height: 100%; width: " + currentProgress + "%; border-radius: 25px; transition: width 0.5s ease; }\n" +
                "        .progress-text { text-align: center; margin: 10px 0; font-weight: bold; color: #333; }\n" +
                "        .log-container { margin-top: 20px; }\n" +
                "        .log-title { font-size: 16px; color: #333; margin-bottom: 10px; }\n" +
                "        .log-box { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; height: 350px; overflow-y: auto; padding: 15px; font-family: 'Consolas', 'Monaco', monospace; font-size: 13px; line-height: 1.4; }\n" +
                "        .log-entry { margin-bottom: 3px; color: #333; padding: 2px 0; }\n" +
                "        .log-entry:nth-child(even) { background-color: rgba(0,0,0,0.02); }\n" +
                "        .spinner { border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; width: 20px; height: 20px; animation: spin 1s linear infinite; display: inline-block; margin-right: 10px; }\n" +
                "        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }\n" +
                "        .refresh-info { text-align: center; color: #999; font-size: 12px; margin-top: 15px; }\n" +
                "    </style>\n" +
                "    <script>\n" +
                "        // 根据进度调整刷新间隔，避免过于频繁\n" +
                "        let refreshInterval;\n" +
                "        if (" + currentProgress + " >= 95) {\n" +
                "            refreshInterval = 5000; // 接近完成时，5秒刷新一次\n" +
                "        } else if (" + currentProgress + " >= 70) {\n" +
                "            refreshInterval = 2000; // 执行阶段，2秒刷新一次\n" +
                "        } else {\n" +
                "            refreshInterval = 3000; // 其他阶段，3秒刷新一次\n" +
                "        }\n" +
                "        \n" +
                "        // 自动滚动日志到底部\n" +
                "        window.onload = function() {\n" +
                "            const logBox = document.querySelector('.log-box');\n" +
                "            if (logBox) {\n" +
                "                logBox.scrollTop = logBox.scrollHeight;\n" +
                "            }\n" +
                "            \n" +
                "            // 显示最后更新时间\n" +
                "            const now = new Date();\n" +
                "            const timeStr = now.getHours().toString().padStart(2, '0') + ':' + \n" +
                "                           now.getMinutes().toString().padStart(2, '0') + ':' + \n" +
                "                           now.getSeconds().toString().padStart(2, '0');\n" +
                "            document.title = '数据库初始化进度 - ' + timeStr;\n" +
                "        };\n" +
                "        \n" +
                "        // 设置自动刷新，但避免过于频繁\n" +
                "        if (" + currentProgress + " < 100) {\n" +
                "            setTimeout(() => {\n" +
                "                try {\n" +
                "                    location.reload();\n" +
                "                } catch (e) {\n" +
                "                    console.error('页面刷新失败:', e);\n" +
                "                    // 如果刷新失败，延迟再试\n" +
                "                    setTimeout(() => location.reload(), 2000);\n" +
                "                }\n" +
                "            }, refreshInterval);\n" +
                "        }\n" +
                "    </script>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class='container'>\n" +
                "        <h1 class='title'>\n" +
                (currentProgress < 100 ? "            <span class='spinner'></span>\n" : "") +
                "            数据库初始化进度 <span class='time-display'>" + timeDisplay + "</span>\n" +
                "        </h1>\n" +
                "        <div class='status'>" + currentStatus + "</div>\n" +
                (totalTables > 0 ?
                "        <div class='table-progress'>已处理 " + createdTables + " / " + totalTables + " 张数据表</div>\n" : "") +
                (actualTables > 0 ?
                "        <div class='actual-tables'>✅ 经过数据库查询，本次实际生成了 " + actualTables + " 张表</div>\n" : "") +
                "        <div class='progress-container'>\n" +
                "            <div class='progress-bar'></div>\n" +
                "        </div>\n" +
                "        <div class='progress-text'>" + currentProgress + "%</div>\n" +
                "        <div class='log-container'>\n" +
                "            <div class='log-title'>执行日志</div>\n" +
                "            <div class='log-box'>\n" +
                logsHtml.toString() +
                "            </div>\n" +
                "        </div>\n" +
                (currentProgress < 100 ? "        <div class='refresh-info'>页面每2秒自动刷新以获取最新进度</div>\n" : "") +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
    

    

}
