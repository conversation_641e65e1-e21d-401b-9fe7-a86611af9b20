package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 第三方登录(SaJustauth)实体类
 *
 * <AUTHOR>
 * @since 2024-09-18 14:01:59
 */
@Data
public class SaJustauthEntity implements Serializable {
    private static final long serialVersionUID = -70007135929547449L;
     // ID
    private String id;
     // 用户id
    private String userid;
     // 登录名
    private String username;
     // 姓名
    private String realname;
     // 昵称
    private String nickname;
     // ding/wxe/openid
    private String authtype;
     // uuid
    private String authuuid;
     // Unionid
    private String unionid;
     // avatar
    private String authavatar;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

