package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaAuthcodePojo;

/**
 * 授权码(SaAuthcode)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-15 09:38:43
 */
public interface SaAuthcodeService {


    SaAuthcodePojo getEntity(String key);

    PageInfo<SaAuthcodePojo> getPageList(QueryParam queryParam);

    SaAuthcodePojo insert(SaAuthcodePojo saAuthcodePojo);

    SaAuthcodePojo update(SaAuthcodePojo saAuthcodepojo);

    int delete(String key);
}
