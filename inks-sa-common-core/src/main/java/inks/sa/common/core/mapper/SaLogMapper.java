package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaLogPojo;
import inks.sa.common.core.domain.SaLogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 通用日志(SaLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-17 16:22:11
 */
@Mapper
public interface SaLogMapper {


    SaLogPojo getEntity(@Param("key") String key);

    List<SaLogPojo> getPageList(QueryParam queryParam);

    int insert(SaLogEntity saLogEntity);

    int update(SaLogEntity saLogEntity);

    int delete(@Param("key") String key);
    
}

