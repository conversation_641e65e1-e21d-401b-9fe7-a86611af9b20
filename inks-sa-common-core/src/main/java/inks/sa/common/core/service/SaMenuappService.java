package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaMenuappPojo;

import java.util.List;

/**
 * APP导航(SaMenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
public interface SaMenuappService {


    SaMenuappPojo getEntity(String key);

    PageInfo<SaMenuappPojo> getPageList(QueryParam queryParam);

    SaMenuappPojo insert(SaMenuappPojo saMenuappPojo);

    SaMenuappPojo update(SaMenuappPojo saMenuapppojo);

    int delete(String key);

    List<SaMenuappPojo> getListByPid(String key);

    List<SaMenuappPojo> getListAll();

    List<SaMenuappPojo> getListByNavids(List<String> navids);
}
