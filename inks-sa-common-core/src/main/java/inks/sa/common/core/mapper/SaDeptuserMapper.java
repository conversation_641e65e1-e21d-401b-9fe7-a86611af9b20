package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaDeptuserEntity;
import inks.sa.common.core.domain.pojo.SaDeptuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组织用户表(SaDeptuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-05 13:00:44
 */
@Mapper
public interface SaDeptuserMapper {


    SaDeptuserPojo getEntity(@Param("key") String key);

    List<SaDeptuserPojo> getPageList(QueryParam queryParam);

    int insert(SaDeptuserEntity saDeptuserEntity);

    int update(SaDeptuserEntity saDeptuserEntity);

    int delete(@Param("key") String key);

    SaDeptuserPojo getEntityByUser(String userid);

    int countByUserid(String userid, String id);

    SaDeptuserPojo getEntityBySuperAdmin();

    List<String> getUseridListInDeptids(@Param("deptids") List<String> deptids);

    String getDeptIDByUserId(String copytouserid);
}

