package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaLoginlogPojo;
import inks.sa.common.core.domain.SaLoginlogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 登录日志(SaLoginlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-24 10:10:27
 */
@Mapper
public interface SaLoginlogMapper {


    SaLoginlogPojo getEntity(@Param("key") String key);

    List<SaLoginlogPojo> getPageList(QueryParam queryParam);

    int insert(SaLoginlogEntity saLoginlogEntity);

    int update(SaLoginlogEntity saLoginlogEntity);

    int delete(@Param("key") String key);
    
}

