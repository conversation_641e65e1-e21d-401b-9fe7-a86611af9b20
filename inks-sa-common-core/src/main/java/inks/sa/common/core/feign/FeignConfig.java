package inks.sa.common.core.feign;

import feign.Feign;
import feign.RequestInterceptor;
import feign.Target;
import inks.api.feign.*;
import inks.sa.common.core.utils.PrintColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

@Configuration
@ConditionalOnProperty(name = "inks.svcfeign")
public class FeignConfig {
    private static final Logger log = LoggerFactory.getLogger(FeignConfig.class);


    @Value("${inks.svcfeign:}") // 默认值为空字符串
    private String svcfeign;

    // 因为授权码登录走的是核心包的authFeignService，不是走自定义的，所以此处覆盖核心包authFeignService的authUrl地址，并需设置解码器
    @Bean
    //@Primary
    public AuthFeignService authFeignService() {
        log.info("覆盖 AuthFeignService，目标地址: {}", authUrl);
        return Feign.builder()
                .contract(new SpringMvcContract()) // 使用 SpringMvcContract
                .target(AuthFeignService.class, authUrl);
    }

    @Bean
    //@Primary
    public SystemFeignService systemFeignService() {
        log.info("覆盖 SystemFeignService，目标地址: {}", systemUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(SystemFeignService.class, systemUrl);
    }

    @Bean
    public UtilsFeignService utilsFeignService() {
        log.info("覆盖 UtilsFeignService，目标地址: {}", utilsUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(UtilsFeignService.class, utilsUrl);
    }

    @Bean
    public QmsFeignService qmsFeignService() {
        log.info("覆盖 QmsFeignService，目标地址: {}", qmsUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(QmsFeignService.class, qmsUrl);
    }

    @Bean
    public FileFeignService fileFeignService() {
        log.info("覆盖 FileFeignService，目标地址: {}", fileUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(FileFeignService.class, fileUrl);
    }

    @Bean
    public GoodsFeignService goodsFeignService() {
        log.info("覆盖 GoodsFeignService，目标地址: {}", goodsUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(GoodsFeignService.class, goodsUrl);
    }

    @Bean
    public ManuFeignService manuFeignService() {
        log.info("覆盖 ManuFeignService，目标地址: {}", manuUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(ManuFeignService.class, manuUrl);
    }

    @Bean
    public StoreFeignService storeFeignService() {
        log.info("覆盖 StoreFeignService，目标地址: {}", storeUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(StoreFeignService.class, storeUrl);
    }

    @Bean
    public JobFeignService jobFeignService() {
        log.info("覆盖 JobFeignService，目标地址: {}", jobUrl);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .target(JobFeignService.class, jobUrl);
    }


    //@Bean
    //public Decoder feignDecoder() {    //    return new CustomResponseDecoder(new ObjectMapper());    //}    @Bean
    public RequestInterceptor requestInterceptor() {
        log.info("进入 requestInterceptor");
        return template -> {
            Target<?> target = template.feignTarget();
            String fullUrl = target.url() + template.url();
            log.info("Feign 请求: {} {}", template.method(), fullUrl);
            log.info("请求头: {}", template.headers());

            // 将请求体从字节数组转换为字符串
            byte[] bodyBytes = template.body();
            if (bodyBytes != null) {
                String body = new String(bodyBytes, StandardCharsets.UTF_8);
                log.info("请求体: {}", body);
            } else {
                log.info("请求体为空");
            }
        };
    }


    private String systemUrl;
    private String utilsUrl;
    private String qmsUrl;
    private String fileUrl;
    private String goodsUrl;
    private String manuUrl;
    private String storeUrl;
    private String authUrl;
    private String jobUrl;


    @PostConstruct
    public void init() {
        systemUrl = svcfeign + "/system";
        utilsUrl = svcfeign + "/utils";
        qmsUrl = svcfeign + "/qms";
        fileUrl = svcfeign + "/file";
        goodsUrl = svcfeign + "/goods";
        manuUrl = svcfeign + "/manu";
        storeUrl = svcfeign + "/store";
        authUrl = svcfeign + "/auth";
        jobUrl = svcfeign + "/job";

        PrintColor.red("FeignConfig inited, systemUrl: " + systemUrl + ", utilsUrl: " + utilsUrl + ", qmsUrl: " + qmsUrl + ", fileUrl: " + fileUrl + ", goodsUrl: " + goodsUrl + ", manuUrl: " + manuUrl + ", storeUrl: " + storeUrl + ", authUrl: " + authUrl + ", jobUrl: " + jobUrl);
    }

    public String getSystemUrl() {
        return systemUrl;
    }

    public String getUtilsUrl() {
        return utilsUrl;
    }

    public String getQmsUrl() {
        return qmsUrl;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public String getGoodsUrl() {
        return goodsUrl;
    }

    public String getManuUrl() {
        return manuUrl;
    }

    public String getStoreUrl() {
        return storeUrl;
    }

    public String getAuthUrl() {
        return authUrl;
    }

    public String getJobUrl() {
        return jobUrl;
    }

}