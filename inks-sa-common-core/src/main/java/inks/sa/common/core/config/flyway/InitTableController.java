package inks.sa.common.core.config.flyway;

import inks.common.core.domain.R;
import inks.sa.common.core.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@Api(tags = "Mysql初始化数据表")
@RequestMapping("/Init")
@ConditionalOnProperty(prefix = "spring.flyway", name = "auto-create-db", havingValue = "true")
@DependsOn("flywayMigrationCompleted") // 确保Flyway已经完成迁移
public class InitTableController {
    @Value("${spring.datasource.url:}")
    private String dbUrl;

    @Value("${spring.datasource.username:}")
    private String dbUsername;

    @Value("${spring.datasource.password:}")
    private String dbPassword;

    @Value("${spring.datasource.driver-class-name:}")
    private String dbDriverClassName;

    @Autowired
    private DataSourceHelper.FlywayMigrationCompleted flywayMigrationCompleted;

    /**
     * Flyway会自动扫描指定目录下的所有SQL文件并按照文件名的顺序执行
     */
    @ApiOperation(value = "初始化数据表(创建admin用户),直接执行classpath:db/migration下SQL文件", notes = "", produces = "application/json")
    @RequestMapping(value = "/initTable", method = RequestMethod.GET)
    public R initTable() {
        long startTime = System.currentTimeMillis();
        try {
            DataSource dataSource = DataSourceBuilder.create()
                    .url(dbUrl)
                    .username(dbUsername)
                    .password(dbPassword)
                    .build();

            // 创建Flyway实例，设置baselineOnMigrate为false，使用clean来清空数据库对象
            Flyway flyway = Flyway.configure().dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .baselineOnMigrate(false)
                    .cleanDisabled(false) // 设置为true以禁用clean（默认为false）
                    .load();

            // 清空数据表
            flyway.clean();

            // 执行迁移 执行migration下SQL文件
            flyway.migrate();

            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            return R.ok("初始化成功,初始化数据表并创建admin用户，耗时：" + elapsedTime + " 毫秒");
        } catch (Exception e) {
            PrintColor.red("初始化数据表失败: " + e.getMessage());
            return R.fail("初始化数据表失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "恢复备份数据库,直接恢复resources/backup下的sql文件", notes = "", produces = "application/json")
    @RequestMapping(value = "/restoreTableInResources", method = RequestMethod.GET)
    public R restoreTableInResources() {
        long startTime = System.currentTimeMillis();
        try {
            DataSource dataSource = DataSourceBuilder.create()
                    .url(dbUrl)
                    .username(dbUsername)
                    .password(dbPassword)
                    .build();

            // 创建Flyway实例，设置baselineOnMigrate为false，使用clean来清空数据库对象
            Flyway flyway = Flyway.configure().dataSource(dataSource)
                    .locations("classpath:backup")
                    .baselineOnMigrate(false)
                    .cleanDisabled(false) // 设置为true以禁用clean（默认为false）
                    .load();

            // 清空数据表
            flyway.clean();

            // 执行迁移 执行backup下SQL文件
            flyway.migrate();

            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            return R.ok("恢复备份数据库成功，耗时：" + elapsedTime + " 毫秒");
        } catch (Exception e) {
            PrintColor.red("恢复备份数据库失败: " + e.getMessage());
            return R.fail("恢复备份数据库失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "传入.sql文件,恢复备份数据库", notes = "", produces = "application/json")
    @RequestMapping(value = "/restoreTable", method = RequestMethod.POST)
    public R restoreTable(@RequestParam("file") MultipartFile sqlFile) throws IOException {
        if (sqlFile.isEmpty()) {
            return R.fail("上传的SQL文件为空");
        }

        long startTime = System.currentTimeMillis();
        File tempFile = null;
        try {
            // 将上传的SQL文件保存到临时目录
            String resourcesPath = System.getProperty("user.dir") + File.separator + "backup";
            PrintColor.red("将SQL文件保存到临时目录: " + resourcesPath);

            // 确保目录存在
            File directory = new File(resourcesPath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 生成当前时间格式为V20240222
            String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
            tempFile = new File(resourcesPath, "V" + date + "__buckup_inkscmr.sql");
            sqlFile.transferTo(tempFile);

            // 创建DataSource
            DataSource dataSource = DataSourceBuilder.create()
                    .url(dbUrl)
                    .username(dbUsername)
                    .password(dbPassword)
                    .build();

            // 创建Flyway实例
            Flyway flyway = Flyway.configure().dataSource(dataSource)
                    .locations("filesystem:" + resourcesPath) // 指定要执行的SQL文件路径
                    .baselineOnMigrate(false)
                    .cleanDisabled(false)
                    .load();

            // 清空数据表
            flyway.clean();
            // 执行Flyway迁移
            flyway.migrate();

            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;

            return R.ok("恢复备份数据库成功，耗时：" + elapsedTime + " 毫秒");
        } catch (Exception e) {
            PrintColor.red("恢复备份数据库失败: " + e.getMessage());
            return R.fail("恢复备份数据库失败: " + e.getMessage());
        } finally {
            // 删除临时生成的备份.sql文件
            if (tempFile != null && tempFile.exists()) {
                delete(tempFile.getAbsoluteFile());
            }
        }
    }

    public static void delete(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    delete(f);
                }
            }
        }

        if (!file.delete()) {
            PrintColor.red("文件删除失败: " + file.getAbsolutePath());
        }
    }
}