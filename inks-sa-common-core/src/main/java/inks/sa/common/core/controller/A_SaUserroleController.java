package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import inks.sa.common.core.domain.pojo.SaRolePojo;
import inks.sa.common.core.domain.pojo.SaUserPojo;
import inks.sa.common.core.domain.pojo.SaUserrolePojo;
import inks.sa.common.core.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户角色关联(Sa_UserRole)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:26
 */
@RestController
@RequestMapping("/SaUserRole")
@Api(tags = "通用:用户角色关联")
public class A_SaUserroleController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaUserroleController.class);
    @Resource
    private SaUserroleService saUserroleService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaRoleService saRoleService;
    @Resource
    private SaUserService saUserService;
    @Resource
    private SaConfigService saConfigService;

    @ApiOperation(value = "根据角色获取关系List", notes = "根据角色获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiUserRole.List")
    public R<List<SaUserrolePojo>> getListByRole(String key) {
        try {
            return R.ok(this.saUserroleService.getListByRole(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取关系List", notes = "根据用户获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiUserRole.List")
    public R<List<SaUserrolePojo>> getListByUser(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saUserroleService.getListByUser(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据角色获取用户Bill", notes = "根据角色获取用户Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByRole", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiUserRole.List")
    public R<SaUserrolePojo> getBillEntityByRole(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaRolePojo pirolePojo = this.saRoleService.getEntity(key);
            SaUserrolePojo saUserrolePojo = new SaUserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(pirolePojo, saUserrolePojo);
            List<SaUserrolePojo> item = this.saUserroleService.getListByRole(key);
            saUserrolePojo.setItem(item);
            return R.ok(saUserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取角色Bill", notes = "根据用户获取角色Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByUser", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiUserRole.List")
    public R<SaUserrolePojo> getBillEntityByUser(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = this.saUserService.getEntity(key);
            SaUserrolePojo saUserrolePojo = new SaUserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(saUserPojo, saUserrolePojo);
            List<SaUserrolePojo> item = this.saUserroleService.getListByUser(key);
            saUserrolePojo.setItem(item);
            return R.ok(saUserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取角色Bill", notes = "根据用户获取角色Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityBySelf", method = RequestMethod.GET)
    public R<SaUserrolePojo> getBillEntityBySelf() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = this.saUserService.getEntity(loginUser.getUserid());
            SaUserrolePojo saUserrolePojo = new SaUserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(saUserPojo, saUserrolePojo);
            List<SaUserrolePojo> item = this.saUserroleService.getListByUser(loginUser.getUserid());
            saUserrolePojo.setItem(item);
            return R.ok(saUserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 根据用户获取权限List
     *
     * @return 权限List
     */
    @ApiOperation(value = "根据用户获取权限List", notes = "根据用户获取权限List", produces = "application/json")
    @RequestMapping(value = "/getPermBySelf", method = RequestMethod.GET)
    public R<List<SaPermcodePojo>> getPermBySelf() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saUserroleService.getPermByUser(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据用户获取权限List,key是token值", notes = "来自/SYSM03B2/updateToken方法", produces = "application/json")
    @RequestMapping(value = "/updateTokenPerm", method = RequestMethod.GET)
    public R<LoginUser> updateTokenPerm(String key) {// key是token值
        try {
            LoginUser loginUser;
            if (StringUtils.isNotBlank(key)) {
                loginUser = this.saRedisService.getLoginUserFromToken(key);
            } else {
                loginUser = this.saRedisService.getLoginUser();
            }
            // 先在LoginUser中加入权限和参数    Controller层/SaUserRole/updateToken额外操作会覆盖原Sa_Redis中的token
            saUserroleService.updateTokenPerm(loginUser);
            // 再覆盖原Sa_Redis中的token
            saRedisService.setCacheObject(MyConstant.LOGIN_TOKEN_KEY + key, loginUser, 43200);
            return R.ok(loginUser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    ------------------------------普通生成方法-------------------------------

    @ApiOperation(value = " 获取用户角色关联详细信息", notes = "获取用户角色关联详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_UserRole.List")
    public R<SaUserrolePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saUserroleService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_UserRole.List")
    public R<PageInfo<SaUserrolePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_UserRole.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saUserroleService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增用户角色关联", notes = "新增用户角色关联", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_UserRole.Add")
    public R<SaUserrolePojo> create(@RequestBody String json) {
        try {
            SaUserrolePojo saUserrolePojo = JSONArray.parseObject(json, SaUserrolePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saUserrolePojo.setCreateby(loginUser.getRealName());   // 创建者
            saUserrolePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saUserrolePojo.setCreatedate(new Date());   // 创建时间
            saUserrolePojo.setLister(loginUser.getRealname());   // 制表
            saUserrolePojo.setListerid(loginUser.getUserid());    // 制表id
            saUserrolePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saUserroleService.insert(saUserrolePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改用户角色关联", notes = "修改用户角色关联", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_UserRole.Edit")
    public R<SaUserrolePojo> update(@RequestBody String json) {
        try {
            SaUserrolePojo saUserrolePojo = JSONArray.parseObject(json, SaUserrolePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saUserrolePojo.setLister(loginUser.getRealname());   // 制表
            saUserrolePojo.setListerid(loginUser.getUserid());    // 制表id
            saUserrolePojo.setModifydate(new Date());   //修改时间
            //            saUserrolePojo.setAssessor(""); // 审核员
            //            saUserrolePojo.setAssessorid(""); // 审核员id
            //            saUserrolePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saUserroleService.update(saUserrolePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除用户角色关联", notes = "删除用户角色关联", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_UserRole.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saUserroleService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_UserRole.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaUserrolePojo saUserrolePojo = this.saUserroleService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saUserrolePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

