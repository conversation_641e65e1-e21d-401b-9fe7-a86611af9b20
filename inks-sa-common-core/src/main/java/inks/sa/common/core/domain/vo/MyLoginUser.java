package inks.sa.common.core.domain.vo;

import inks.common.core.domain.LoginUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MyLoginUser extends LoginUser {

    //---------------------------------------------以下是新加字段---------------------------------------------
    // 在原有的LoginUser基础上添加了一个工程师的信息
    private SaEngineerPojo engineer;

    // ID
    private String id;
    //微信唯一标识
    private String wxopenid;
    // 是否管理员
    private Integer adminmark;

    public SaEngineerPojo getEngineer() {
        return engineer;
    }

    public void setEngineer(SaEngineerPojo engineer) {
        this.engineer = engineer;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWxopenid() {
        return wxopenid;
    }

    public void setWxopenid(String wxopenid) {
        this.wxopenid = wxopenid;
    }

    public Integer getAdminmark() {
        return adminmark;
    }

    public void setAdminmark(Integer adminmark) {
        this.adminmark = adminmark;
    }
}
