package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaNoticeEntity;
import inks.sa.common.core.domain.pojo.SaNoticePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaNotice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-08 10:07:02
 */
@Mapper
public interface SaNoticeMapper {


    SaNoticePojo getEntity(@Param("key") String key);

    List<SaNoticePojo> getPageList(QueryParam queryParam);

    int insert(SaNoticeEntity saNoticeEntity);

    int update(SaNoticeEntity saNoticeEntity);


    int delete(@Param("key") String key);

}

