package inks.sa.common.core.config.oss.service;

import cn.hutool.core.util.IdUtil;
import inks.common.core.domain.FileInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.domain.pojo.SaFilelogPojo;
import inks.sa.common.core.service.SaFilelogService;
import io.minio.*;
import io.minio.messages.Bucket;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * Minio对象存储服务实现类
 * 基于MinIO实现文件的存储、获取与管理
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2020-11-04
 */
@Service
public class OssMinioServiceImpl implements OssService {

    @Resource
    @Qualifier("minioStorage")
    private Storage storage;

    @Autowired
    private OSSConfigManager configManager;

    /**
     * 默认存储桶名
     */
    private String BUCKET;

    /**
     * 访问URL前缀
     */
    private String URL_PREFIX;

    /**
     * 初始化Minio配置
     */
    @PostConstruct
    public void init() {
        this.BUCKET = configManager.getMinioBucket();
        this.URL_PREFIX = configManager.getMinioUrlprefix();
    }

    @Value("minio")
    private String storageType;
    private final static Logger log = LoggerFactory.getLogger(OssMinioServiceImpl.class);


    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private SaFilelogService filelogService;


    /**
     * 构建文件信息对象，用于上传后的返回
     *
     * @param file    上传的文件
     * @param objectname 目录名
     * @return 文件信息对象
     */
    @Override
    public FileInfo of(MultipartFile file, String objectname, String module) {
        return this.of(file, BUCKET, null, objectname, module);
    }

    /**
     * 上传文件到Minio
     *
     * @param file       上传的文件
     * @param objectname 存储对象名称
     * @return 文件信息对象
     */
    @Override
    @Transactional
    public FileInfo upload(MultipartFile file, String objectname, String module) {
        FileInfo fileInfo = of(file, objectname, module);
        try {
            storage.putObject(
                    fileInfo.getBucketname(),
                    fileInfo.getDirname(),
                    fileInfo.getFilename(),
                    file.getInputStream(),
                    fileInfo.getContenttype(),
                    fileInfo.getFileoriname());
        } catch (Exception e) {
            throw new BaseBusinessException("文件上传失败: " + e.getMessage());
        }
        return fileInfo;
    }

    // 构建文件信息对象
    private FileInfo of(MultipartFile file, String bucket, String dirname, String objectName, String module) {
        return this.of(file, null, bucket, dirname, objectName, module);
    }

    // 通过文件路径构建文件信息对象
    private FileInfo of(String filePath, String bucket, String dirname, String objectName, String module) {
        return this.of(null, filePath, bucket, dirname, objectName, module);
    }

    /**
     * 构建文件信息对象
     * <p>
     * 支持通过 MultipartFile 或 本地文件路径 两种方式构建 FileInfo
     *
     * @param file       上传的文件，允许为 null
     * @param filePath   本地文件路径，允许为 null
     * @param bucket     存储桶名
     * @param dirname    目录名，可为空（支持自动从 objectName 拆分）
     * @param objectName 对象名（支持路径自动拆分和自动生成）
     * @return 文件信息对象
     */
    private FileInfo of(MultipartFile file, String filePath, String bucket, String dirname, String objectName, String module) {
        // 定义原始文件名、文件大小、文件类型
        String oriFileName;
        long fileSize;
        String contentType;

        // 1. 文件来源判断与基本信息获取
        if (file != null) {
            // 如果上传文件不为空，直接读取相关信息
            oriFileName = file.getOriginalFilename();
            fileSize = file.getSize();
            contentType = file.getContentType();
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(filePath)) {
            // 如果是本地文件路径
            File inputFile = new File(filePath);
            oriFileName = inputFile.getName();
            fileSize = inputFile.length();
            // 根据扩展名获取内容类型
            String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(oriFileName);
            contentType = getContentType(fileSuffix);
        } else {
            // 文件和路径不能同时为空，直接抛出异常
            throw new IllegalArgumentException("file 和 filePath 不能同时为空");
        }

        // 2. objectName 包含路径时，自动拆分 dirname 和 objectName
        if (org.apache.commons.lang3.StringUtils.isNotBlank(objectName)
                && objectName.contains("/")
                && org.apache.commons.lang3.StringUtils.isBlank(dirname)) {
            int lastSlash = objectName.lastIndexOf("/");
            dirname = objectName.substring(0, lastSlash);
            objectName = objectName.substring(lastSlash + 1);
        }

        // 3. objectName 为空时自动生成（UUID 截取）
        if (org.apache.commons.lang3.StringUtils.isBlank(objectName)) {
            objectName = IdUtil.simpleUUID().substring(1, 21);
        }

        // 4. 获取文件扩展名
        String fileSuffix = org.springframework.util.StringUtils.getFilenameExtension(oriFileName);

        // 5. 生成最终文件名（不重复拼接扩展名）
        String fileName = objectName;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(fileSuffix)
                && !objectName.endsWith("." + fileSuffix)) {
            fileName = objectName.concat(".").concat(fileSuffix);
        }

        // 6. 构建文件信息对象
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(bucket.toLowerCase());     // 存储桶名小写
        fileInfo.setDirname(dirname);                     // 目录名
        fileInfo.setFilename(fileName);                   // 文件名
        fileInfo.setFileoriname(oriFileName);             // 原始文件名
        fileInfo.setFilesize(fileSize);                   // 文件大小
        fileInfo.setContenttype(contentType);             // 文件类型
        fileInfo.setFilesuffix(fileSuffix);               // 扩展名
        fileInfo.setStorage(storageType);                 // 存储方式

        // 7. 生成文件访问 URL（占位符替换）
        fileInfo.setFileurl(
                URL_PREFIX.replace("{dirname}", dirname)
                        .replace("{bucket}", bucket)
                        .replace("{filename}", fileName)
        );

        // 20250616 文件上传日志
        SaFilelogPojo utsFilelogPojo = new SaFilelogPojo();
        utsFilelogPojo.setOptype(0);
        utsFilelogPojo.setUsedmark(0);
        utsFilelogPojo.setModule(module);
        utsFilelogPojo.setFileoriname(fileInfo.getFileoriname());
        utsFilelogPojo.setBucketname(fileInfo.getBucketname());
        utsFilelogPojo.setDirname(fileInfo.getDirname());
        utsFilelogPojo.setFilename(fileInfo.getFilename());
        utsFilelogPojo.setFileurl(fileInfo.getFileurl());
        utsFilelogPojo.setFilesize(fileInfo.getFilesize());
        utsFilelogPojo.setContenttype(fileInfo.getContenttype());
        utsFilelogPojo.setFilesuffix(fileInfo.getFilesuffix());
        utsFilelogPojo.setStorage(fileInfo.getStorage());
        filelogService.insert(utsFilelogPojo);
        return fileInfo;
    }


    /**
     * 根据文件扩展名获取内容类型
     *
     * @param fileSuffix 文件扩展名
     * @return 内容类型
     */
    public static String getContentType(String fileSuffix) {
        if (fileSuffix == null) return "application/octet-stream";
        switch (fileSuffix.toLowerCase()) {
            case "txt":
                return "text/plain";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "pdf":
                return "application/pdf";
            case "zip":
                return "application/zip";
            case "sql":
                return "application/sql";
            case "gif":
                return "image/gif";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "html":
                return "text/html";
            default:
                return "application/octet-stream";
        }
    }


    /**
     * 通过文件路径上传文件到默认桶
     *
     * @param filePath   文件路径
     * @param objectName 对象名
     * @return 文件信息对象
     */
    @Override
    @Transactional
    public FileInfo putFile(String filePath, String objectName, String module) {
        try {
            File file = new File(filePath);
            String fileName = file.getName();
            String contentType = getContentType(fileName);

            // 构建文件信息对象
            FileInfo fileInfo = of(filePath, BUCKET, "", objectName, module);

            try (FileInputStream inputStream = new FileInputStream(file)) {
                storage.putObject(BUCKET, objectName, inputStream, contentType,fileName);
            }
            return fileInfo;
        } catch (Exception e) {
            throw new BaseBusinessException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传输入流到存储服务
     *
     * @param inputStream 输入流
     * @param bucketName  桶名称
     * @param objectName  对象名
     * @param size        文件大小
     * @param contentType 内容类型
     * @return 文件信息对象
     */
    @Override
    @Transactional
    public FileInfo uploadStream(InputStream inputStream, String bucketName, String objectName, long size, String contentType,String module) {
        try {
            // 解析目录名和文件名
            String dirname = "";
            String filename = objectName;
            if (objectName.contains("/")) {
                int lastSlashIndex = objectName.lastIndexOf("/");
                dirname = objectName.substring(0, lastSlashIndex);
                filename = objectName.substring(lastSlashIndex + 1);
            }

            // 构建文件信息对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setBucketname(bucketName);
            fileInfo.setFilename(filename);
            fileInfo.setDirname(dirname);
            fileInfo.setFilesize(size);
            fileInfo.setContenttype(contentType);

            // 获取文件扩展名
            String fileSuffix = "";
            if (filename.contains(".")) {
                fileSuffix = filename.substring(filename.lastIndexOf(".") + 1);
                fileInfo.setFilesuffix(fileSuffix);
            }

            fileInfo.setStorage(storageType);
            fileInfo.setFileurl(URL_PREFIX
                    .replace("{dirname}", dirname)
                    .replace("{bucket}", bucketName)
                    .replace("{filename}", filename));

            // 执行上传
            storage.putObject(bucketName, objectName, inputStream, contentType,null);
            // 20250616 文件上传日志
            SaFilelogPojo utsFilelogPojo = new SaFilelogPojo();
            utsFilelogPojo.setOptype(0);
            utsFilelogPojo.setUsedmark(0);
            utsFilelogPojo.setModule(module);
            utsFilelogPojo.setFileoriname(fileInfo.getFileoriname());
            utsFilelogPojo.setBucketname(fileInfo.getBucketname());
            utsFilelogPojo.setDirname(fileInfo.getDirname());
            utsFilelogPojo.setFilename(fileInfo.getFilename());
            utsFilelogPojo.setFileurl(fileInfo.getFileurl());
            utsFilelogPojo.setFilesize(fileInfo.getFilesize());
            utsFilelogPojo.setContenttype(fileInfo.getContenttype());
            utsFilelogPojo.setFilesuffix(fileInfo.getFilesuffix());
            utsFilelogPojo.setStorage(fileInfo.getStorage());
            filelogService.insert(utsFilelogPojo);
            return fileInfo;
        } catch (Exception e) {
            throw new BaseBusinessException("流上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除对象存储中的文件
     *
     * @param bucketName 桶名称
     * @param objectname 对象名
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public boolean remove(String bucketName, String objectname) {
        try {
            storage.removeObject(bucketName.toLowerCase(), objectname);
            return true;
        } catch (Exception e) {
            throw new BaseBusinessException("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件到指定桶和目录
     *
     * @param file       上传的文件
     * @param bucket     存储桶名
     * @param dirname    目录名
     * @param objectname 对象名
     * @return 文件信息对象
     */
    @Override
    @Transactional
    public FileInfo putFile(MultipartFile file, String bucket, String dirname, String objectname, String module) {
        FileInfo fileInfo = of(file, bucket, dirname, objectname, module);
        try {
            storage.putObject(
                    fileInfo.getBucketname(),
                    fileInfo.getDirname(),
                    fileInfo.getFilename(),
                    file.getInputStream(),
                    fileInfo.getContenttype(),
                    fileInfo.getFileoriname());
        } catch (Exception e) {
            throw new BaseBusinessException("文件上传失败: " + e.getMessage());
        }
        return fileInfo;
    }


    /**
     * 动态更新Minio存储配置
     *
     * @param endpoint        Minio服务端点
     * @param accessKeyId     访问密钥ID
     * @param accessKeySecret 访问密钥密文
     */
    @Override
    public void updateStorage(String endpoint, String accessKeyId, String accessKeySecret) {
        // 创建新的 MinIO Client 对象
        MinioClient minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKeyId, accessKeySecret)
                .build();

        // 验证连接配置是否正确
        try {
            //List<Bucket> buckets = minioClient.listBuckets();
            //boolean bucketExists = minioClient.bucketExists(BUCKET);
        } catch (Exception e) {
            throw new BaseBusinessException("无法连接到 MinIO 服务，请检查配置是否正确：" + e.getMessage());
        }

        // 使用反射更新Storage组件中的MinioClient
        try {
            String beanName = "minioStorage";
            Storage storage = applicationContext.getBean(beanName, Storage.class);
            Field field = storage.getClass().getDeclaredField("minioClient");
            field.setAccessible(true);
            ReflectionUtils.setField(field, storage, minioClient);
        } catch (NoSuchFieldException e) {
            throw new BaseBusinessException("无法获取到 MinIO Client 对象，请检查配置是否正确：" + e.getMessage());
        }
    }


    // ==============================文件夹操作类：=====================

    @Resource
    private MinioClient minioClient;

    /**
     * 获取指定路径下的文件/文件夹大小，并按大小排序（支持递归/非递归模式）
     *
     * @param bucket    桶名
     * @param path      路径（如 "folder/" 或 "folder/file.txt"）
     * @param recursive 是否递归统计子路径
     * @return Map<文件路径, 总大小 （ MB ）>，按大小降序排列
     */
    public Map<String, Double> getFileTreeSizes(String bucket, String path, boolean recursive) {
        if (org.apache.commons.lang3.StringUtils.isBlank(bucket)) {
            return getAllBucketsSizes();
        }
        // 使用 BigDecimal 存储，避免 double 精度误差
        Map<String, BigDecimal> sizeMap = new HashMap<>();
        String prefix = processPrefix(path);

        try {
            // 列出指定 bucket 和前缀的对象，递归遍历子目录
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            for (Result<Item> result : results) {
                Item item = result.get();
                String objectName = item.objectName();

                // 字节转 MB，保留 4 位小数
                BigDecimal sizeInMB = BigDecimal.valueOf(item.size())
                        .divide(BigDecimal.valueOf(1024 * 1024), 4, RoundingMode.HALF_UP);

                if (!recursive) {
                    // 非递归模式下，仅处理当前层级的子路径
                    String subPath = getDirectChildPath(objectName, prefix);
                    if (subPath == null) continue;

                    // 累加大小
                    sizeMap.merge(subPath, sizeInMB, BigDecimal::add);
                } else {
                    // 递归模式下，获取所有父路径并逐级累加
                    List<String> parentPaths = getParentPaths(objectName);
                    for (String parentPath : parentPaths) {
                        sizeMap.merge(parentPath, sizeInMB, BigDecimal::add);
                    }
                }
            }

            // 对结果按大小倒序排序，并将 BigDecimal 转为保留两位小数的 Double
            return sizeMap.entrySet().stream()
                    .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> e.getValue().setScale(4, RoundingMode.HALF_UP).doubleValue(),
                            (a, b) -> a,
                            LinkedHashMap::new
                    ));

        } catch (Exception e) {
            throw new RuntimeException("获取文件树大小失败", e);
        }
    }

    // 提取当前层级的直接子路径（非递归模式）
    private String getDirectChildPath(String objectName, String prefix) {
        if (!objectName.startsWith(prefix)) return null;

        String relativePath = objectName.substring(prefix.length());
        int slashIndex = relativePath.indexOf('/');
        if (slashIndex == -1) {
            // 当前路径下的文件，例如 "folder/file.txt"
            return prefix + relativePath;
        } else {
            // 当前路径下的文件夹，例如 "folder/subfolder/xxx.txt" → 只保留 "folder/subfolder/"
            return prefix + relativePath.substring(0, slashIndex + 1);
        }
    }

    // 处理路径前缀
    private String processPrefix(String path) {
        if (org.apache.commons.lang3.StringUtils.isBlank(path)) return "";

        String prefix = path;
        if (!path.endsWith("/")) prefix += "/";

        return prefix;
    }


    // 原getParentPaths方法保持不变
    private List<String> getParentPaths(String objectName) {
        List<String> paths = new ArrayList<>();
        String[] parts = objectName.split("/");
        StringBuilder path = new StringBuilder();

        for (int i = 0; i < parts.length; i++) {
            path.append(parts[i]);
            if (i < parts.length - 1) path.append("/");
            paths.add(path.toString());
        }

        return paths;
    }


    // 所有桶的名称及其总大小（按从大到小排序）
    public Map<String, Double> getAllBucketsSizes() {
        Map<String, Long> bucketSizeMapBytes = new HashMap<>();
        Map<String, Double> bucketSizeMapMB = new HashMap<>();

        try {
            List<Bucket> buckets = minioClient.listBuckets();

            for (Bucket bucket : buckets) {
                String bucketName = bucket.name();
                long totalSizeBytes = 0L;

                try {
                    Iterable<Result<Item>> results = minioClient.listObjects(
                            ListObjectsArgs.builder()
                                    .bucket(bucketName)
                                    .recursive(true)
                                    .build()
                    );

                    for (Result<Item> result : results) {
                        Item item = result.get();
                        totalSizeBytes += item.size();
                    }

                    bucketSizeMapBytes.put(bucketName, totalSizeBytes);
                } catch (Exception e) {
                    log.warn("无法访问桶: {}，原因: {}", bucketName, e.getMessage());
                }
            }

            // 转换为 MB 并保留两位小数
            for (Map.Entry<String, Long> entry : bucketSizeMapBytes.entrySet()) {
                BigDecimal sizeInMB = BigDecimal.valueOf(entry.getValue())
                        .divide(BigDecimal.valueOf(1024 * 1024), 4, RoundingMode.HALF_UP);
                bucketSizeMapMB.put(entry.getKey(), sizeInMB.doubleValue());
            }

            // 排序并返回 LinkedHashMap（保留两位小数）
            return bucketSizeMapMB.entrySet().stream()
                    .sorted((a, b) -> Double.compare(b.getValue(), a.getValue()))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> Math.round(entry.getValue() * 100.0) / 100.0,
                            (oldValue, newValue) -> oldValue,
                            LinkedHashMap::new
                    ));
        } catch (Exception e) {
            throw new RuntimeException("获取所有桶大小失败", e);
        }
    }


    /**
     * 批量下载文件并打包为 ZIP（保留原始目录结构）
     *
     * @param bucket     桶名
     * @param remotePath 远程路径（如 "folder/" 或 "folder/subfolder/"）
     * @return ZIP 文件的字节流（需由调用方处理释放）
     */
    public byte[] downloadFilesAsZip(String bucket, String remotePath) {
        Map<String, byte[]> fileMap = new HashMap<>();
        String prefix = processPrefix(remotePath);

        try {
            // 1. 列出所有对象
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            // 2. 下载所有文件到内存
            for (Result<Item> result : results) {
                Item item = result.get();

                // 跳过文件夹对象（MinIO中文件夹对象大小为0）
                if (item.size() == 0) continue;

                // 3. 构建ZIP内的相对路径
                String zipEntryName = buildZipEntryName(item.objectName(), prefix);
                if (zipEntryName == null) continue;

                // 4. 下载文件到内存
                try (InputStream is = minioClient.getObject(
                        GetObjectArgs.builder()
                                .bucket(bucket)
                                .object(item.objectName())
                                .build())) {

                    // 5. 读取为字节数组
                    fileMap.put(zipEntryName, readStreamToByteArray(is));
                }
            }

            // 6. 创建ZIP字节输出流
            ByteArrayOutputStream zipOutput = new ByteArrayOutputStream();
            try (ZipOutputStream zos = new ZipOutputStream(zipOutput)) {
                // 7. 将文件写入ZIP
                for (Map.Entry<String, byte[]> entry : fileMap.entrySet()) {
                    zos.putNextEntry(new ZipEntry(entry.getKey()));
                    zos.write(entry.getValue());
                    zos.closeEntry();
                }
            }

            return zipOutput.toByteArray();

        } catch (Exception e) {
            throw new RuntimeException("批量下载并打包ZIP失败", e);
        }
    }

    /**
     * 构建 ZIP 条目名称
     */
    private String buildZipEntryName(String objectName, String prefix) {
        if (!objectName.startsWith(prefix)) return null;

        // 移除前缀并构建相对路径
        String relativePath = objectName.substring(prefix.length());

        // 确保统一使用 ZIP 标准路径分隔符
        return relativePath.replace(File.separatorChar, '/');
    }

    /**
     * 将输入流转为字节数组
     */
    private byte[] readStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[8192];
        int bytesRead;

        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }

        return buffer.toByteArray();
    }


    /**
     * 上传 ZIP 文件并恢复原始目录结构到 MinIO
     *
     * @param zipFile ZIP 文件
     * @param bucket  目标桶名
     * @param baseDir 基础目录（可选，用于在桶下建立子路径）
     * @return 上传的文件列表
     */
    public List<String> uploadZipAndRebuildStructure(MultipartFile zipFile, String bucket, String baseDir) {
        List<String> uploadedFiles = new ArrayList<>();

        // 确保基础目录格式正确
        if (baseDir != null && !baseDir.isEmpty() && !baseDir.endsWith("/")) {
            baseDir += "/";
        }

        try (InputStream is = zipFile.getInputStream();
             ZipInputStream zis = new ZipInputStream(is)) {

            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                // 跳过目录条目
                if (entry.isDirectory()) {
                    log.warn("跳过 ZIP 中的目录条目: {}", entry.getName());
                    continue;
                }

                // 构建目标对象名（去除 ZIP 条目开头的斜杠）
                String objectName = entry.getName();
                if (objectName.startsWith("/")) {
                    objectName = objectName.substring(1);
                }

                // 拼接基础目录
                if (baseDir != null && !baseDir.isEmpty()) {
                    objectName = baseDir + objectName;
                }

                // 上传单个文件
                try {
                    uploadSingleFileFromZip(zis, bucket, objectName);
                    uploadedFiles.add(objectName);
                    zis.closeEntry();
                } catch (Exception e) {
                    log.error("上传 ZIP 条目失败: {}", entry.getName(), e);
                    continue;
                }
            }

            return uploadedFiles;

        } catch (Exception e) {
            throw new RuntimeException("处理 ZIP 文件失败", e);
        }
    }

    /**
     * 从 ZIP 输入流中读取并上传单个文件
     */
    private void uploadSingleFileFromZip(ZipInputStream zis, String bucket, String objectName) throws Exception {
        ByteArrayOutputStream fileContent = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;

        while ((bytesRead = zis.read(buffer)) != -1) {
            fileContent.write(buffer, 0, bytesRead);
        }

        // 拆分 objectName 为 dirname 和 filename
        String dirname = "";
        String filename = objectName;

        int lastSlashIndex = objectName.lastIndexOf("/");
        if (lastSlashIndex > 0) {
            dirname = objectName.substring(0, lastSlashIndex);
            filename = objectName.substring(lastSlashIndex + 1);
        }

        // 使用 Storage 接口的 putObject 方法
        storage.putObject(
                bucket,
                dirname,
                filename,
                new ByteArrayInputStream(fileContent.toByteArray()),
                "application/octet-stream",
                null);
    }


    /**
     * 批量删除指定路径下的所有文件（不包含文件夹）
     *
     * @param bucket 桶名
     * @param path   路径（如 "folder/" 或 "folder/subfolder/"）
     * @return 删除的文件数量
     */
    public int removeFilesByPath(String bucket, String path) {
        String prefix = processPrefix(path);

        try {
            // 1. 列出指定路径下所有对象
            Iterable<Result<Item>> results = minioClient.listObjects(
                    io.minio.ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            // 2. 构建 DeleteObject 列表（只删除文件，size>0）
            List<DeleteObject> objectsToDelete = new ArrayList<>();
            for (Result<Item> result : results) {
                Item item = result.get();
                if (item.size() > 0) {
                    objectsToDelete.add(new DeleteObject(item.objectName()));
                }
            }

            // 3. 批量删除
            if (!objectsToDelete.isEmpty()) {
                Iterable<Result<DeleteError>> errors = minioClient.removeObjects(
                        RemoveObjectsArgs.builder()
                                .bucket(bucket)
                                .objects(objectsToDelete)
                                .build()
                );
                // 可选：遍历 errors 打印失败信息
                for (Result<DeleteError> errorResult : errors) {
                    DeleteError error = errorResult.get();
                    System.err.println("删除失败: object=" + error.objectName() + ", code=" + error.errorCode());
                }
            }

            return objectsToDelete.size();

        } catch (Exception e) {
            throw new RuntimeException("批量删除失败: " + e.getMessage(), e);
        }
    }


}