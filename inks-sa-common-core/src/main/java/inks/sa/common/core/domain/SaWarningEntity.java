package inks.sa.common.core.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 预警(SaWarning)实体类
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Data
public class SaWarningEntity implements Serializable {
    private static final long serialVersionUID = 314546608408368113L;
     // id
    private String id;
     // 通用分组
    private String gengroupid;
     // 模块编码
    private String modulecode;
     // 预警编码
    private String warncode;
     // 预警名称
    private String warnname;
     // 预警字段
    private String warnfield;
     // 服务编码
    private String svccode;
     // 预警接口
    private String warnapi;
     // web文件
    private String webpath;
     // Css图标
    private String imagecss;
     // 标签文本
    private String tagtitle;
     // 许可编码
    private String permcode;
     // 行号
    private Integer rownum;
     // 有效性1
    private Integer enabledmark;
     // 摘要
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 乐观锁
    private Integer revision;



}

