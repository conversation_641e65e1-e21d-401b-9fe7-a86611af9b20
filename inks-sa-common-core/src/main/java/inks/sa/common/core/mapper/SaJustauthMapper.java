package inks.sa.common.core.mapper;

import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.TenantInfo;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.domain.SaJustauthEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 第三方登录(SaJustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-18 14:01:59
 */
@Mapper
public interface SaJustauthMapper {


    SaJustauthPojo getEntity(@Param("key") String key);

    List<SaJustauthPojo> getPageList(QueryParam queryParam);

    int insert(SaJustauthEntity saJustauthEntity);

    int update(SaJustauthEntity saJustauthEntity);

    int delete(@Param("key") String key);

    SaJustauthPojo getEntityByAuthuuid(String authuuid);

    int deleteByAuthUuid(String authuuid);

    SaJustauthPojo getJustauthByUuid(String key, String type, String tid);

    SaJustauthPojo getJustauthByUserid(String key, String type, String tid);

    List<JustauthPojo> getAdminListByDeptid(String key, String type, String tid);

    List<JustauthPojo> getListByUnionid(String key);
}

