package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaConfigEntity;
import inks.sa.common.core.domain.pojo.SaConfigPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-30 16:36:58
 */
@Mapper
public interface SaConfigMapper {


    SaConfigPojo getEntity(@Param("key") String key);


    List<SaConfigPojo> getPageList(QueryParam queryParam);


    int insert(SaConfigEntity saConfigEntity);


    int update(SaConfigEntity saConfigEntity);


    int delete(@Param("key") String key);

    SaConfigPojo getEntityByCfgKey(String cfgkey);

    SaConfigPojo getEntityByKeyUser(@Param("key") String key, @Param("userid") String userid);

    SaConfigPojo getEntityByKey(String key);

    String getSystemRegistrkey();

    String getCfgValueByCfgKey(String cfgKey);

    List<SaConfigPojo> getAllList();
}

