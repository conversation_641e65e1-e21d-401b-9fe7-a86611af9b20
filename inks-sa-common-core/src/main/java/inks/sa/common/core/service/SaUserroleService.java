package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import inks.sa.common.core.domain.pojo.SaUserrolePojo;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 用户角色关联(SaUserrole)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:28
 */
public interface SaUserroleService {


    SaUserrolePojo getEntity(String key);

    PageInfo<SaUserrolePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUserrolePojo 实例对象
     * @return 实例对象
     */
    SaUserrolePojo insert(SaUserrolePojo saUserrolePojo);

    /**
     * 修改数据
     *
     * @param saUserrolepojo 实例对象
     * @return 实例对象
     */
    SaUserrolePojo update(SaUserrolePojo saUserrolepojo);

    int delete(String key);

    List<SaUserrolePojo> getListByRole(String key);


    List<SaUserrolePojo> getListByUser(String key);


    List<SaPermcodePojo> getPermByUser(String key);

    HashSet<String> getPermSetByUserNoRedis(String key);

    Map<String, String> getConfigMapByTenUiNoRedis();

    Map<String, String> getConfigMapByUserUiNoRedis(String userid);

    LoginUser updateTokenPerm(LoginUser loginUser);
}
