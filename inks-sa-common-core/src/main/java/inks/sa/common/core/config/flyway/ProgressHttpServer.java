package inks.sa.common.core.config.flyway;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import inks.sa.common.core.utils.PrintColor;

import java.io.*;
import java.net.InetSocketAddress;
import java.nio.file.Files;
import java.util.concurrent.Executors;

/**
 * Flyway 进度页面 HTTP 服务器
 * 提供通过 HTTP 访问进度页面的功能
 */
public class ProgressHttpServer {
    
    private HttpServer server;
    private final int port;
    private final ProgressPageManager progressManager;

    public ProgressHttpServer(int port, ProgressPageManager progressManager) {
        this.port = port;
        this.progressManager = progressManager;
    }
    
    /**
     * 启动HTTP服务器
     */
    public void start() {
        try {
            server = HttpServer.create(new InetSocketAddress(port), 0);
            
            // 设置进度页面路由
            server.createContext("/flyway", new ProgressHandler());
            server.createContext("/flyway-progress.html", new ProgressHandler()); // 保持向后兼容
            server.createContext("/", new IndexHandler());
            
            // 使用线程池处理请求
            server.setExecutor(Executors.newFixedThreadPool(2));
            server.start();
            
            PrintColor.red("Flyway 进度服务器已启动:");
            PrintColor.red("  - 进度页面: http://localhost:8080/flyway");
            PrintColor.red("  - 详细页面: http://localhost:8080/flyway-progress.html");
            PrintColor.red("  - 首页: http://localhost:8080/");
            PrintColor.red("  - 注意: 初始化完成后将自动释放8080端口给Spring Boot应用");
            
        } catch (IOException e) {
            PrintColor.red("启动HTTP服务器失败: " + e.getMessage());
        }
    }
    
    /**
     * 停止HTTP服务器
     */
    public void stop() {
        if (server != null) {
            server.stop(0);
            PrintColor.red("Flyway 进度服务器已停止");
        }
    }
    
    /**
     * 进度页面处理器
     */
    private class ProgressHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String html = progressManager.generateProgressHtml();
            handleHtmlResponse(exchange, html);
        }
    }
    
    /**
     * 首页处理器
     */
    private class IndexHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String indexHtml = generateIndexHtml();
            
            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.getResponseHeaders().set("Cache-Control", "no-cache");
            exchange.sendResponseHeaders(200, indexHtml.getBytes("UTF-8").length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(indexHtml.getBytes("UTF-8"));
            }
        }
    }
    
    /**
     * 处理HTML响应
     */
    private void handleHtmlResponse(HttpExchange exchange, String html) throws IOException {
        byte[] content = html.getBytes("UTF-8");

        exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.getResponseHeaders().set("Refresh", "2"); // 每2秒自动刷新
        exchange.sendResponseHeaders(200, content.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(content);
        }
    }
    
    /**
     * 生成首页HTML
     */
    private String generateIndexHtml() {
        return "<!DOCTYPE html>\n" +
                "<html lang='zh-CN'>\n" +
                "<head>\n" +
                "    <meta charset='UTF-8'>\n" +
                "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n" +
                "    <title>Flyway 数据库初始化服务</title>\n" +
                "    <style>\n" +
                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; padding: 20px; }\n" +
                "        .container { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 30px; max-width: 600px; margin: 0 auto; text-align: center; }\n" +
                "        h1 { color: #333; margin-bottom: 30px; }\n" +
                "        .link-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; transition: all 0.3s; }\n" +
                "        .link-card:hover { background: #e9ecef; transform: translateY(-2px); }\n" +
                "        .link-card a { text-decoration: none; color: #007bff; font-size: 18px; font-weight: bold; }\n" +
                "        .link-card p { color: #666; margin: 10px 0 0 0; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class='container'>\n" +
                "        <h1>🚀 Flyway 数据库初始化服务</h1>\n" +
                "        \n" +
                "        <div class='link-card'>\n" +
                "            <a href='/flyway-progress.html'>📊 查看数据库初始化进度</a>\n" +
                "            <p>实时查看数据库初始化进度和表创建状态</p>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div style='margin-top: 30px; color: #666; font-size: 14px;'>\n" +
                "            <p>服务器端口: 8080</p>\n" +
                "            <p>页面会自动刷新以显示最新状态</p>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}
