package inks.sa.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.constant.RsaKey;
import inks.common.core.utils.RSAUtils;
import inks.sa.common.core.utils.RSA.MyRSA;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Component
public class LicenseUtils {
    @Value("${inks.license:Pwx4L16D1TNkS/PowyEJIyigGR6iniaSLOUvjOAwAG+wK/11mDdupDQMjSwypdWp7HCtewGkPvpTuT1vPknc3msYxr4c/gRQHTbxxZMzjXr3OCOu++vHBawihScogF5ODfrtsKEXpL0gLDiRNjjoeF3LYcVzHP5+eUG38LfVBxypC+sJXIrLMiX/xWiB9rHAMEZah/Rf3H1mnZREqBDUvKPmcsHYlDucmnphgsoE+Ay4kkLN6f9IM5EMmDEpXRLZ0kYn71OBxvpIB3DINNX458sYw3UehMV+fJIhac5lkQXQ5lNQvuQokE1C+wzi+X3axhiDBwFmB34OsqZc/5L00Q==}")
    private String INKS_LICENSE;

    /**
     * 解密 yml 配置文件中的 license，获取其中的 SvcLicense 对象。
     * 该解密后的内容是一个 JSON 格式的字符串，结构如下：
     * {
     * "code": "sasom",           // 系统编码
     * "dt": 生成时间的时间戳,   // 时间戳，生成时间
     * "sn": "生成的UUID",        // 代替机器的 SN
     * "gp": "组织标识",          // 组织，如 c1006, k1006
     * "env": "环境",             // 环境：dev/demo/beta/prod
     * "lt": 失效期时间戳        // 失效时间的时间戳
     * }
     * 该字符串将在初始化时进行解密并缓存，以便后续调用。
     */
    private static String yml_license_json;

    private static LicenseUtils instance; // 单例实例

    @PostConstruct
    private void init() {
        // 保存当前实例到静态字段
        instance = this;

        // RSA 私钥解密 INKS_LICENSE，并解析出 SN
        try {
            // 没值时使用默认值sn:init222
            if (StringUtils.isBlank(INKS_LICENSE)) {
                INKS_LICENSE = "Pwx4L16D1TNkS/PowyEJIyigGR6iniaSLOUvjOAwAG+wK/11mDdupDQMjSwypdWp7HCtewGkPvpTuT1vPknc3msYxr4c/gRQHTbxxZMzjXr3OCOu++vHBawihScogF5ODfrtsKEXpL0gLDiRNjjoeF3LYcVzHP5+eUG38LfVBxypC+sJXIrLMiX/xWiB9rHAMEZah/Rf3H1mnZREqBDUvKPmcsHYlDucmnphgsoE+Ay4kkLN6f9IM5EMmDEpXRLZ0kYn71OBxvpIB3DINNX458sYw3UehMV+fJIhac5lkQXQ5lNQvuQokE1C+wzi+X3axhiDBwFmB34OsqZc/5L00Q==";
            }
            String decryptLicense = RSAUtils.decrypt(INKS_LICENSE, RsaKey.PRIVATE_KEY);
            yml_license_json = decryptLicense; // 缓存解密结果
            PrintColor.green("SvcLicense解密成功：" + yml_license_json);
            //* 检查 License 解密后的值，若为默认值则输出警告
            if (yml_license_json != null && yml_license_json.contains("init222")) {
                String warningMessage = "=========================== 警告：当前 yml.license 为默认值sn:init222，请购买正版 license！";
                for (int i = 0; i < 3; i++) {
                    PrintColor.red(warningMessage);
                }
            }
        } catch (Exception e) {
            throw new IllegalStateException("License 解密失败，请检查配置或密钥", e);
        }
    }


    /**
     * 获取解密后的完整 license JSON 字符串
     */
    public static String getYmlSvcLicenseJson() {
        ensureInitialized();
        return yml_license_json;
    }

    /**
     * 获取yml解密后的 SN
     */
    public static String getYmlSN() {
        ensureInitialized();
        try {
            Map<String, String> map = JSON.parseObject(yml_license_json, new TypeReference<Map<String, String>>() {});
            return map.get("sn");
        } catch (Exception e) {
            throw new IllegalStateException("yml.license中的sn JSON解析失败", e);
        }
    }

    /**
     * 获取yml解密后的装机证书失效时间lt
     */
    public static Long getYmlLT() {
        ensureInitialized();
        try {
            Map<String, String> map = JSON.parseObject(yml_license_json, new TypeReference<Map<String, String>>() {});
            return Long.valueOf(map.get("lt"));
        } catch (Exception e) {
            throw new IllegalStateException("yml.license中的lt JSON解析失败", e);
        }
    }


    /**
     * 确保 License 初始化完成
     */
    private static void ensureInitialized() {
        if (yml_license_json == null) {
            throw new IllegalStateException("LicenseUtils 未完成初始化，请确保在使用前完成初始化");
        }
        //else if (yml_license_json.equals("123456")) {
        //    throw new IllegalStateException("yml.license 为默认值，请购买正版license！");
        //}
    }
}
