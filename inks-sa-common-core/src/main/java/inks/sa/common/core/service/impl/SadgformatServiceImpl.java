package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SadgformatEntity;
import inks.sa.common.core.domain.SadgformatitemEntity;
import inks.sa.common.core.domain.pojo.SadgformatPojo;
import inks.sa.common.core.domain.pojo.SadgformatitemPojo;
import inks.sa.common.core.domain.pojo.SadgformatitemdetailPojo;
import inks.sa.common.core.mapper.SadgformatMapper;
import inks.sa.common.core.mapper.SadgformatitemMapper;
import inks.sa.common.core.service.SadgformatService;
import inks.sa.common.core.service.SadgformatitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 列表格式(Sadgformat)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-21 15:14:27
 */
@Service("sadgformatService")
public class SadgformatServiceImpl implements SadgformatService {
    @Resource
    private SadgformatMapper sadgformatMapper;

    @Resource
    private SadgformatitemMapper sadgformatitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SadgformatitemService sadgformatitemService;


    @Override
    public SadgformatPojo getEntity(String key, String tid) {
        return this.sadgformatMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<SadgformatitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SadgformatitemdetailPojo> lst = sadgformatMapper.getPageList(queryParam);
            PageInfo<SadgformatitemdetailPojo> pageInfo = new PageInfo<SadgformatitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SadgformatPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            SadgformatPojo sadgformatPojo = this.sadgformatMapper.getEntity(key, tid);
            //读取子表
            sadgformatPojo.setItem(sadgformatitemMapper.getList(sadgformatPojo.getId(), sadgformatPojo.getTenantid()));
            return sadgformatPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SadgformatPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SadgformatPojo> lst = sadgformatMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(sadgformatitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<SadgformatPojo> pageInfo = new PageInfo<SadgformatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SadgformatPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SadgformatPojo> lst = sadgformatMapper.getPageTh(queryParam);
            PageInfo<SadgformatPojo> pageInfo = new PageInfo<SadgformatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param sadgformatPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SadgformatPojo insert(SadgformatPojo sadgformatPojo) {

        // 删除原有内容 EricRen 2022-04-21
//        SadgformatPojo DbPojo = this.getBillEntityByCode(sadgformatPojo.getFormcode(), sadgformatPojo.getListerid(), sadgformatPojo.getTenantid());
//        if (DbPojo != null) {
//            this.delete(DbPojo.getId(), DbPojo.getTenantid());
//        }
//初始化NULL字段
        if (sadgformatPojo.getFormgroupid() == null) sadgformatPojo.setFormgroupid("");
        if (sadgformatPojo.getFormcode() == null) sadgformatPojo.setFormcode("");
        if (sadgformatPojo.getFormname() == null) sadgformatPojo.setFormname("");
        if (sadgformatPojo.getRownum() == null) sadgformatPojo.setRownum(0);
        if (sadgformatPojo.getEnabledmark() == null) sadgformatPojo.setEnabledmark(0);
        if (sadgformatPojo.getDefmark() == null) sadgformatPojo.setDefmark(0);
        if (sadgformatPojo.getSummary() == null) sadgformatPojo.setSummary("");
        if (sadgformatPojo.getCreateby() == null) sadgformatPojo.setCreateby("");
        if (sadgformatPojo.getCreatebyid() == null) sadgformatPojo.setCreatebyid("");
        if (sadgformatPojo.getCreatedate() == null) sadgformatPojo.setCreatedate(new Date());
        if (sadgformatPojo.getLister() == null) sadgformatPojo.setLister("");
        if (sadgformatPojo.getListerid() == null) sadgformatPojo.setListerid("");
        if (sadgformatPojo.getModifydate() == null) sadgformatPojo.setModifydate(new Date());
        if (sadgformatPojo.getCustom1() == null) sadgformatPojo.setCustom1("");
        if (sadgformatPojo.getCustom2() == null) sadgformatPojo.setCustom2("");
        if (sadgformatPojo.getCustom3() == null) sadgformatPojo.setCustom3("");
        if (sadgformatPojo.getCustom4() == null) sadgformatPojo.setCustom4("");
        if (sadgformatPojo.getCustom5() == null) sadgformatPojo.setCustom5("");
        if (sadgformatPojo.getTenantid() == null) sadgformatPojo.setTenantid("");
        if (sadgformatPojo.getTenantname() == null) sadgformatPojo.setTenantname("");
        if (sadgformatPojo.getRevision() == null) sadgformatPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SadgformatEntity sadgformatEntity = new SadgformatEntity();
        BeanUtils.copyProperties(sadgformatPojo, sadgformatEntity);
        //设置id和新建日期
        sadgformatEntity.setId(id);
        sadgformatEntity.setRevision(1);  //乐观锁
        //插入主表
        this.sadgformatMapper.insert(sadgformatEntity);
        //Item子表处理
        List<SadgformatitemPojo> lst = sadgformatPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                SadgformatitemPojo itemPojo = this.sadgformatitemService.clearNull(lst.get(i));
                SadgformatitemEntity sadgformatitemEntity = new SadgformatitemEntity();
                BeanUtils.copyProperties(itemPojo, sadgformatitemEntity);
                //设置id和Pid
                sadgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                sadgformatitemEntity.setPid(id);
                sadgformatitemEntity.setTenantid(sadgformatPojo.getTenantid());
                sadgformatitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.sadgformatitemMapper.insert(sadgformatitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(sadgformatEntity.getId(), sadgformatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param sadgformatPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SadgformatPojo update(SadgformatPojo sadgformatPojo) {
        //主表更改
        SadgformatEntity sadgformatEntity = new SadgformatEntity();
        BeanUtils.copyProperties(sadgformatPojo, sadgformatEntity);
        this.sadgformatMapper.update(sadgformatEntity);
        if (sadgformatPojo.getItem() != null) {
            //Item子表处理
            List<SadgformatitemPojo> lst = sadgformatPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = sadgformatMapper.getDelItemIds(sadgformatPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.sadgformatitemMapper.delete(lstDelIds.get(i), sadgformatEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SadgformatitemEntity sadgformatitemEntity = new SadgformatitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SadgformatitemPojo itemPojo = this.sadgformatitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, sadgformatitemEntity);
                        //设置id和Pid
                        sadgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        sadgformatitemEntity.setPid(sadgformatEntity.getId());  // 主表 id
                        sadgformatitemEntity.setTenantid(sadgformatPojo.getTenantid());   // 租户id
                        sadgformatitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.sadgformatitemMapper.insert(sadgformatitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), sadgformatitemEntity);
                        sadgformatitemEntity.setTenantid(sadgformatPojo.getTenantid());
                        this.sadgformatitemMapper.update(sadgformatitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(sadgformatEntity.getId(), sadgformatEntity.getTenantid());
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
        SadgformatPojo sadgformatPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<SadgformatitemPojo> lst = sadgformatPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.sadgformatitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.sadgformatMapper.delete(key, tid);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    @Override
    public SadgformatPojo getBillEntityByCode(String code, String userid, String tid) {
        try {
            //1.加载个人列设置；
            SadgformatPojo sadgformatPojo = this.sadgformatMapper.getEntityByCodeUser(code, userid, tid);
            if (sadgformatPojo != null) {
                //赋值子表
                sadgformatPojo.setItem(sadgformatitemMapper.getList(sadgformatPojo.getId(), sadgformatPojo.getTenantid()));
            }
            //2.无：加载租户下唯一的默认列设置；
            if (sadgformatPojo == null) {
                sadgformatPojo = this.sadgformatMapper.getTenBillEntityByCode(code, tid);
                if (sadgformatPojo != null) {                    //赋值子表
                    sadgformatPojo.setItem(sadgformatitemMapper.getList(sadgformatPojo.getId(), sadgformatPojo.getTenantid()));
                    sadgformatPojo.setId("");
                }
            }
            //3.还无：加载随机同事的列设置(ORDER BY CreateDate)；
            if (sadgformatPojo == null) {
                sadgformatPojo = this.sadgformatMapper.getEntityByCode(code, tid);
                if (sadgformatPojo != null) {                    //赋值子表
                    sadgformatPojo.setItem(sadgformatitemMapper.getList(sadgformatPojo.getId(), sadgformatPojo.getTenantid()));
                    sadgformatPojo.setId("");
                }
            }
            return sadgformatPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SadgformatPojo getTenBillEntityByCode(String code, String tid) {
        // 读取租户指定code下唯一一条默认数据 主表defmark=1 ;
        SadgformatPojo sadgformatPojo = this.sadgformatMapper.getTenBillEntityByCode(code, tid);
        if (sadgformatPojo != null) {
//            throw new BaseBusinessException("管理员需先设置一个默认的列表格式！");
            //读取子表
            sadgformatPojo.setItem(sadgformatitemMapper.getList(sadgformatPojo.getId(), sadgformatPojo.getTenantid()));
        }
        return sadgformatPojo;
    }
}
