package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaRolemenuwebEntity;
import inks.sa.common.core.domain.pojo.SaRolemenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单Web(SaRolemenuweb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Mapper
public interface SaRolemenuwebMapper {


    SaRolemenuwebPojo getEntity(@Param("key") String key);

    List<SaRolemenuwebPojo> getPageList(QueryParam queryParam);

    int insert(SaRolemenuwebEntity saRolemenuwebEntity);

    int update(SaRolemenuwebEntity saRolemenuwebEntity);

    int delete(@Param("key") String key);

    List<String> getNavidsByUserid(String userid, String tenantid);

    List<String> getNavidsByRoleid(String roleid, String tenantid);

    Integer batchDelete(@Param("roleid") String roleid, @Param("deleteNavids") List<String> deleteNavids);

    Integer batchInsert(@Param("rolemenuwebPojoList") List<SaRolemenuwebPojo> rolemenuwebPojoList);
}

