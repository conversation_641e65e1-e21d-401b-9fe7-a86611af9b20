package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.domain.SaWebnavEntity;
import inks.sa.common.core.domain.pojo.SaWebnavPojo;
import inks.sa.common.core.mapper.SaWebnavMapper;
import inks.sa.common.core.service.SaWebnavService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Pc导航(SaWebnav)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-02 13:42:49
 */
@Service("saWebnavService")
public class SaWebnavServiceImpl implements SaWebnavService {
    @Resource
    private SaWebnavMapper saWebnavMapper;


    @Override
    public SaWebnavPojo getEntity(String key) {
        return this.saWebnavMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaWebnavPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWebnavPojo> lst = saWebnavMapper.getPageList(queryParam);
            PageInfo<SaWebnavPojo> pageInfo = new PageInfo<SaWebnavPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saWebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWebnavPojo insert(SaWebnavPojo saWebnavPojo) {
        //初始化NULL字段
        if (saWebnavPojo.getNavcode() == null) saWebnavPojo.setNavcode("");
        if (saWebnavPojo.getNavname() == null) saWebnavPojo.setNavname("");
        if (saWebnavPojo.getNavcontent() == null) saWebnavPojo.setNavcontent("");
        if (saWebnavPojo.getRownum() == null) saWebnavPojo.setRownum(0);
        if (saWebnavPojo.getEnabledmark() == null) saWebnavPojo.setEnabledmark(0);
        if (saWebnavPojo.getPermissioncode() == null) saWebnavPojo.setPermissioncode("");
        if (saWebnavPojo.getRemark() == null) saWebnavPojo.setRemark("");
        if (saWebnavPojo.getCreateby() == null) saWebnavPojo.setCreateby("");
        if (saWebnavPojo.getCreatebyid() == null) saWebnavPojo.setCreatebyid("");
        if (saWebnavPojo.getCreatedate() == null) saWebnavPojo.setCreatedate(new Date());
        if (saWebnavPojo.getLister() == null) saWebnavPojo.setLister("");
        if (saWebnavPojo.getListerid() == null) saWebnavPojo.setListerid("");
        if (saWebnavPojo.getModifydate() == null) saWebnavPojo.setModifydate(new Date());
        if (saWebnavPojo.getRevision() == null) saWebnavPojo.setRevision(0);
        SaWebnavEntity saWebnavEntity = new SaWebnavEntity();
        BeanUtils.copyProperties(saWebnavPojo, saWebnavEntity);
        //生成雪花id
        saWebnavEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
        saWebnavEntity.setRevision(1);  //乐观锁
        this.saWebnavMapper.insert(saWebnavEntity);
        return this.getEntity(saWebnavEntity.getNavid());

    }

    /**
     * 修改数据
     *
     * @param saWebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWebnavPojo update(SaWebnavPojo saWebnavPojo) {
        SaWebnavEntity saWebnavEntity = new SaWebnavEntity();
        BeanUtils.copyProperties(saWebnavPojo, saWebnavEntity);
        this.saWebnavMapper.update(saWebnavEntity);
        return this.getEntity(saWebnavEntity.getNavid());
    }

    @Override
    public int delete(String key) {
        return this.saWebnavMapper.delete(key);
    }


}
