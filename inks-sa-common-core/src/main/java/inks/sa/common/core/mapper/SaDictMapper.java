package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaDictEntity;
import inks.sa.common.core.domain.pojo.SaDictPojo;
import inks.sa.common.core.domain.pojo.SaDictitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典(SaDict)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:09
 */
@Mapper
public interface SaDictMapper {


    SaDictPojo getEntity(@Param("key") String key);


    List<SaDictitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaDictPojo> getPageTh(QueryParam queryParam);

    int insert(SaDictEntity saDictEntity);

    int update(SaDictEntity saDictEntity);


    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saDictPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaDictPojo saDictPojo);

    SaDictPojo getEntityByDictCode(String key);
}

