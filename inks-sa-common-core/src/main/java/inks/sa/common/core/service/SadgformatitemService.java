package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SadgformatitemPojo;

import java.util.List;

/**
 * 列表项目(Sadgformatitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:14
 */
public interface SadgformatitemService {


    SadgformatitemPojo getEntity(String key, String tid);

    PageInfo<SadgformatitemPojo> getPageList(QueryParam queryParam);


    List<SadgformatitemPojo> getList(String Pid, String tid);

    /**
     * 新增数据
     *
     * @param sadgformatitemPojo 实例对象
     * @return 实例对象
     */
    SadgformatitemPojo insert(SadgformatitemPojo sadgformatitemPojo);

    /**
     * 修改数据
     *
     * @param sadgformatitempojo 实例对象
     * @return 实例对象
     */
    SadgformatitemPojo update(SadgformatitemPojo sadgformatitempojo);

    int delete(String key, String tid);

    /**
     * 修改数据
     *
     * @param sadgformatitempojo 实例对象
     * @return 实例对象
     */
    SadgformatitemPojo clearNull(SadgformatitemPojo sadgformatitempojo);
}
