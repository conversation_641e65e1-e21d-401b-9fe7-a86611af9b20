package inks.sa.common.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.*;
import inks.common.redis.service.RedisService;
import inks.common.security.sa.service.TokenService;
import inks.sa.common.core.constant.MyConstant;
import inks.sa.common.core.controller.A_SaUserController;
import inks.sa.common.core.domain.pojo.SaRedisPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.service.SaRedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * MySQL暂替Redis(SaRedis)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-16 15:13:03
 */
@Service("saRedisService")
@ConditionalOnProperty(name = "inks.redisType", havingValue = "redis")
public class SaRedis_RedisServiceImpl implements SaRedisService {

    @Resource
    private A_SaUserController a_SaUserController;
    @Value("${inks.redisType:mysql}")
    private String redisType;
    @Resource
    private TokenService tokenService;

    @Resource
    private RedisService redisService;

    private static final Logger logger = LoggerFactory.getLogger(SaRedis_RedisServiceImpl.class);
    // 定义一个线程级别的缓存，存放登录用户信息
    private static final ThreadLocal<LoginUser> LOGINUSER_THREADLOCAL = new ThreadLocal<>();

    @Override
    public LoginUser getLoginUser(HttpServletRequest request) {
        // 优先从 ThreadLocal 中获取登录用户信息
        LoginUser loginUser = LOGINUSER_THREADLOCAL.get();
        if (loginUser != null) {
            logger.info("LOGINUSER_THREADLOCAL中已存在登录用户: {}", loginUser);
            return loginUser;
        } else {
            logger.info("LOGINUSER_THREADLOCAL中未找到登录用户，开始使用原有逻辑获取用户信息");
        }
        // 从 request 中获取 authCode (或 token)继续原有逻辑
        String authCode = SecurityUtils.getAuthCode(request);

        // 判断是否使用 MySQL 存储类型
        if ("mysql".equals(redisType)) {
            // 根据authCode是否为空来处理
            if (isBlank(authCode)) {
                loginUser = getLoginUserFromToken(SecurityUtils.getToken(request));
            } else {
                loginUser = getLoginUserFromAuthCode(authCode, request);
            }
        } else {
            // 其他 redisType 处理逻辑
            loginUser = tokenService.getLoginUser();
        }
        // 保存登录用户到 ThreadLocal，确保后续相同线程内可复用
        LOGINUSER_THREADLOCAL.set(loginUser);
        return loginUser;
    }

    @Override
    public SaRedisPojo getEntity(String key) {
        return null;
    }

    @Override
    public PageInfo<SaRedisPojo> getPageList(QueryParam queryParam) {
        return null;
    }


    @Override
    public SaRedisPojo insert(SaRedisPojo saRedisPojo) {
        return null;
    }


    @Override
    public SaRedisPojo update(SaRedisPojo saRedisPojo) {
        return null;
    }

    @Override
    public int delete(String key) {
        return 0;
    }

    @Override
    public int deleteObject(String key) {
        boolean b = this.redisService.deleteObject(key);
        return b ? 1 : 0;
    }

    @Override
    public <T> void setKeyValue(String redisKey, T redisValue, long timeout) {
        this.redisService.setCacheObject(redisKey, redisValue, timeout, TimeUnit.SECONDS);
    }

    @Override
    public <T> void setCacheObject(String redisKey, T redisValue, long timeout) {
        this.redisService.setCacheObject(redisKey, redisValue, timeout, TimeUnit.SECONDS);
    }

    @Override
    public <T> void setKeyValue(String redisKey, T redisValue, long timeout, TimeUnit timeUnit) {
        this.redisService.setCacheObject(redisKey, redisValue, timeout, timeUnit);
    }

    @Override
    public <T> void setCacheObject(String redisKey, T redisValue, long timeout, TimeUnit timeUnit) {
        this.redisService.setCacheObject(redisKey, redisValue, timeout, timeUnit);
    }

    @Override
    public LoginUser getLoginUser() {
        return getLoginUser(ServletUtils.getRequest());
    }


    @Override
    public LoginUser getLoginUserFromToken(String token) {
        String tokenKey = MyConstant.LOGIN_TOKEN_KEY + token;
        LoginUser login = redisService.getCacheObject(tokenKey);
        if (login == null) {
            throw new BaseBusinessException("登录已过期,请重新登录");
        }
        return login;
    }

    // 从Sa_Redis表中通过 键authCode获取登录用户信息；
    // 获取不到则调用authCode进行登录并将 authCode和token的映射 存入 Sa_Redis 表
    public LoginUser getLoginUserFromAuthCode(String authCode, HttpServletRequest request) {
        LoginUser loginUser = getLoginUserByAuthCode(authCode);

        if (loginUser != null) {
            return loginUser;
        } else {
            R<Map<String, Object>> mapR = a_SaUserController.loginByAuthCode(authCode, request, null);
            if (mapR.getCode() != 200) {
                throw new BaseBusinessException(mapR.getMsg());
            }
            Map<String, Object> data = mapR.getData();
            String loginUserJson = JSON.toJSONString(data.get("loginuser"));

            try {
                return JSON.parseObject(loginUserJson, LoginUser.class);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }

        }
    }

    @Override
    public MyLoginUser getMyLoginUser(HttpServletRequest request) {
        return null;
    }

    // 从Sa_Redis表中通过 键authCode获取登录用户信息
    private LoginUser getLoginUserByAuthCode(String authCode) {
        if (isNotBlank(authCode)) {
            try {
                String authKey = MyConstant.AUTHCODE_KEY + AESUtil.Encrypt(authCode);
                String token = getCacheObject(authKey, String.class);
                if (isNotBlank(token)) {
                    return getLoginUserFromToken(token);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        return null;
    }


    @Override
    public int cleanSa_Redis() {
        return 0;
    }

    @Override
    public String getValue(String redisKey) {
        return redisService.getCacheObject(redisKey);
    }

    public <T> T getCacheObject(String redisKey, Class<T> type) {
        String value = redisService.getCacheObject(redisKey);

        if (value == null) {
            return null;
        }

        try {
            // 如果目标类型是 String，直接返回原始值
            if (type == String.class) {
                return type.cast(value);
            }

            // 如果目标类型是 Number，尝试将字符串转换为相应的数字类型
            if (Number.class.isAssignableFrom(type)) {
                return parseNumber(value, type);
            }

            // 如果目标类型是 Boolean，处理为布尔值
            if (type == Boolean.class || type == boolean.class) {
                return type.cast(Boolean.parseBoolean(value));
            }

            // 如果目标类型是 JSON 对象或数组，尝试解析为 JSON 对象或数组
            return JSON.parseObject(value, type);
        } catch (Exception e) {
            // 捕获并处理异常，返回null或者抛出自定义异常
            throw new RuntimeException("Failed to parse value: " + value + " to type: " + type.getName(), e);
        }
    }

    @SuppressWarnings("unchecked")
    private <T> T parseNumber(String value, Class<T> type) {
        if (type == Integer.class || type == int.class) {
            return (T) Integer.valueOf(value);
        } else if (type == Long.class || type == long.class) {
            return (T) Long.valueOf(value);
        } else if (type == Double.class || type == double.class) {
            return (T) Double.valueOf(value);
        } else if (type == Float.class || type == float.class) {
            return (T) Float.valueOf(value);
        } else if (type == Short.class || type == short.class) {
            return (T) Short.valueOf(value);
        } else if (type == Byte.class || type == byte.class) {
            return (T) Byte.valueOf(value);
        } else {
            throw new IllegalArgumentException("Unsupported number type: " + type.getName());
        }
    }

    // 新增方法：处理泛型类型
    public <T> T getCacheObject(String redisKey, TypeReference<T> typeRef) {
        Object value = redisService.getCacheObject(redisKey);
        if (value == null) {
            return null;
        }
        // 直接解析原始对象，避免不必要的JSON转换
        return JSON.parseObject(value.toString(), typeRef);
    }


    public ReportsPojo getCacheObject(String redisKey) {
        String json = redisService.getCacheObject(redisKey);
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, ReportsPojo.class);
    }


    @Override
    public <T> void setCacheMapValue(String redisKey, String hKey, T redisValue) {
        this.setCacheMapValue(redisKey, hKey, redisValue);
    }

    public <T> T getCacheMapValue(String redisKey, String hKey) {
        // 查询Sa_Redis表中是否存在该键和哈希内部的键
        SaRedisPojo saRedisPojo = redisService.getCacheMapValue(redisKey, hKey);
        if (saRedisPojo != null) {
            // 如果找到记录，返回对应的值
            String redisValue = saRedisPojo.getRedisvalue();
            // 使用FastJSON将字符串转换为泛型T的类型
            return JSON.parseObject(redisValue, new TypeReference<T>() {
            });
        } else {
            // 如果记录不存在，返回null或者其他默认值，取决于业务需求
            return null;
        }
    }

    // 已有值则返回false, 否则设置值并返回true
    @Override
    public Boolean setIfAbsent(String redisKey, String value, long timeout, TimeUnit timeUnit) {
        String valueDB = getValue(redisKey);
        if (isNotBlank(valueDB)) {
            return false;
        }
        setKeyValue(redisKey, value, timeout, timeUnit);
        return true;
    }

    public Map<String, Object> createToken(LoginUser loginUser) {
        String token = IdUtils.fastUUID();
        loginUser.setToken(token);
        loginUser.setUserid(loginUser.getUserid());
        loginUser.setUsername(loginUser.getUsername());
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        loginUser.setLogintime(System.currentTimeMillis());
        loginUser.setExpiretime(loginUser.getLogintime() + 43200000L);
        Map<String, Object> map = new HashMap();
        map.put("access_token", token);
        map.put("loginuser", loginUser);
        map.put("expires_in", 43200L);
        this.redisService.setCacheObject("login_tokens:" + token, loginUser, 43200L, TimeUnit.SECONDS);
        return map;
    }
}
