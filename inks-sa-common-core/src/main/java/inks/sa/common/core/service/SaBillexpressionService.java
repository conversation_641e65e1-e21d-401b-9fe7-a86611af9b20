package inks.sa.common.core.service;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaBillexpressionPojo;
import inks.sa.common.core.domain.SaBillexpressionEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 单据公式(SaBillexpression)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-23 14:40:22
 */
public interface SaBillexpressionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaBillexpressionPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaBillexpressionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saBillexpressionPojo 实例对象
     * @return 实例对象
     */
    SaBillexpressionPojo insert(SaBillexpressionPojo saBillexpressionPojo);

    /**
     * 修改数据
     *
     * @param saBillexpressionpojo 实例对象
     * @return 实例对象
     */
    SaBillexpressionPojo update(SaBillexpressionPojo saBillexpressionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaBillexpressionPojo> getListByCode(String key);
}
