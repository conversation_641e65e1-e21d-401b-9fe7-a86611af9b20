package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaValidatorPojo;
import inks.sa.common.core.domain.vo.ValidationResponse;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaValidatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 数据验证(Sa_Validator)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-18 14:23:32
 */
@RestController
@RequestMapping("SaValidator")
@Api(tags = "通用:数据验证")
public class A_SaValidatorController {
    private final static Logger logger = LoggerFactory.getLogger(A_SaValidatorController.class);
    @Resource
    private SaValidatorService saValidatorService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "数据验证", notes = "根据验证编码和数据对象进行验证", produces = "application/json")
    @PostMapping("/validate")
    public R<ValidationResponse> validate(@RequestParam String valicode, @RequestBody Map<String, Object> dataObj) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            return R.ok(saValidatorService.validate(valicode, dataObj, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取数据验证详细信息", notes = "获取数据验证详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Validator.List")
    public R<SaValidatorPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saValidatorService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Validator.List")
    public R<PageInfo<SaValidatorPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Validator.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saValidatorService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增数据验证", notes = "新增数据验证", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Validator.Add")
    public R<SaValidatorPojo> create(@RequestBody String json) {
        try {
            SaValidatorPojo saValidatorPojo = JSONArray.parseObject(json, SaValidatorPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saValidatorPojo.setCreateby(loginUser.getRealName());   // 创建者
            saValidatorPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saValidatorPojo.setCreatedate(new Date());   // 创建时间
            saValidatorPojo.setLister(loginUser.getRealname());   // 制表
            saValidatorPojo.setListerid(loginUser.getUserid());    // 制表id
            saValidatorPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saValidatorService.insert(saValidatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改数据验证", notes = "修改数据验证", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Validator.Edit")
    public R<SaValidatorPojo> update(@RequestBody String json) {
        try {
            SaValidatorPojo saValidatorPojo = JSONArray.parseObject(json, SaValidatorPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saValidatorPojo.setLister(loginUser.getRealname());   // 制表
            saValidatorPojo.setListerid(loginUser.getUserid());    // 制表id
            saValidatorPojo.setModifydate(new Date());   //修改时间
            //            saValidatorPojo.setAssessor(""); // 审核员
            //            saValidatorPojo.setAssessorid(""); // 审核员id
            //            saValidatorPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saValidatorService.update(saValidatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除数据验证", notes = "删除数据验证", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Validator.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saValidatorService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Validator.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaValidatorPojo saValidatorPojo = this.saValidatorService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saValidatorPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

