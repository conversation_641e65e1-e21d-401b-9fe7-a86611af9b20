package inks.sa.common.core.config.oss.service;

import inks.sa.common.core.config.flyway.DataSourceHelper.FlywayMigrationCompleted;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.sa.common.core.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
// 修改为依赖flywayMigrationCompleted，确保Flyway迁移完成后再初始化OSSConfigManager
@DependsOn("flywayMigrationCompleted")
public class OSSConfigManager {

    @Resource
    private SaConfigMapper saConfigMapper;

    // OSS类型
    private String ossType;

    // MinIO配置缓存
    private String minioBucket;
    private String minioAccessKey;
    private String minioSecretKey;
    private String minioEndpoint;
    private String minioUrlprefix;

    // 阿里云配置缓存
    private String aliyunBucket;
    private String aliyunAccessKeyId;
    private String aliyunAccessKeySecret;
    private String aliyunEndpoint;
    private String aliyunUrlprefix;

    @PostConstruct
    public synchronized void loadConfig() {
        try {
            // 加载OSS类型
            this.ossType = getConfigValue("system.oss.type", "minio");
            // 加载 MinIO 配置（带默认值）
            this.minioBucket = getConfigValue("system.oss.minio.bucket", "未配置minio bucket");
            this.minioAccessKey = getConfigValue("system.oss.minio.accesskey", "未配置minio accesskey");
            this.minioSecretKey = getConfigValue("system.oss.minio.accesssecret", "未配置minio accesssecret");
            this.minioEndpoint = getConfigValue("system.oss.minio.endpoint", "1.2.3.4");
            this.minioUrlprefix = getConfigValue("system.oss.minio.urlprefix", "1.2.3.4");

            // 加载阿里云配置（带默认值）
            this.aliyunBucket = getConfigValue("system.oss.aliyun.bucket", "未配置aliyun bucket");
            this.aliyunAccessKeyId = getConfigValue("system.oss.aliyun.accesskeyid", "未配置aliyun accesskeyid");
            this.aliyunAccessKeySecret = getConfigValue("system.oss.aliyun.accesskeysecret", "未配置aliyun accesskeysecret");
            this.aliyunEndpoint = getConfigValue("system.oss.aliyun.endpoint", "1.2.3.4");
            this.aliyunUrlprefix = getConfigValue("system.oss.aliyun.urlprefix", "1.2.3.4");

            PrintColor.lv("[OSS配置加载完毕]");
            PrintColor.lv("激活OssType = " + ossType);
            PrintColor.lv("MinIO -> bucket: " + minioBucket + ", accessKey: " + minioAccessKey + ", secretKey: " + minioSecretKey + ", endpoint: " + minioEndpoint + ", urlprefix: " + minioUrlprefix);
            PrintColor.lv("AliYun -> bucket: " + aliyunBucket + ", accessKeyId: " + aliyunAccessKeyId + ", accessKeySecret: " + aliyunAccessKeySecret + ", endpoint: " + aliyunEndpoint + ", urlprefix: " + aliyunUrlprefix);
        } catch (Exception e) {
            PrintColor.red("[OSS配置加载异常] " + e.getMessage());
            // 设置默认值确保应用可以继续运行
            setDefaultValues();
        }
    }

    // 如果配置加载失败，设置默认值
    private void setDefaultValues() {
        this.ossType = "minio";
        this.minioBucket = "未配置minio bucket";
        this.minioAccessKey = "未配置minio accesskey";
        this.minioSecretKey = "未配置minio accesssecret";
        this.minioEndpoint = "1.2.3.4";
        this.minioUrlprefix = "1.2.3.4";
        this.aliyunBucket = "未配置aliyun bucket";
        this.aliyunAccessKeyId = "未配置aliyun accesskeyid";
        this.aliyunAccessKeySecret = "未配置aliyun accesskeysecret";
        this.aliyunEndpoint = "1.2.3.4";
        this.aliyunUrlprefix = "1.2.3.4";
    }

    // 安全获取配置值，添加异常处理
    private String getConfigValue(String key, String defaultValue) {
        try {
            String value = saConfigMapper.getCfgValueByCfgKey(key);
            return StringUtils.isNotBlank(value) ? value : defaultValue;
        } catch (Exception e) {
            PrintColor.red("[配置获取异常] key: " + key + ", 使用默认值: " + defaultValue);
            return defaultValue;
        }
    }

    // 刷新阿里云配置
    public synchronized void refreshAliyunConfig() {
        this.aliyunBucket = getConfigValue("system.oss.aliyun.bucket", "未配置aliyun bucket");
        this.aliyunAccessKeyId = getConfigValue("system.oss.aliyun.accesskeyid", "未配置aliyun accesskeyid");
        this.aliyunAccessKeySecret = getConfigValue("system.oss.aliyun.accesskeysecret", "未配置aliyun accesskeysecret");
        this.aliyunEndpoint = getConfigValue("system.oss.aliyun.endpoint", "1.2.3.4");
        this.aliyunUrlprefix = getConfigValue("system.oss.aliyun.urlprefix", "1.2.3.4");

        PrintColor.lv("[阿里云配置刷新] bucket: " + aliyunBucket + ", endpoint: " + aliyunEndpoint);
    }

    // 刷新MinIO配置
    public synchronized void refreshMinioConfig() {
        this.minioBucket = getConfigValue("system.oss.minio.bucket", "未配置minio bucket");
        this.minioAccessKey = getConfigValue("system.oss.minio.accesskey", "未配置minio accesskey");
        this.minioSecretKey = getConfigValue("system.oss.minio.accesssecret", "未配置minio accesssecret");
        this.minioEndpoint = getConfigValue("system.oss.minio.endpoint", "1.2.3.4");
        this.minioUrlprefix = getConfigValue("system.oss.minio.urlprefix", "1.2.3.4");

        PrintColor.lv("[MinIO配置刷新] bucket: " + minioBucket + ", endpoint: " + minioEndpoint);
    }

    // 激活的OSS类型
    public String getOssType() {
        return ossType;
    }

    // MinIO Getters
    public String getMinioBucket() {
        return minioBucket;
    }

    public String getMinioAccessKey() {
        return minioAccessKey;
    }

    public String getMinioSecretKey() {
        return minioSecretKey;
    }

    public String getMinioEndpoint() {
        return minioEndpoint;
    }

    public String getMinioUrlprefix() {
        return minioUrlprefix;
    }

    // 阿里云 Getters
    public String getAliyunBucket() {
        return aliyunBucket;
    }

    public String getAliyunAccessKeyId() {
        return aliyunAccessKeyId;
    }

    public String getAliyunAccessKeySecret() {
        return aliyunAccessKeySecret;
    }

    public String getAliyunEndpoint() {
        return aliyunEndpoint;
    }

    public String getAliyunUrlprefix() {
        return aliyunUrlprefix;
    }
}