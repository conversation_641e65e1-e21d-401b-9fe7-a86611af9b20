package inks.sa.common.core.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限编码(SaPermcode)实体类
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:35
 */
public class SaPermcodePojo implements Serializable {
    private static final long serialVersionUID = -56710935958775330L;
    // Id
    @Excel(name = "Id")
    private String permid;
    // 父级主键
    @Excel(name = "父级主键")
    private String parentid;
    // 权限类型
    @Excel(name = "权限类型")
    private String permtype;
    // 权限编码
    @Excel(name = "权限编码")
    private String permcode;
    // 权限名称
    @Excel(name = "权限名称")
    private String permname;
    // 排列序号
    @Excel(name = "排列序号")
    private Integer rownum;
    // 是否公开
    @Excel(name = "是否公开")
    private Integer ispublic;
    // 是否有效
    @Excel(name = "是否有效")
    private Integer enabledmark;
    // 允许删除
    @Excel(name = "允许删除")
    private Integer allowdelete;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // Id
    public String getPermid() {
        return permid;
    }

    public void setPermid(String permid) {
        this.permid = permid;
    }

    // 父级主键
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 权限类型
    public String getPermtype() {
        return permtype;
    }

    public void setPermtype(String permtype) {
        this.permtype = permtype;
    }

    // 权限编码
    public String getPermcode() {
        return permcode;
    }

    public void setPermcode(String permcode) {
        this.permcode = permcode;
    }

    // 权限名称
    public String getPermname() {
        return permname;
    }

    public void setPermname(String permname) {
        this.permname = permname;
    }

    // 排列序号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 是否公开
    public Integer getIspublic() {
        return ispublic;
    }

    public void setIspublic(Integer ispublic) {
        this.ispublic = ispublic;
    }

    // 是否有效
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 允许删除
    public Integer getAllowdelete() {
        return allowdelete;
    }

    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

