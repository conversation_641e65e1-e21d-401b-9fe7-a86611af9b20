package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaRolemenuappPojo;

import java.util.List;

/**
 * 角色菜单App(SaRolemenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
public interface SaRolemenuappService {


    SaRolemenuappPojo getEntity(String key);

    PageInfo<SaRolemenuappPojo> getPageList(QueryParam queryParam);

    SaRolemenuappPojo insert(SaRolemenuappPojo saRolemenuappPojo);

    SaRolemenuappPojo update(SaRolemenuappPojo saRolemenuapppojo);

    int delete(String key);

    Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser);
}
