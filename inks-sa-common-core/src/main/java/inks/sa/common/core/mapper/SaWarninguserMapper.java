package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaWarninguserPojo;
import inks.sa.common.core.domain.SaWarninguserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预警用户(SaWarninguser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-20 11:16:37
 */
@Mapper
public interface SaWarninguserMapper {


    SaWarninguserPojo getEntity(@Param("key") String key);

    List<SaWarninguserPojo> getPageList(QueryParam queryParam);

    int insert(SaWarninguserEntity saWarninguserEntity);

    int update(SaWarninguserEntity saWarninguserEntity);

    int delete(@Param("key") String key);

    List<SaWarninguserPojo> getListByUser(String userid);
}

