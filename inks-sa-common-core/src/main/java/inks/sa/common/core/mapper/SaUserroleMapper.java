package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaUserroleEntity;
import inks.sa.common.core.domain.pojo.SaConfigPojo;
import inks.sa.common.core.domain.pojo.SaPermcodePojo;
import inks.sa.common.core.domain.pojo.SaUserrolePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联(SaUserrole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:42:27
 */
@Mapper
public interface SaUserroleMapper {


    SaUserrolePojo getEntity(@Param("key") String key);


    List<SaUserrolePojo> getPageList(QueryParam queryParam);

    int insert(SaUserroleEntity saUserroleEntity);

    int update(SaUserroleEntity saUserroleEntity);


    int delete(@Param("key") String key);

    List<SaUserrolePojo> getListByRole(String key);

    List<SaUserrolePojo> getListByUser(@Param("key") String key);

    List<SaPermcodePojo> getPermByUser(@Param("key") String key);

    // resourceid就是roleid
    List<String> getUseridsByRoleid(@Param("resourceid") String resourceid);

    List<SaConfigPojo> getConfigListAll();

    List<SaConfigPojo> getConfigListByUserid(String userid);
}

