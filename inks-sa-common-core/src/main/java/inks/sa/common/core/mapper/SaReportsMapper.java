package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaReportsEntity;
import inks.sa.common.core.domain.pojo.SaReportsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表中心(SaReports)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
@Mapper
public interface SaReportsMapper {


    SaReportsPojo getEntity(@Param("key") String key);


    List<SaReportsPojo> getPageList(QueryParam queryParam);


    int insert(SaReportsEntity saReportsEntity);


    int update(SaReportsEntity saReportsEntity);


    int delete(@Param("key") String key);

    List<SaReportsPojo> getListByDef(@Param("moduleCode") String moduleCode);

    SaReportsPojo getEntityByNameCode(@Param("rptname") String rptname, @Param("moduleCode") String moduleCode);


    List<SaReportsPojo> getPageListAll(QueryParam queryParam);

    List<SaReportsPojo> getListByModuleCode(@Param("moduleCode") String moduleCode);

}

