package inks.sa.common.core.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.pojo.SaConfigPojo;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * (Sa_Config)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-30 16:36:58
 */

@RequestMapping("/SaConfig")
@Api(tags = "通用:系统配置")
@RestController
public class A_SaConfigController {

    private final static Logger logger = LoggerFactory.getLogger(A_SaConfigController.class);


    @Resource
    private SaConfigService saConfigService;


    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaConfigPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Config.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Cfglevel=3";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saConfigService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取系统参数详细信息", notes = "获取系统参数详细信息", produces = "application/json")
    @RequestMapping(value = "/getConfigValue", method = RequestMethod.GET)
    public R<String> getConfigValue(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saConfigService.getConfigValue(key, loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增系统参数 self=1查个人配置，0查通用配置（默认0）", notes = "新增系统参数", produces = "application/json")
    @RequestMapping(value = "/setConfig", method = RequestMethod.POST)
    public R<SaConfigPojo> setConfig(@RequestBody String json, @RequestParam(defaultValue = "0") int self) {
        try {
            SaConfigPojo saConfigPojo = JSONArray.parseObject(json, SaConfigPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            SaConfigPojo dbPojo;
            if (self == 1) {
                dbPojo = this.saConfigService.getEntityByKeyUser(saConfigPojo.getCfgkey(), loginUser.getUserid());
            } else {
                dbPojo = this.saConfigService.getEntityByCfgKey(saConfigPojo.getCfgkey());
            }
////            // 清除 redis缓存
//            this.saRedisService.delete(CacheConstants.USER_CONFIG_KEY + "_UI_" + loginUser.getTenantid() + "_" + loginUser.getUserid().substring(0, 8));
            if (dbPojo == null) {
                saConfigPojo.setCfglevel(3);
                saConfigPojo.setUserid(loginUser.getUserid());
                saConfigPojo.setCreateby(loginUser.getRealname());   // 创建者
                saConfigPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                saConfigPojo.setCreatedate(new Date());   // 创建时间
                saConfigPojo.setLister(loginUser.getRealname());   // 制表
                saConfigPojo.setListerid(loginUser.getUserid());    // 制表id
                saConfigPojo.setAllowui(1); // 前端参数
                saConfigPojo.setModifydate(new Date());   //修改时间
                saConfigPojo.setTenantid(loginUser.getTenantid());   //租户id
//                saConfigPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                return R.ok(this.saConfigService.insert(saConfigPojo));
            } else {
                SaConfigPojo setPojo = new SaConfigPojo();
                setPojo.setId(dbPojo.getId());
                setPojo.setCfgvalue(saConfigPojo.getCfgvalue());
                setPojo.setTenantid(loginUser.getTenantid());
                setPojo.setLister(loginUser.getRealname());   // 制表
                setPojo.setListerid(loginUser.getUserid());    // 制表id
                setPojo.setModifydate(new Date());   //修改时间
                return R.ok(this.saConfigService.update(setPojo));
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取详细信息", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaConfigPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saConfigService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
//    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    public R<PageInfo<SaConfigPojo>> getPageList(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
//                queryParam.setOrderBy("Sa_Config.CreateDate");
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser();
//            String qpfilter = "";
//            qpfilter += queryParam.getAllFilter();
//            queryParam.setFilterstr(qpfilter);
//            return R.ok(this.saConfigService.getPageList(queryParam));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


    @ApiOperation(value = " 新增", notes = "新增", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaConfigPojo> create(@RequestBody String json) {
        try {
            SaConfigPojo saConfigPojo = JSONArray.parseObject(json, SaConfigPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saConfigPojo.setCreateby(loginUser.getRealName());   // 创建者
            saConfigPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saConfigPojo.setCreatedate(new Date());   // 创建时间
            saConfigPojo.setLister(loginUser.getRealname());   // 制表
            saConfigPojo.setListerid(loginUser.getUserid());    // 制表id  
            saConfigPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saConfigService.insert(saConfigPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改", notes = "修改", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaConfigPojo> update(@RequestBody String json) {
        try {
            SaConfigPojo saConfigPojo = JSONArray.parseObject(json, SaConfigPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            saConfigPojo.setLister(loginUser.getRealname());   // 制表
            saConfigPojo.setListerid(loginUser.getUserid());    // 制表id  
            saConfigPojo.setModifydate(new Date());   //修改时间
//            saConfigPojo.setAssessor(""); // 审核员
//            saConfigPojo.setAssessorid(""); // 审核员id
//            saConfigPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saConfigService.update(saConfigPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除", notes = "删除", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saConfigService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaConfigPojo saConfigPojo = this.saConfigService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saConfigPojo);
        // 加入公司信息
        if (loginUser.getTenantinfo() != null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从Sa_Redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    @ApiOperation(value = " 获取详细信息", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCfgKey", method = RequestMethod.GET)
    public R<SaConfigPojo> getEntityByCfgKey(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser();
            return R.ok(this.saConfigService.getEntityByCfgKey(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

