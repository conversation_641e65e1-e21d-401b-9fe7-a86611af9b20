package inks.sa.common.core.config.oa.aes; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/11
 * @param 回调数据实体类
 */

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "xml")
public class InMsgPojo {
    @XmlElement(name = "AgentID")
    private String agentID;
    /**
     * 经过加密的密文
     */
    @XmlElement(name = "Encrypt")
    private String encrypt;
    /**
     * 发送方帐号
     */
    @XmlElement(name = "ToUserName")
    private String toUserName;

    public String getAgentID() {
        return agentID;
    }

    public void setAgentID(String agentID) {
        this.agentID = agentID;
    }

    public String getEncrypt() {
        return encrypt;
    }

    public void setEncrypt(String encrypt) {
        this.encrypt = encrypt;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }
}
