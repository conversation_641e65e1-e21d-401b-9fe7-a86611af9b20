package inks.sa.common.core.utils.wxe; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/13
 * @param
 */

import com.alibaba.fastjson.JSONObject;

public class QyWeChatUtils {
    public static String refreshToken(String type) {
        String token = "";
        if ("emaiToken".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.mailSecret);
            JSONObject jsonObject = SendRequest.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
            }
        }
        if ("agentToken".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.agentSecret);
            JSONObject jsonObject = SendRequest.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
            }
        }
        if ("approve".equals(type)) {
            String url = QyWeChat.getToken.replace("{corpid}", QyWeChat.corpId).replace("{corpsecret}", QyWeChat.approve);
            JSONObject jsonObject = SendRequest.sendGet(url);
            if (null != jsonObject) {
                token = jsonObject.getString("access_token");
            }
        }
        return token;
    }
}
