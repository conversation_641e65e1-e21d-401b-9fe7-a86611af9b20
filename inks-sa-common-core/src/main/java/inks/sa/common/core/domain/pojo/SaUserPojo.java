package inks.sa.common.core.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户(SaUser)实体类
 *
 * <AUTHOR>
 * @since 2024-08-20 10:23:47
 */
@Data
public class SaUserPojo implements Serializable {
    private static final long serialVersionUID = -86824848213720578L;
    // ID
    @Excel(name = "ID")
    private String id;
     // 用户名
    @Excel(name = "用户名")
    private String username;
    // 真实姓名
    @Excel(name = "真实姓名")
    private String realname;
    // 密码
    @Excel(name = "密码")
    private String password;
    // 手机号
    @Excel(name = "手机号")
    private String phone;
    // 电子邮箱
    @Excel(name = "电子邮箱")
    private String email;
    // 阿里邮箱授权码
    @Excel(name = "阿里邮箱授权码")
    private String emailauthcode;
    // 性别
    @Excel(name = "性别")
    private Integer sex;
    // 邮箱
    @Excel(name = "邮箱")
    private String avatar;
    // 目录名
    @Excel(name = "目录名")
    private String dirname;
    // minio文件名
    @Excel(name = "minio文件名")
    private String filename;
    // 0新注册1技术员
    @Excel(name = "0新注册1技术员")
    private Integer roletype;
    // 是否管理员
    @Excel(name = "是否管理员")
    private Integer adminmark;
    // 用户状态
    @Excel(name = "用户状态")
    private Integer userstate;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建人
    @Excel(name = "创建人")
    private String createby;
    // 创建时间
    @Excel(name = "创建时间")
    private Date createdate;
    // 制表人
    @Excel(name = "制表人")
    private String lister;
    // 修改时间
    @Excel(name = "修改时间")
    private Date modifydate;
    // 锁
    @Excel(name = "锁")
    private Integer revision;

    //               auth_openid.AuthUuid as openid,
    //               auth_wxe.AuthUuid as wxeuserid,
    //               auth_ding.AuthUuid as dinguserid
    private String wxopenid;
    private String wxeuserid;
    private String dinguserid;

}

