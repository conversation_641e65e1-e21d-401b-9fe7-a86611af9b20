package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaScenefieldPojo;

import java.util.List;

/**
 * 场景字段(Sascenefield)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
public interface SaScenefieldService {


    SaScenefieldPojo getEntity(String key);

    PageInfo<SaScenefieldPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param sascenefieldPojo 实例对象
     * @return 实例对象
     */
    SaScenefieldPojo insert(SaScenefieldPojo sascenefieldPojo);

    /**
     * 修改数据
     *
     * @param sascenefieldpojo 实例对象
     * @return 实例对象
     */
    SaScenefieldPojo update(SaScenefieldPojo sascenefieldpojo);

    int delete(String key);


    /**
     * 分页查询
     *
     * @param code 筛选条件
     * @return 查询结果
     */
    List<SaScenefieldPojo> getListByCode(String code);
}
