package inks.sa.common.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.SerialNoPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StringUtils;
import inks.sa.common.core.domain.SaBillcodeEntity;
import inks.sa.common.core.domain.pojo.SaBillcodePojo;
import inks.sa.common.core.mapper.SaBillcodeMapper;
import inks.sa.common.core.service.SaBillcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 单据编码(SaBillcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:46
 */
@Service("saBillcodeService")
public class SaBillcodeServiceImpl implements SaBillcodeService {
    @Resource
    private SaBillcodeMapper sabillcodeMapper;


    @Override
    public SaBillcodePojo getEntity(String key, String tid) {
        return this.sabillcodeMapper.getEntity(key, tid);
    }

    @Override
    public PageInfo<SaBillcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBillcodePojo> lst = sabillcodeMapper.getPageList(queryParam);
            PageInfo<SaBillcodePojo> pageInfo = new PageInfo<SaBillcodePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param sabillcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaBillcodePojo insert(SaBillcodePojo sabillcodePojo) {
        //初始化NULL字段
        if (sabillcodePojo.getModulecode() == null) sabillcodePojo.setModulecode("");
        if (sabillcodePojo.getBillname() == null) sabillcodePojo.setBillname("");
        if (sabillcodePojo.getPrefix1() == null) sabillcodePojo.setPrefix1("");
        if (sabillcodePojo.getSuffix1() == null) sabillcodePojo.setSuffix1("");
        if (sabillcodePojo.getPrefix2() == null) sabillcodePojo.setPrefix2("");
        if (sabillcodePojo.getSuffix2() == null) sabillcodePojo.setSuffix2("");
        if (sabillcodePojo.getPrefix3() == null) sabillcodePojo.setPrefix3("");
        if (sabillcodePojo.getSuffix3() == null) sabillcodePojo.setSuffix3("");
        if (sabillcodePojo.getPrefix4() == null) sabillcodePojo.setPrefix4("");
        if (sabillcodePojo.getSuffix4() == null) sabillcodePojo.setSuffix4("");
        if (sabillcodePojo.getPrefix5() == null) sabillcodePojo.setPrefix5("");
        if (sabillcodePojo.getSuffix5() == null) sabillcodePojo.setSuffix5("");
        if (sabillcodePojo.getCounttype() == null) sabillcodePojo.setCounttype("");
        if (sabillcodePojo.getStep() == null) sabillcodePojo.setStep(0);
        if (sabillcodePojo.getCurrentnum() == null) sabillcodePojo.setCurrentnum(0);
        if (sabillcodePojo.getTablename() == null) sabillcodePojo.setTablename("");
        if (sabillcodePojo.getDatecolumn() == null) sabillcodePojo.setDatecolumn("");
        if (sabillcodePojo.getColumnname() == null) sabillcodePojo.setColumnname("");
        if (sabillcodePojo.getDbfilter() == null) sabillcodePojo.setDbfilter("");
        if (sabillcodePojo.getAllowedit() == null) sabillcodePojo.setAllowedit(0);
        if (sabillcodePojo.getAllowdelete() == null) sabillcodePojo.setAllowdelete(0);
        if (sabillcodePojo.getParam1() == null) sabillcodePojo.setParam1("");
        if (sabillcodePojo.getParam2() == null) sabillcodePojo.setParam2("");
        if (sabillcodePojo.getParam3() == null) sabillcodePojo.setParam3("");
        if (sabillcodePojo.getParam4() == null) sabillcodePojo.setParam4("");
        if (sabillcodePojo.getParam5() == null) sabillcodePojo.setParam5("");
        if (sabillcodePojo.getRemark() == null) sabillcodePojo.setRemark("");
        if (sabillcodePojo.getLister() == null) sabillcodePojo.setLister("");
        if (sabillcodePojo.getCreatedate() == null) sabillcodePojo.setCreatedate(new Date());
        if (sabillcodePojo.getModifydate() == null) sabillcodePojo.setModifydate(new Date());
        if (sabillcodePojo.getTenantid() == null) sabillcodePojo.setTenantid("");
        SaBillcodeEntity sabillcodeEntity = new SaBillcodeEntity();
        BeanUtils.copyProperties(sabillcodePojo, sabillcodeEntity);

        sabillcodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.sabillcodeMapper.insert(sabillcodeEntity);
        return this.getEntity(sabillcodeEntity.getId(), sabillcodeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param sabillcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaBillcodePojo update(SaBillcodePojo sabillcodePojo) {
        SaBillcodeEntity sabillcodeEntity = new SaBillcodeEntity();
        BeanUtils.copyProperties(sabillcodePojo, sabillcodeEntity);
        this.sabillcodeMapper.update(sabillcodeEntity);
        return this.getEntity(sabillcodeEntity.getId(), sabillcodeEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.sabillcodeMapper.delete(key, tid);
    }

    /**
     * 通过功能code生成序号
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public String getSerialNo(String moduleCode, String tid) {
        return generateSerialNo(moduleCode, tid, null, null);
    }

    @Override
    public String getSerialNo(String moduleCode) {
        return generateSerialNo(moduleCode, null, null, null);
    }

    @Override
    public String getSerialNo(String moduleCode, String tid, String tablename_req, String prefix_req) {
        return generateSerialNo(moduleCode, tid, tablename_req, prefix_req);
    }

    @Override
    public String getSerialNo(String moduleCode, String tid, String tablename_req) {
        return generateSerialNo(moduleCode, tid, tablename_req, null);
    }

    private String generateSerialNo(String moduleCode, String tid, String tablename_req, String prefix_req) {
        String serialNo = DateUtils.dateTimeNow();  // YYYYMMDDHHMMSS
        String renfosn;

        // 查询单据编码生成规则
        SaBillcodeEntity sabillcodeEntity = sabillcodeMapper.getEntityByModuleCode(moduleCode, tid);
        if (sabillcodeEntity == null) {
            sabillcodeEntity = sabillcodeMapper.getEntityByModuleCode(moduleCode, null);
        }

        // 如果数据库没有此编码规则，则手动创建一个CibillcodeEntity对象
        // 要求格式： “BD-yyyyMM-[0000] ,以月计数； 排序字段 createdate
        // Bus_Deliery 若prefix_req没有传，则取2个大写BD
        if (sabillcodeEntity == null && StringUtils.isNotBlank(tablename_req)) {
            sabillcodeEntity = new SaBillcodeEntity();
            sabillcodeEntity.setTablename(tablename_req);
            sabillcodeEntity.setColumnname("RefNo");
            sabillcodeEntity.setDatecolumn("CreateDate");
            // 若未传入前缀，则默认取表名前俩大写作为前缀
            if (StringUtils.isBlank(prefix_req)) {
                prefix_req = this.getPrefixByTableName(tablename_req);
            }
            sabillcodeEntity.setPrefix1(prefix_req);
            sabillcodeEntity.setSuffix1("-");
            sabillcodeEntity.setPrefix2("YYYY");
            sabillcodeEntity.setSuffix2("-");
            sabillcodeEntity.setPrefix3("MM");
            sabillcodeEntity.setSuffix3("-");
            sabillcodeEntity.setPrefix4("[0000]");
            sabillcodeEntity.setSuffix4("");
            sabillcodeEntity.setPrefix5("");
            sabillcodeEntity.setSuffix5("");
            sabillcodeEntity.setCounttype("month");
            sabillcodeEntity.setStep(1);
        }

        if (sabillcodeEntity != null) {
            SerialNoPojo serialNoPojo = new SerialNoPojo();
            BeanUtils.copyProperties(sabillcodeEntity, serialNoPojo);
            String prefix = BillCodeUtil.getPrefix(serialNoPojo);

            Date currentDate = new Date();
            Map<String, Object> serialNoMap = sabillcodeMapper.getSerialNo(sabillcodeEntity, prefix.length() + 1, tid, currentDate);
            renfosn = serialNoMap.get("RefNo").toString();
            if (!StringUtils.isNumeric(renfosn)) {
                renfosn = "1";
            }
            serialNo = BillCodeUtil.getSerialNo(serialNoPojo, renfosn);
        }
        return serialNo;
    }


    // 默认拿表名前2个大写字母作为前缀：假设tablename_req是Bus_Deliery，前缀是BD,
    //                            假设tablename_req是Bus_deliery，前缀是BU
    public String getPrefixByTableName(String tablename_req) {
        // 只要2个大写字母，就取前2个
        StringBuilder prefix = new StringBuilder(2);
        for (char ch : tablename_req.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                prefix.append(ch);
                if (prefix.length() == 2) {
                    return prefix.toString();
                }
            }
        }
        // 如果没有找到两个大写字母，提取前两个字符
        return tablename_req.substring(0, Math.min(2, tablename_req.length())).toUpperCase();
    }

    @Override
    public String getNextCode(String tablename, String column) {

        String MaxStr = this.sabillcodeMapper.getMaxCode(tablename, column);

        //如果字段没有创建过值，则默认赋值为：表名前2个大写字母+0001
        if (MaxStr.isEmpty()) {
            String prefixByTableName = getPrefixByTableName(tablename);
            return prefixByTableName + "0001q";//生成的编号从0001开始
        }

        // 如果字段已经有值，则获取最大值，并加1
        return BillCodeUtil.SnLatter(MaxStr, 1);
    }
}
