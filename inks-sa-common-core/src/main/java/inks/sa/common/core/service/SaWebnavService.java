package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaWebnavPojo;

/**
 * Pc导航(SaWebnav)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-02 13:42:49
 */
public interface SaWebnavService {


    SaWebnavPojo getEntity(String key);

    PageInfo<SaWebnavPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saWebnavPojo 实例对象
     * @return 实例对象
     */
    SaWebnavPojo insert(SaWebnavPojo saWebnavPojo);

    /**
     * 修改数据
     *
     * @param saWebnavpojo 实例对象
     * @return 实例对象
     */
    SaWebnavPojo update(SaWebnavPojo saWebnavpojo);

    int delete(String key);
}
