package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaAuthcodeEntity;
import inks.sa.common.core.domain.pojo.SaAuthcodePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 授权码(SaAuthcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-15 09:38:43
 */
@Mapper
public interface SaAuthcodeMapper {


    SaAuthcodePojo getEntity(@Param("key") String key);

    List<SaAuthcodePojo> getPageList(QueryParam queryParam);

    int insert(SaAuthcodeEntity saAuthcodeEntity);

    int update(SaAuthcodeEntity saAuthcodeEntity);

    int delete(@Param("key") String key);

    SaAuthcodePojo getEntityByAuthCode(String authCode);
}

