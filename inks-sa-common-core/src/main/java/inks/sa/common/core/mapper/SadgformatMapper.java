package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SadgformatEntity;
import inks.sa.common.core.domain.pojo.SadgformatPojo;
import inks.sa.common.core.domain.pojo.SadgformatitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 列表格式(Sadgformat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-21 15:16:08
 */
@Mapper
public interface SadgformatMapper {


    SadgformatPojo getEntity(@Param("key") String key, @Param("tid") String tid);


    List<SadgformatitemdetailPojo> getPageList(QueryParam queryParam);


    List<SadgformatPojo> getPageTh(QueryParam queryParam);

    int insert(SadgformatEntity sadgformatEntity);

    int update(SadgformatEntity sadgformatEntity);


    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param sadgformatPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SadgformatPojo sadgformatPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    SadgformatPojo getEntityByCode(@Param("code") String code, @Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    SadgformatPojo getEntityByCodeUser(@Param("code") String code, @Param("userid") String userid, @Param("tid") String tid);

    SadgformatPojo getTenBillEntityByCode(@Param("code") String code, @Param("tid") String tid);
}

