package inks.sa.common.core.config.oss;

import io.minio.*;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Collections;

/**
 * minio 存储
 *
 * <AUTHOR> lhm
 * @version : 1.0
 * @since : 2022-10-10
 */
public class MinioStorage implements Storage {
    private final MinioClient minioClient;

    public MinioStorage(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    @Override
    public void putObject(String bucketName, String dirName, String filename, InputStream inputStream, String contentType, String oriFileName) throws Exception {
        boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!isExist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
        if (StringUtils.isNotBlank(dirName)) {
            filename = dirName + "/" + filename;
        }
        try {
            // 上传前编码
            String metaOriFileName = oriFileName == null ? "" : URLEncoder.encode(oriFileName, "UTF-8");
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filename)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(contentType)
                            .userMetadata(Collections.singletonMap("orifilename", metaOriFileName
                            )) // 重点 存入原文件名
                            .build());
        } catch (Exception e) {
            // 记录日志并重新抛出异常
            throw new Exception("上传文件到Minio失败: " + e.getMessage(), e);
        }
    }

    public void putObject(String bucketName, String objectName, InputStream inputStream, String contentType, String oriFileName) throws Exception {
        boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!isExist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
        try {
            // 上传前编码
            String metaOriFileName = oriFileName == null ? "" : URLEncoder.encode(oriFileName, "UTF-8");
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(contentType)
                            .userMetadata(Collections.singletonMap("orifilename", metaOriFileName)) // 重点 存入原文件名
                            .build());
        } catch (Exception e) {
            // 记录日志并重新抛出异常
            throw new Exception("上传文件到Minio失败: " + e.getMessage(), e);
        }
    }


    @Override
    public void removeObject(String bucketName, String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    @Override
    public InputStream getObject(String bucketName, String objectName) throws Exception {
        return minioClient.getObject(
                GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }
}