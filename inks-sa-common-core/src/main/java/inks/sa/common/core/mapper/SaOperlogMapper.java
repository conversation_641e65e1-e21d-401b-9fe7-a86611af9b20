package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaOperlogEntity;
import inks.sa.common.core.domain.pojo.SaOperlogPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志(SaOperlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-19 14:41:50
 */
@Mapper
public interface SaOperlogMapper {


    SaOperlogPojo getEntity(@Param("key") String key);

    List<SaOperlogPojo> getPageList(QueryParam queryParam);

    int insert(SaOperlogEntity saOperlogEntity);

    int update(SaOperlogEntity saOperlogEntity);

    int delete(@Param("key") String key);

    int deleteByTime(QueryParam queryParam);
}

