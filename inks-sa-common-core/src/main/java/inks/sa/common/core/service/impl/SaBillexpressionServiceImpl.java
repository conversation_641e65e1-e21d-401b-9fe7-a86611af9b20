package inks.sa.common.core.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.domain.pojo.SaBillexpressionPojo;
import inks.sa.common.core.domain.SaBillexpressionEntity;
import inks.sa.common.core.mapper.SaBillexpressionMapper;
import inks.sa.common.core.service.SaBillexpressionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
/**
 * 单据公式(SaBillexpression)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-23 14:40:22
 */
@Service("saBillexpressionService")
public class SaBillexpressionServiceImpl implements SaBillexpressionService {
    @Resource
    private SaBillexpressionMapper saBillexpressionMapper;

    @Override
    public SaBillexpressionPojo getEntity(String key) {
        return this.saBillexpressionMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaBillexpressionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBillexpressionPojo> lst = saBillexpressionMapper.getPageList(queryParam);
            PageInfo<SaBillexpressionPojo> pageInfo = new PageInfo<SaBillexpressionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaBillexpressionPojo insert(SaBillexpressionPojo saBillexpressionPojo) {
        //初始化NULL字段
        cleanNull(saBillexpressionPojo);
        SaBillexpressionEntity saBillexpressionEntity = new SaBillexpressionEntity(); 
        BeanUtils.copyProperties(saBillexpressionPojo,saBillexpressionEntity);
        //生成雪花id
          saBillexpressionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saBillexpressionEntity.setRevision(1);  //乐观锁
          this.saBillexpressionMapper.insert(saBillexpressionEntity);
        return this.getEntity(saBillexpressionEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saBillexpressionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaBillexpressionPojo update(SaBillexpressionPojo saBillexpressionPojo) {
        SaBillexpressionEntity saBillexpressionEntity = new SaBillexpressionEntity(); 
        BeanUtils.copyProperties(saBillexpressionPojo,saBillexpressionEntity);
        this.saBillexpressionMapper.update(saBillexpressionEntity);
        return this.getEntity(saBillexpressionEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saBillexpressionMapper.delete(key) ;
    }

    @Override
    public List<SaBillexpressionPojo> getListByCode(String key) {
        return this.saBillexpressionMapper.getListByCode(key);
    }

    private static void cleanNull(SaBillexpressionPojo saBillexpressionPojo) {
        if(saBillexpressionPojo.getModulecode()==null) saBillexpressionPojo.setModulecode("");
        if(saBillexpressionPojo.getBillname()==null) saBillexpressionPojo.setBillname("");
        if(saBillexpressionPojo.getOrgcolumns()==null) saBillexpressionPojo.setOrgcolumns("");
        if(saBillexpressionPojo.getExprtemp()==null) saBillexpressionPojo.setExprtemp("");
        if(saBillexpressionPojo.getTgcolumn()==null) saBillexpressionPojo.setTgcolumn("");
        if(saBillexpressionPojo.getDecnum()==null) saBillexpressionPojo.setDecnum(0);
        if(saBillexpressionPojo.getReturntype()==null) saBillexpressionPojo.setReturntype(0);
        if(saBillexpressionPojo.getEnabledmark()==null) saBillexpressionPojo.setEnabledmark(0);
        if(saBillexpressionPojo.getRownum()==null) saBillexpressionPojo.setRownum(0);
        if(saBillexpressionPojo.getRemark()==null) saBillexpressionPojo.setRemark("");
        if(saBillexpressionPojo.getCreateby()==null) saBillexpressionPojo.setCreateby("");
        if(saBillexpressionPojo.getCreatebyid()==null) saBillexpressionPojo.setCreatebyid("");
        if(saBillexpressionPojo.getCreatedate()==null) saBillexpressionPojo.setCreatedate(new Date());
        if(saBillexpressionPojo.getLister()==null) saBillexpressionPojo.setLister("");
        if(saBillexpressionPojo.getListerid()==null) saBillexpressionPojo.setListerid("");
        if(saBillexpressionPojo.getModifydate()==null) saBillexpressionPojo.setModifydate(new Date());
        if(saBillexpressionPojo.getCustom1()==null) saBillexpressionPojo.setCustom1("");
        if(saBillexpressionPojo.getCustom2()==null) saBillexpressionPojo.setCustom2("");
        if(saBillexpressionPojo.getCustom3()==null) saBillexpressionPojo.setCustom3("");
        if(saBillexpressionPojo.getCustom4()==null) saBillexpressionPojo.setCustom4("");
        if(saBillexpressionPojo.getCustom5()==null) saBillexpressionPojo.setCustom5("");
        if(saBillexpressionPojo.getTenantid()==null) saBillexpressionPojo.setTenantid("");
        if(saBillexpressionPojo.getTenantname()==null) saBillexpressionPojo.setTenantname("");
        if(saBillexpressionPojo.getRevision()==null) saBillexpressionPojo.setRevision(0);
   }

}
