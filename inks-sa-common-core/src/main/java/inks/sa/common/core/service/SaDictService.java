package inks.sa.common.core.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.SaDictPojo;
import inks.sa.common.core.domain.pojo.SaDictitemdetailPojo;

/**
 * 数据字典(SaDict)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-03 16:10:09
 */
public interface SaDictService {


    SaDictPojo getEntity(String key);

    PageInfo<SaDictitemdetailPojo> getPageList(QueryParam queryParam);


    SaDictPojo getBillEntity(String key);

    PageInfo<SaDictPojo> getBillList(QueryParam queryParam);

    PageInfo<SaDictPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDictPojo 实例对象
     * @return 实例对象
     */
    SaDictPojo insert(SaDictPojo saDictPojo);

    /**
     * 修改数据
     *
     * @param saDictpojo 实例对象
     * @return 实例对象
     */
    SaDictPojo update(SaDictPojo saDictpojo);

    int delete(String key);

    SaDictPojo getBillEntityByDictCode(String key);
}
