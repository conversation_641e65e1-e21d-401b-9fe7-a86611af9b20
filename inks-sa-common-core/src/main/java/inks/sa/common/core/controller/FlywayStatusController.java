package inks.sa.common.core.controller;

import inks.sa.common.core.config.flyway.DataSourceHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Flyway状态页面控制器
 * 提供数据库表统计信息，实现与Flyway进度页面的无缝过渡
 */
@Controller
public class FlywayStatusController {

    @Autowired
    private DataSource dataSource;

    /**
     * 提供/flyway路径的页面，显示当前数据库状态
     */
    @GetMapping("/flyway")
    public String flywayStatusPage() {
        return "flyway-status";
    }

    /**
     * 提供/flyway-progress.html路径的页面（向后兼容）
     */
    @GetMapping("/flyway-progress.html")
    public String flywayProgressPage() {
        return "flyway-status";
    }

    /**
     * 获取数据库表统计信息的API
     */
    @GetMapping("/flyway/api/status")
    @ResponseBody
    public FlywayStatusInfo getFlywayStatus() {
        FlywayStatusInfo status = new FlywayStatusInfo();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            // 获取数据库名称
            String databaseName = connection.getCatalog();
            status.setDatabaseName(databaseName);
            
            // 统计表数量
            int tableCount = 0;
            try (ResultSet tables = metaData.getTables(databaseName, null, "%", new String[]{"TABLE"})) {
                while (tables.next()) {
                    tableCount++;
                }
            }
            status.setTableCount(tableCount);
            
            // 统计视图数量
            int viewCount = 0;
            try (ResultSet views = metaData.getTables(databaseName, null, "%", new String[]{"VIEW"})) {
                while (views.next()) {
                    viewCount++;
                }
            }
            status.setViewCount(viewCount);
            
            // 获取数据库产品信息
            status.setDatabaseProduct(metaData.getDatabaseProductName() + " " + metaData.getDatabaseProductVersion());
            
            // 获取数据库连接信息
            status.setDatabaseUrl(metaData.getURL());
            status.setDriverName(metaData.getDriverName() + " " + metaData.getDriverVersion());
            
            // 获取MySQL特定信息
            if (metaData.getDatabaseProductName().toLowerCase().contains("mysql")) {
                getMySQLSpecificInfo(connection, status);
            }
            
            // 获取数据库初始化状态信息
            String initStatus = DataSourceHelper.getInitializationStatus();
            long initTime = DataSourceHelper.getInitializationTime();
            
            status.setInitializationStatus(initStatus);
            status.setInitializationTime(initTime);
            
            // 根据初始化状态设置显示信息
            if ("首次建库".equals(initStatus)) {
                status.setStatus("首次建库完成");
                status.setMessage(String.format("数据库首次创建完成，耗时 %.2f 秒，共创建 %d 张表", 
                    initTime / 1000.0, tableCount));
            } else if ("已有数据库".equals(initStatus)) {
                status.setStatus("使用已有数据库");
                status.setMessage(String.format("使用已有数据库，跳过初始化，当前共有 %d 张表", tableCount));
            } else {
                status.setStatus("运行中");
                status.setMessage("数据库初始化已完成，应用正常运行");
            }
            
        } catch (SQLException e) {
            status.setStatus("错误");
            status.setMessage("获取数据库信息失败: " + e.getMessage());
            status.setTableCount(0);
        }
        
        return status;
    }

    /**
     * 获取MySQL特定的状态信息
     */
    private void getMySQLSpecificInfo(Connection connection, FlywayStatusInfo status) {
        try {
            // 获取数据库大小
            try (PreparedStatement stmt = connection.prepareStatement(
                "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB' " +
                "FROM information_schema.tables WHERE table_schema = ?")) {
                stmt.setString(1, connection.getCatalog());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        status.setDatabaseSize(rs.getDouble(1));
                    }
                }
            }
            
            // 获取连接数信息
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW STATUS LIKE 'Threads_connected'")) {
                if (rs.next()) {
                    status.setActiveConnections(rs.getInt(2));
                }
            }
            
            // 获取最大连接数
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW VARIABLES LIKE 'max_connections'")) {
                if (rs.next()) {
                    status.setMaxConnections(rs.getInt(2));
                }
            }
            
            // 获取数据库运行时间
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW STATUS LIKE 'Uptime'")) {
                if (rs.next()) {
                    status.setUptime(rs.getLong(2));
                }
            }
            
            // 获取查询统计
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW STATUS LIKE 'Questions'")) {
                if (rs.next()) {
                    status.setTotalQueries(rs.getLong(2));
                }
            }
            
            // 获取慢查询数量
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW STATUS LIKE 'Slow_queries'")) {
                if (rs.next()) {
                    status.setSlowQueries(rs.getLong(2));
                }
            }
            
        } catch (SQLException e) {
            // 如果获取MySQL特定信息失败，不影响主要功能
            System.err.println("获取MySQL特定信息失败: " + e.getMessage());
        }
    }

    /**
     * Flyway状态信息数据类
     */
    public static class FlywayStatusInfo {
        private String databaseName;
        private int tableCount;
        private int viewCount;
        private String databaseProduct;
        private String status;
        private String message;
        private String timestamp;
        private String initializationStatus;
        private long initializationTime;
        private String databaseUrl;
        private String driverName;
        private Double databaseSize;
        private Integer activeConnections;
        private Integer maxConnections;
        private Long uptime;
        private Long totalQueries;
        private Long slowQueries;

        public FlywayStatusInfo() {
            this.timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        }

        // Getters and Setters
        public String getDatabaseName() {
            return databaseName;
        }

        public void setDatabaseName(String databaseName) {
            this.databaseName = databaseName;
        }

        public int getTableCount() {
            return tableCount;
        }

        public void setTableCount(int tableCount) {
            this.tableCount = tableCount;
        }

        public int getViewCount() {
            return viewCount;
        }

        public void setViewCount(int viewCount) {
            this.viewCount = viewCount;
        }

        public String getDatabaseProduct() {
            return databaseProduct;
        }

        public void setDatabaseProduct(String databaseProduct) {
            this.databaseProduct = databaseProduct;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getInitializationStatus() {
            return initializationStatus;
        }

        public void setInitializationStatus(String initializationStatus) {
            this.initializationStatus = initializationStatus;
        }

        public long getInitializationTime() {
            return initializationTime;
        }

        public void setInitializationTime(long initializationTime) {
            this.initializationTime = initializationTime;
        }

        public String getDatabaseUrl() {
            return databaseUrl;
        }

        public void setDatabaseUrl(String databaseUrl) {
            this.databaseUrl = databaseUrl;
        }

        public String getDriverName() {
            return driverName;
        }

        public void setDriverName(String driverName) {
            this.driverName = driverName;
        }

        public Double getDatabaseSize() {
            return databaseSize;
        }

        public void setDatabaseSize(Double databaseSize) {
            this.databaseSize = databaseSize;
        }

        public Integer getActiveConnections() {
            return activeConnections;
        }

        public void setActiveConnections(Integer activeConnections) {
            this.activeConnections = activeConnections;
        }

        public Integer getMaxConnections() {
            return maxConnections;
        }

        public void setMaxConnections(Integer maxConnections) {
            this.maxConnections = maxConnections;
        }

        public Long getUptime() {
            return uptime;
        }

        public void setUptime(Long uptime) {
            this.uptime = uptime;
        }

        public Long getTotalQueries() {
            return totalQueries;
        }

        public void setTotalQueries(Long totalQueries) {
            this.totalQueries = totalQueries;
        }

        public Long getSlowQueries() {
            return slowQueries;
        }

        public void setSlowQueries(Long slowQueries) {
            this.slowQueries = slowQueries;
        }
    }
}
