package inks.sa.common.core.config.oss;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;

import java.io.InputStream;

/**
 * aliyun 存储
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
public class AliYunStorage implements Storage {
    private final OSSClient ossClient;
//    private OSSClient ossClient;

    public AliYunStorage(OSSClient ossClient) {
        this.ossClient = ossClient;
    }

    @Override
    public void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType, String oriFileName) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        if (oriFileName != null) {
            objectMetadata.addUserMetadata("orifilename", oriFileName);
        } else {
            objectMetadata.addUserMetadata("orifilename", "");
        }
        if (dirName != null && !dirName.equals("")) {
            objectName = dirName + "/" + objectName;
        }
        this.ossClient.putObject(bucketName, objectName, inputStream, objectMetadata);
    }


    @Override
    public void putObject(String bucketName, String objectName, InputStream inputStream, String contentType, String oriFileName) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        // 增加原文件名元数据
        objectMetadata.addUserMetadata("orifilename", oriFileName == null ? "" : oriFileName);
        this.ossClient.putObject(bucketName, objectName, inputStream, objectMetadata);
    }

    @Override
    public void removeObject(String bucketName, String objectName) {
        this.ossClient.deleteObject(bucketName, objectName);
    }

    @Override
    public InputStream getObject(String bucketName, String objectName) throws Exception {
        return this.ossClient.getObject(bucketName, objectName).getObjectContent();
    }

}