package inks.sa.common.core.mapper;

import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.SaMenuwebEntity;
import inks.sa.common.core.domain.pojo.SaMenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 后台导航(SaMenuweb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-18 10:51:01
 */
@Mapper
public interface SaMenuwebMapper {


    SaMenuwebPojo getEntity(@Param("key") String key);

    List<SaMenuwebPojo> getPageList(QueryParam queryParam);

    int insert(SaMenuwebEntity saMenuwebEntity);

    int update(SaMenuwebEntity saMenuwebEntity);

    int delete(@Param("key") String key);

    List<SaMenuwebPojo> getListByPid(String key);

    List<SaMenuwebPojo> getListAll();

    List<SaMenuwebPojo> getListByNavids(@Param("navids") List<String> navids);
}

