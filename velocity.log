2025-07-28 16:14:22,613 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2025-07-28 16:14:22,613 - Initializing Velocity, Calling init()...
2025-07-28 16:14:22,614 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:14:22,614 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:14:22,614 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:14:22,614 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:14:22,614 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:14:22,614 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:14:22,621 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:14:22,622 - Do unicode file recognition:  false
2025-07-28 16:14:22,622 - FileResourceLoader : adding path '.'
2025-07-28 16:14:22,636 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:14:22,642 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:14:22,644 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:14:22,645 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:14:22,647 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:14:22,648 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:14:22,649 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:14:22,652 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:14:22,653 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:14:22,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:14:22,676 - Created '20' parsers.
2025-07-28 16:14:22,679 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:14:22,679 - Velocimacro : Default library not found.
2025-07-28 16:14:22,679 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:14:22,679 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:14:22,679 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:14:22,679 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-28 16:15:03,766 - Log4JLogChute initialized using file 'velocity.log'
2025-07-28 16:15:03,767 - Initializing Velocity, Calling init()...
2025-07-28 16:15:03,768 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 16:15:03,768 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 16:15:03,768 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 16:15:03,768 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 16:15:03,768 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:15:03,768 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 16:15:03,773 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 16:15:03,775 - Do unicode file recognition:  false
2025-07-28 16:15:03,775 - FileResourceLoader : adding path '.'
2025-07-28 16:15:03,791 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 16:15:03,794 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 16:15:03,795 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 16:15:03,795 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 16:15:03,796 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 16:15:03,796 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 16:15:03,797 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 16:15:03,798 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 16:15:03,798 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 16:15:03,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 16:15:03,809 - Created '20' parsers.
2025-07-28 16:15:03,811 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 16:15:03,811 - Velocimacro : Default library not found.
2025-07-28 16:15:03,811 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 16:15:03,811 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 16:15:03,811 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 16:15:03,812 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 10:32:38,639 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 10:32:38,641 - Initializing Velocity, Calling init()...
2025-07-29 10:32:38,641 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 10:32:38,641 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 10:32:38,641 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 10:32:38,641 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 10:32:38,641 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:32:38,641 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 10:32:38,644 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 10:32:38,645 - Do unicode file recognition:  false
2025-07-29 10:32:38,645 - FileResourceLoader : adding path '.'
2025-07-29 10:32:38,652 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 10:32:38,655 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 10:32:38,656 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 10:32:38,656 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 10:32:38,657 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 10:32:38,657 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 10:32:38,658 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 10:32:38,658 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 10:32:38,659 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 10:32:38,660 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 10:32:38,672 - Created '20' parsers.
2025-07-29 10:32:38,674 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 10:32:38,674 - Velocimacro : Default library not found.
2025-07-29 10:32:38,674 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 10:32:38,674 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 10:32:38,674 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 10:32:38,674 - Velocimacro : autoload off : VM system will not automatically reload global library macros
